{"logs": [{"outputFile": "top.yogiczy.mytv.tv-mergeDisguisedDebugResources-85:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\cdd23b539dbc00d913771dd5e581033e\\transformed\\foundation-release\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,147,234", "endColumns": "91,86,88", "endOffsets": "142,229,318"}, "to": {"startLines": "50,262,263", "startColumns": "4,4,4", "startOffsets": "3657,22690,22777", "endColumns": "91,86,88", "endOffsets": "3744,22772,22861"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\75b82da373a07aa04f7fa0a8c467d672\\transformed\\media3-exoplayer-1.8.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,194,261,332,414,496,591,680", "endColumns": "74,63,66,70,81,81,94,88,78", "endOffsets": "125,189,256,327,409,491,586,675,754"}, "to": {"startLines": "97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7775,7850,7914,7981,8052,8134,8216,8311,8400", "endColumns": "74,63,66,70,81,81,94,88,78", "endOffsets": "7845,7909,7976,8047,8129,8211,8306,8395,8474"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\0c82a76523f617aef246a3faf4397c6a\\transformed\\ui-release\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "198,293,376,473,572,658,737,834,925,1012,1097,1187,1263,1348,1424,1503,1578,1654,1726", "endColumns": "94,82,96,98,85,78,96,90,86,84,89,75,84,75,78,74,75,71,121", "endOffsets": "288,371,468,567,653,732,829,920,1007,1092,1182,1258,1343,1419,1498,1573,1649,1721,1843"}, "to": {"startLines": "66,67,68,69,70,125,126,244,245,247,248,252,254,255,256,257,259,260,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5223,5318,5401,5498,5597,9719,9798,21162,21253,21426,21511,21849,22004,22089,22165,22244,22420,22496,22568", "endColumns": "94,82,96,98,85,78,96,90,86,84,89,75,84,75,78,74,75,71,121", "endOffsets": "5313,5396,5493,5592,5678,9793,9890,21248,21335,21506,21596,21920,22084,22160,22239,22314,22491,22563,22685"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\35f391f48f1163378f0ecf1dfb563ddd\\transformed\\material3-release\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,296,419,539,641,740,856,997,1115,1260,1344,1446,1544,1644,1759,1886,1993,2138,2282,2428,2620,2758,2879,3003,3129,3228,3325,3450,3588,3692,3805,3910,4056,4207,4317,4422,4508,4603,4698,4812,4902,4989,5090,5170,5254,5355,5460,5553,5653,5741,5851,5952,6057,6176,6256,6360", "endColumns": "120,119,122,119,101,98,115,140,117,144,83,101,97,99,114,126,106,144,143,145,191,137,120,123,125,98,96,124,137,103,112,104,145,150,109,104,85,94,94,113,89,86,100,79,83,100,104,92,99,87,109,100,104,118,79,103,95", "endOffsets": "171,291,414,534,636,735,851,992,1110,1255,1339,1441,1539,1639,1754,1881,1988,2133,2277,2423,2615,2753,2874,2998,3124,3223,3320,3445,3583,3687,3800,3905,4051,4202,4312,4417,4503,4598,4693,4807,4897,4984,5085,5165,5249,5350,5455,5548,5648,5736,5846,5947,6052,6171,6251,6355,6451"}, "to": {"startLines": "128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9960,10081,10201,10324,10444,10546,10645,10761,10902,11020,11165,11249,11351,11449,11549,11664,11791,11898,12043,12187,12333,12525,12663,12784,12908,13034,13133,13230,13355,13493,13597,13710,13815,13961,14112,14222,14327,14413,14508,14603,14717,14807,14894,14995,15075,15159,15260,15365,15458,15558,15646,15756,15857,15962,16081,16161,16265", "endColumns": "120,119,122,119,101,98,115,140,117,144,83,101,97,99,114,126,106,144,143,145,191,137,120,123,125,98,96,124,137,103,112,104,145,150,109,104,85,94,94,113,89,86,100,79,83,100,104,92,99,87,109,100,104,118,79,103,95", "endOffsets": "10076,10196,10319,10439,10541,10640,10756,10897,11015,11160,11244,11346,11444,11544,11659,11786,11893,12038,12182,12328,12520,12658,12779,12903,13029,13128,13225,13350,13488,13592,13705,13810,13956,14107,14217,14322,14408,14503,14598,14712,14802,14889,14990,15070,15154,15255,15360,15453,15553,15641,15751,15852,15957,16076,16156,16260,16356"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\07b9e5f639314a52052890932176ce08\\transformed\\media3-ui-1.8.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,471,658,739,822,895,989,1079,1153,1220,1317,1414,1480,1549,1616,1687,1798,1909,2019,2086,2172,2245,2319,2406,2495,2559,2626,2679,2737,2785,2846,2911,2979,3044,3113,3177,3238,3304,3357,3417,3491,3565,3624", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,80,82,72,93,89,73,66,96,96,65,68,66,70,110,110,109,66,85,72,73,86,88,63,66,52,57,47,60,64,67,64,68,63,60,65,52,59,73,73,58,70", "endOffsets": "280,466,653,734,817,890,984,1074,1148,1215,1312,1409,1475,1544,1611,1682,1793,1904,2014,2081,2167,2240,2314,2401,2490,2554,2621,2674,2732,2780,2841,2906,2974,3039,3108,3172,3233,3299,3352,3412,3486,3560,3619,3690"}, "to": {"startLines": "2,11,15,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,566,5807,5888,5971,6044,6138,6228,6302,6369,6466,6563,6629,6698,6765,6836,6947,7058,7168,7235,7321,7394,7468,7555,7644,7708,8479,8532,8590,8638,8699,8764,8832,8897,8966,9030,9091,9157,9210,9270,9344,9418,9477", "endLines": "10,14,18,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "endColumns": "17,12,12,80,82,72,93,89,73,66,96,96,65,68,66,70,110,110,109,66,85,72,73,86,88,63,66,52,57,47,60,64,67,64,68,63,60,65,52,59,73,73,58,70", "endOffsets": "375,561,748,5883,5966,6039,6133,6223,6297,6364,6461,6558,6624,6693,6760,6831,6942,7053,7163,7230,7316,7389,7463,7550,7639,7703,7770,8527,8585,8633,8694,8759,8827,8892,8961,9025,9086,9152,9205,9265,9339,9413,9472,9543"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\e4a290dba5f52ccc9bc02699d6e15cbe\\transformed\\material-release\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "198", "startColumns": "4", "startOffsets": "17355", "endColumns": "88", "endOffsets": "17439"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\71973fdd2f8b1122e619daae98b107aa\\transformed\\appcompat-1.7.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,734,819,899,991,1085,1182,1276,1375,1469,1565,1660,1752,1844,1929,2036,2147,2249,2357,2465,2572,2737,2836", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "208,314,421,510,611,729,814,894,986,1080,1177,1271,1370,1464,1560,1655,1747,1839,1924,2031,2142,2244,2352,2460,2567,2732,2831,2917"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,249", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "926,1034,1140,1247,1336,1437,1555,1640,1720,1812,1906,2003,2097,2196,2290,2386,2481,2573,2665,2750,2857,2968,3070,3178,3286,3393,3558,21601", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "1029,1135,1242,1331,1432,1550,1635,1715,1807,1901,1998,2092,2191,2285,2381,2476,2568,2660,2745,2852,2963,3065,3173,3281,3388,3553,3652,21682"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\c64c27f9b5a3bd14c8d70afc2c5cfe89\\transformed\\core-1.16.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "56,57,58,59,60,61,62,258", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4187,4284,4386,4485,4585,4692,4798,22319", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "4279,4381,4480,4580,4687,4793,4914,22415"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\03c59f01b4d50573fe2a148684529e0c\\transformed\\material-1.12.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,434,516,615,711,814,934,1015,1075,1139,1231,1310,1375,1465,1529,1597,1659,1732,1796,1850,1976,2034,2096,2150,2226,2369,2456,2536,2635,2721,2803,2942,3024,3106,3242,3329,3409,3465,3516,3582,3657,3737,3808,3887,3960,4037,4106,4180,4287,4380,4457,4550,4648,4722,4803,4902,4955,5039,5105,5194,5282,5344,5408,5471,5539,5655,5763,5870,5972,6032,6087,6173,6256,6335", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,79,81,98,95,102,119,80,59,63,91,78,64,89,63,67,61,72,63,53,125,57,61,53,75,142,86,79,98,85,81,138,81,81,135,86,79,55,50,65,74,79,70,78,72,76,68,73,106,92,76,92,97,73,80,98,52,83,65,88,87,61,63,62,67,115,107,106,101,59,54,85,82,78,78", "endOffsets": "268,349,429,511,610,706,809,929,1010,1070,1134,1226,1305,1370,1460,1524,1592,1654,1727,1791,1845,1971,2029,2091,2145,2221,2364,2451,2531,2630,2716,2798,2937,3019,3101,3237,3324,3404,3460,3511,3577,3652,3732,3803,3882,3955,4032,4101,4175,4282,4375,4452,4545,4643,4717,4798,4897,4950,5034,5100,5189,5277,5339,5403,5466,5534,5650,5758,5865,5967,6027,6082,6168,6251,6330,6409"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,71,72,123,124,127,185,186,187,188,189,190,191,192,193,194,195,196,197,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,246,250,251,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "753,3749,3830,3910,3992,4091,4919,5022,5142,5683,5743,9548,9640,9895,16361,16451,16515,16583,16645,16718,16782,16836,16962,17020,17082,17136,17212,17444,17531,17611,17710,17796,17878,18017,18099,18181,18317,18404,18484,18540,18591,18657,18732,18812,18883,18962,19035,19112,19181,19255,19362,19455,19532,19625,19723,19797,19878,19977,20030,20114,20180,20269,20357,20419,20483,20546,20614,20730,20838,20945,21047,21107,21340,21687,21770,21925", "endLines": "22,51,52,53,54,55,63,64,65,71,72,123,124,127,185,186,187,188,189,190,191,192,193,194,195,196,197,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,246,250,251,253", "endColumns": "12,80,79,81,98,95,102,119,80,59,63,91,78,64,89,63,67,61,72,63,53,125,57,61,53,75,142,86,79,98,85,81,138,81,81,135,86,79,55,50,65,74,79,70,78,72,76,68,73,106,92,76,92,97,73,80,98,52,83,65,88,87,61,63,62,67,115,107,106,101,59,54,85,82,78,78", "endOffsets": "921,3825,3905,3987,4086,4182,5017,5137,5218,5738,5802,9635,9714,9955,16446,16510,16578,16640,16713,16777,16831,16957,17015,17077,17131,17207,17350,17526,17606,17705,17791,17873,18012,18094,18176,18312,18399,18479,18535,18586,18652,18727,18807,18878,18957,19030,19107,19176,19250,19357,19450,19527,19620,19718,19792,19873,19972,20025,20109,20175,20264,20352,20414,20478,20541,20609,20725,20833,20940,21042,21102,21157,21421,21765,21844,21999"}}]}]}
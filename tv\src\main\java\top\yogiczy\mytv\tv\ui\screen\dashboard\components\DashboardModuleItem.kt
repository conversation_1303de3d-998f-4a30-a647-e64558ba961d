package top.yogiczy.mytv.tv.ui.screen.dashboard.components

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsFocusedAsState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.FavoriteBorder
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.focus.onFocusEvent
import androidx.compose.ui.platform.LocalDensity
import androidx.tv.material3.Border
import androidx.tv.material3.ClickableSurfaceDefaults
import androidx.tv.material3.Icon
import androidx.tv.material3.MaterialTheme
import androidx.tv.material3.Surface
import androidx.tv.material3.Text
import top.yogiczy.mytv.tv.ui.theme.MyTvTheme
import top.yogiczy.mytv.tv.ui.utils.focusOnLaunched
import top.yogiczy.mytv.tv.ui.utils.gridColumns
import top.yogiczy.mytv.tv.ui.utils.handleKeyEvents

@Composable
fun DashboardModuleItem(
    modifier: Modifier = Modifier,
    imageVector: ImageVector,
    title: String,
    tag: String? = null,
    onSelected: () -> Unit = {},
) {

    val interactionSource = remember { MutableInteractionSource() }
    val isFocused by interactionSource.collectIsFocusedAsState()
    
    val scale by animateFloatAsState(
        targetValue = if (isFocused) 1.05f else 1.0f,
        label = "scale"
    )

    val view = LocalView.current
    val density = LocalDensity.current
    
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center,
    ) {
        Surface(
            modifier = modifier
                .handleKeyEvents(onSelect = onSelected)
                .scale(scale)
                .size(1.5f.gridColumns()).clip(CircleShape)
                .onFocusEvent { focusState ->
                    if (focusState.isFocused) {
                        view.requestRectangleOnScreen(
                            android.graphics.Rect(0, 0, view.width, view.height + with(density) { 30.dp.toPx().toInt() }),
                            true
                        )
                    }
                },
            interactionSource = interactionSource,
            onClick = onSelected,
            colors = ClickableSurfaceDefaults.colors(
                containerColor = MaterialTheme.colorScheme.onSurface.copy(0.1f),
            ),
            border = ClickableSurfaceDefaults.border(
                focusedBorder = Border(BorderStroke(1.dp, MaterialTheme.colorScheme.onSurface)),
            ),
        ) {
            Icon(
                imageVector = imageVector,
                contentDescription = null,
                modifier = Modifier.size(45.dp)
                    .align(Alignment.Center),
                // tint = MaterialTheme.colorScheme.onSurface
            )
            
            // tag 标签
            tag?.let { nnTag ->
                Text(
                    text = nnTag,
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .padding(4.dp)
                        .background(
                            MaterialTheme.colorScheme.primary,
                            MaterialTheme.shapes.small,
                        )
                        .padding(horizontal = 4.dp, vertical = 2.dp),
                    style = MaterialTheme.typography.labelSmall.copy(
                        color = MaterialTheme.colorScheme.onPrimary
                    ),
                )
            }
        }
        Text(
            text = title,
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.colorScheme.onSurface
        )
    }
}

@Preview
@Composable
private fun DashboardModuleItemPreview() {
    MyTvTheme {
        Column(
            modifier = Modifier.padding(20.dp),
            verticalArrangement = Arrangement.spacedBy(20.dp),
        ) {
            DashboardModuleItem(
                imageVector = Icons.Default.FavoriteBorder,
                title = "收藏",
                tag = "BETA",
            )

            DashboardModuleItem(
                modifier = Modifier.focusOnLaunched(),
                imageVector = Icons.Default.FavoriteBorder,
                title = "收藏",
                tag = "BETA",
            )
        }
    }
}
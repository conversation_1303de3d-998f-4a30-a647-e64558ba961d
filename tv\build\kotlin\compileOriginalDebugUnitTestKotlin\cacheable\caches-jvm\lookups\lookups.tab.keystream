  currentTimeMillis java.lang.System  	compareTo kotlin.Long  minus kotlin.Long  plus kotlin.Long  EpgProgramme 	org.junit  System 	org.junit  Test 	org.junit  assertFalse 	org.junit  
assertTrue 	org.junit  assertFalse org.junit.Assert  
assertTrue org.junit.Assert  EpgProgramme 'top.yogiczy.mytv.core.data.entities.epg  endAt 4top.yogiczy.mytv.core.data.entities.epg.EpgProgramme  startAt 4top.yogiczy.mytv.core.data.entities.epg.EpgProgramme  EpgProgramme 0top.yogiczy.mytv.tv.ui.screensold.epg.components  EpgProgrammeItemTest 0top.yogiczy.mytv.tv.ui.screensold.epg.components  System 0top.yogiczy.mytv.tv.ui.screensold.epg.components  Test 0top.yogiczy.mytv.tv.ui.screensold.epg.components  assertFalse 0top.yogiczy.mytv.tv.ui.screensold.epg.components  
assertTrue 0top.yogiczy.mytv.tv.ui.screensold.epg.components  EpgProgramme Etop.yogiczy.mytv.tv.ui.screensold.epg.components.EpgProgrammeItemTest  System Etop.yogiczy.mytv.tv.ui.screensold.epg.components.EpgProgrammeItemTest  assertFalse Etop.yogiczy.mytv.tv.ui.screensold.epg.components.EpgProgrammeItemTest  
assertTrue Etop.yogiczy.mytv.tv.ui.screensold.epg.components.EpgProgrammeItemTest                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      
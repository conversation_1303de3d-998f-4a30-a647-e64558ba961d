package top.yogiczy.mytv.tv.ui.screen.search

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusProperties
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.KeyEventType
import androidx.compose.ui.input.key.key
import androidx.compose.ui.input.key.onKeyEvent
import androidx.compose.ui.input.key.type
import androidx.compose.ui.platform.LocalFocusManager
import androidx.tv.material3.Text
import com.github.promeg.pinyinhelper.Pinyin
import top.yogiczy.mytv.core.data.entities.channel.Channel
import top.yogiczy.mytv.core.data.entities.channel.ChannelGroupList
import top.yogiczy.mytv.core.data.entities.channel.ChannelGroupList.Companion.channelList
import top.yogiczy.mytv.core.data.entities.channel.ChannelList
import top.yogiczy.mytv.core.data.entities.epg.EpgList
import top.yogiczy.mytv.tv.ui.rememberChildPadding
import top.yogiczy.mytv.tv.ui.screen.components.AppScreen
import top.yogiczy.mytv.tv.ui.screen.search.components.SearchKeyboard
import top.yogiczy.mytv.tv.ui.screen.search.components.SearchResult
import top.yogiczy.mytv.tv.ui.theme.MyTvTheme
import top.yogiczy.mytv.tv.ui.tooling.PreviewWithLayoutGrids
import top.yogiczy.mytv.tv.ui.utils.gridColumns
import top.yogiczy.mytv.tv.ui.utils.clearFocusOnKeyboardDismiss
import top.yogiczy.mytv.tv.R
import androidx.compose.ui.res.stringResource
@Composable
fun SearchScreen(
    modifier: Modifier = Modifier,
    channelGroupListProvider: () -> ChannelGroupList = { ChannelGroupList() },
    onChannelSelected: (Channel) -> Unit = {},
    onChannelFavoriteToggle: (Channel) -> Unit = {},
    epgListProvider: () -> EpgList = { EpgList() },
    onBackPressed: () -> Unit = {},
) {
    val childPadding = rememberChildPadding()
    val focusManager = LocalFocusManager.current
    var keyWord by rememberSaveable { mutableStateOf("") }

    val (nextFieldFocus, prevFieldFocus) = remember { FocusRequester.createRefs() }

    AppScreen(
        modifier = modifier,
        focusRequesterProvider = { prevFieldFocus },
        header = {
            Text(stringResource(R.string.ui_dashboard_module_search))
        },
        canBack = true,
        onBackPressed = onBackPressed,
    ) {
        Row(horizontalArrangement = Arrangement.spacedBy(20.dp)) {
            Column(
                modifier = Modifier
                    .padding(start = childPadding.start)
                    .width(4.gridColumns())
                    .weight(1f),
                verticalArrangement = Arrangement.spacedBy(10.dp),
            ) {
                OutlinedTextField(
                    value = keyWord,
                    onValueChange = { keyWord = it },
                    label = { Text(stringResource(R.string.ui_search_keyword_hint)) },
                    modifier = Modifier.width(4.gridColumns())
                        // .clearFocusOnKeyboardDismiss()
                        .focusProperties {
                            previous = prevFieldFocus
                            next = nextFieldFocus
                        },
                    singleLine = true,
                    maxLines = 1,
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedTextColor = Color.White,
                        unfocusedTextColor = Color.White,
                        focusedLabelColor = Color.White,
                        unfocusedLabelColor = Color.White,
                        focusedBorderColor = Color.White,
                        unfocusedBorderColor = Color.White,
                        cursorColor = Color.White
                    ),
                    keyboardOptions = KeyboardOptions(
                        imeAction = ImeAction.Search,
                        showKeyboardOnFocus = true
                    ),
                    keyboardActions = KeyboardActions(
                        onNext = {
                            focusManager.moveFocus(
                                FocusDirection.Next
                            )
                        },
                        onPrevious = {
                            focusManager.moveFocus(
                                FocusDirection.Previous
                            )
                        },
                    )
                )
                SearchKeyboard(
                    modifier = Modifier.width(4.gridColumns()),
                    focusRequesterProvider = { nextFieldFocus },
                    onInput = { keyWord += it },
                    onDelete = { if (keyWord.isNotEmpty()) keyWord = keyWord.dropLast(1) },
                )
            }

            SearchResult(
                modifier = Modifier
                    .weight(2f),
                channelListProvider = {
                    if (keyWord.isEmpty()) {
                        ChannelList()
                    } else {
                        val channelList = channelGroupListProvider().channelList
                        ChannelList(channelList.filter { channel ->
                            val searchKey = keyWord.lowercase()
                            if (searchKey.any { it.code > 127 }) {
                                channel.name.lowercase().contains(searchKey)
                            } else {
                                Pinyin.toPinyin(channel.name, ",")
                                    .split(",")
                                    .joinToString("") { it.firstOrNull()?.lowercase() ?: "" }
                                    .contains(searchKey)
                            }
                        })
                    }
                },
                onChannelSelected = onChannelSelected,
                onChannelFavoriteToggle = onChannelFavoriteToggle,
                epgListProvider = epgListProvider,
            )
        }
    }
}

@Preview(device = "id:Android TV (720p)")
@Composable
private fun SearchScreenPreview() {
    MyTvTheme {
        SearchScreen()
        PreviewWithLayoutGrids { }
    }
}


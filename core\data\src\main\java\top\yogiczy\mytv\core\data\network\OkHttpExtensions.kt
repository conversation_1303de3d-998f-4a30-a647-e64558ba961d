package top.yogiczy.mytv.core.data.network

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import okhttp3.Call
import okhttp3.Callback
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import okhttp3.ResponseBody
import okhttp3.internal.closeQuietly
import java.io.IOException
import kotlin.coroutines.resumeWithException

@OptIn(ExperimentalCoroutinesApi::class)
suspend fun Call.await(): Response = suspendCancellableCoroutine { continuation ->
    enqueue(
        object : Callback {
            override fun onResponse(call: Call, response: Response) {
                continuation.resume(response) {
                    response.closeQuietly()
                }
            }

            override fun onFailure(call: Call, e: IOException) {
                continuation.resumeWithException(e)
            }
        }
    )

    continuation.invokeOnCancellation {
        try {
            cancel()
        } catch (t: Throwable) {
            // Ignore cancel exception
        }
    }
}

suspend fun <T> String.request(
    builder: (Request.Builder) -> Request.Builder = { it -> it },
    action: suspend CoroutineScope.(ResponseBody) -> T,
) = request(builder) { response, _ -> response.body?.let { action(it) } }

suspend fun <T> String.request(
    builder: (Request.Builder) -> Request.Builder = { it -> it },
    action: suspend CoroutineScope.(Response, Request) -> T,
): T {
    val url = this

    return withContext(Dispatchers.IO) {
        val client = OkHttpClient.Builder()
            .sslSocketFactory(
                TrustAllSSLSocketFactory.sslSocketFactory,
                TrustAllSSLSocketFactory.trustManager
            )
            .hostnameVerifier { _, _ -> true }
            .followRedirects(true).build()
        val request = Request.Builder()
            .url(url)
            .let(builder)
            .build()

        val response = client.newCall(request).await()

        if (!response.isSuccessful) throw Exception("${response.code}: ${response.message}")

        action(response, response.request)
    }
}
package top.yogiczy.mytv.core.data.utils

import org.junit.Test
import org.junit.Assert.*
import java.text.SimpleDateFormat
import java.util.*

class ChannelUtilTest {

    @Test
    fun testReplacePlaybackFormat_YmdHMS() {
        // 测试时间：2025年1月19日 14:00:00 到 15:00:00 (UTC)
        val utcFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        utcFormat.timeZone = java.util.TimeZone.getTimeZone("UTC")
        val startTime = utcFormat.parse("2025-01-19 14:00:00")?.time ?: 0L
        val endTime = utcFormat.parse("2025-01-19 15:00:00")?.time ?: 0L
        val nowTime = System.currentTimeMillis()

        val playbackFormat = "?tvdr={utc:YmdHMS}GMT-{utcend:YmdHMS}GMT"

        val result = ChannelUtil.replacePlaybackFormat(playbackFormat, startTime, nowTime, endTime)



        assertNotNull(result)
        assertTrue("结果应该包含正确的开始时间", result!!.contains("20250119140000GMT"))
        assertTrue("结果应该包含正确的结束时间", result.contains("20250119150000GMT"))
        assertEquals("?tvdr=20250119140000GMT-20250119150000GMT", result)
    }

    @Test
    fun testReplacePlaybackFormat_standardFormat() {
        val utcFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        utcFormat.timeZone = java.util.TimeZone.getTimeZone("UTC")
        val startTime = utcFormat.parse("2025-01-19 14:00:00")?.time ?: 0L
        val endTime = utcFormat.parse("2025-01-19 15:00:00")?.time ?: 0L
        val nowTime = System.currentTimeMillis()

        val playbackFormat = "?start={utc:yyyyMMddHHmmss}&end={utcend:yyyyMMddHHmmss}"

        val result = ChannelUtil.replacePlaybackFormat(playbackFormat, startTime, nowTime, endTime)

        assertNotNull(result)
        assertEquals("?start=20250119140000&end=20250119150000", result)
    }

    @Test
    fun testReplacePlaybackFormat_emptyFormat() {
        val result = ChannelUtil.replacePlaybackFormat("", 0L, 0L, 0L)
        assertNull(result)
        
        val result2 = ChannelUtil.replacePlaybackFormat(null, 0L, 0L, 0L)
        assertNull(result2)
    }

    @Test
    fun testReplacePlaybackFormat_invalidFormat() {
        val startTime = System.currentTimeMillis()
        val endTime = startTime + 3600000 // 1小时后
        val nowTime = System.currentTimeMillis()

        val playbackFormat = "?start={utc:invalidformat}&end={utcend:invalidformat}"

        val result = ChannelUtil.replacePlaybackFormat(playbackFormat, startTime, nowTime, endTime)

        assertNotNull(result)
        // 应该使用默认格式 yyyyMMddHHmmss
        assertTrue("应该包含数字时间戳", result!!.matches(Regex(".*\\d{14}.*")))
    }


}

(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))o(r);new MutationObserver(r=>{for(const i of r)if(i.type==="childList")for(const l of i.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&o(l)}).observe(document,{childList:!0,subtree:!0});function n(r){const i={};return r.integrity&&(i.integrity=r.integrity),r.referrerPolicy&&(i.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?i.credentials="include":r.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function o(r){if(r.ep)return;r.ep=!0;const i=n(r);fetch(r.href,i)}})();const Fm="modulepreload",Bm=function(e){return"/remote-configs-en/"+e},qs={},Se=function(t,n,o){let r=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const i=document.querySelector("meta[property=csp-nonce]"),l=(i==null?void 0:i.nonce)||(i==null?void 0:i.getAttribute("nonce"));r=Promise.all(n.map(s=>{if(s=Bm(s),s in qs)return;qs[s]=!0;const a=s.endsWith(".css"),c=a?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${s}"]${c}`))return;const f=document.createElement("link");if(f.rel=a?"stylesheet":Fm,a||(f.as="script",f.crossOrigin=""),f.href=s,l&&f.setAttribute("nonce",l),document.head.appendChild(f),a)return new Promise((u,d)=>{f.addEventListener("load",u),f.addEventListener("error",()=>d(new Error(`Unable to preload CSS for ${s}`)))})}))}return r.then(()=>t()).catch(i=>{const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=i,window.dispatchEvent(l),!l.defaultPrevented)throw i})};/*!
  * shared v9.14.0
  * (c) 2024 kazuya kawaguchi
  * Released under the MIT License.
  */const Bi=typeof window<"u",oo=(e,t=!1)=>t?Symbol.for(e):Symbol(e),Um=(e,t,n)=>jm({l:e,k:t,s:n}),jm=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),dt=e=>typeof e=="number"&&isFinite(e),Hm=e=>Bf(e)==="[object Date]",Ui=e=>Bf(e)==="[object RegExp]",fl=e=>Ae(e)&&Object.keys(e).length===0,Ot=Object.assign;let Js;const ts=()=>Js||(Js=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Qs(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const Wm=Object.prototype.hasOwnProperty;function ji(e,t){return Wm.call(e,t)}const ct=Array.isArray,Ze=e=>typeof e=="function",ae=e=>typeof e=="string",qe=e=>typeof e=="boolean",Ve=e=>e!==null&&typeof e=="object",Gm=e=>Ve(e)&&Ze(e.then)&&Ze(e.catch),Ff=Object.prototype.toString,Bf=e=>Ff.call(e),Ae=e=>{if(!Ve(e))return!1;const t=Object.getPrototypeOf(e);return t===null||t.constructor===Object},Km=e=>e==null?"":ct(e)||Ae(e)&&e.toString===Ff?JSON.stringify(e,null,2):String(e);function Ym(e,t=""){return e.reduce((n,o,r)=>r===0?n+o:n+t+o,"")}function dl(e){let t=e;return()=>++t}function zm(e,t){typeof console<"u"&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const ci=e=>!Ve(e)||ct(e);function Ii(e,t){if(ci(e)||ci(t))throw new Error("Invalid value");const n=[{src:e,des:t}];for(;n.length;){const{src:o,des:r}=n.pop();Object.keys(o).forEach(i=>{ci(o[i])||ci(r[i])?r[i]=o[i]:n.push({src:o[i],des:r[i]})})}}/*!
  * message-compiler v9.14.0
  * (c) 2024 kazuya kawaguchi
  * Released under the MIT License.
  */function Xm(e,t,n){return{line:e,column:t,offset:n}}function Hi(e,t,n){return{start:e,end:t}}const qm=/\{([0-9a-zA-Z]+)\}/g;function Uf(e,...t){return t.length===1&&Jm(t[0])&&(t=t[0]),(!t||!t.hasOwnProperty)&&(t={}),e.replace(qm,(n,o)=>t.hasOwnProperty(o)?t[o]:"")}const jf=Object.assign,Zs=e=>typeof e=="string",Jm=e=>e!==null&&typeof e=="object";function Hf(e,t=""){return e.reduce((n,o,r)=>r===0?n+o:n+t+o,"")}const ns={USE_MODULO_SYNTAX:1,__EXTEND_POINT__:2},Qm={[ns.USE_MODULO_SYNTAX]:"Use modulo before '{{0}}'."};function Zm(e,t,...n){const o=Uf(Qm[e],...n||[]),r={message:String(o),code:e};return t&&(r.location=t),r}const ve={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16,__EXTEND_POINT__:17},eg={[ve.EXPECTED_TOKEN]:"Expected token: '{0}'",[ve.INVALID_TOKEN_IN_PLACEHOLDER]:"Invalid token in placeholder: '{0}'",[ve.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER]:"Unterminated single quote in placeholder",[ve.UNKNOWN_ESCAPE_SEQUENCE]:"Unknown escape sequence: \\{0}",[ve.INVALID_UNICODE_ESCAPE_SEQUENCE]:"Invalid unicode escape sequence: {0}",[ve.UNBALANCED_CLOSING_BRACE]:"Unbalanced closing brace",[ve.UNTERMINATED_CLOSING_BRACE]:"Unterminated closing brace",[ve.EMPTY_PLACEHOLDER]:"Empty placeholder",[ve.NOT_ALLOW_NEST_PLACEHOLDER]:"Not allowed nest placeholder",[ve.INVALID_LINKED_FORMAT]:"Invalid linked format",[ve.MUST_HAVE_MESSAGES_IN_PLURAL]:"Plural must have messages",[ve.UNEXPECTED_EMPTY_LINKED_MODIFIER]:"Unexpected empty linked modifier",[ve.UNEXPECTED_EMPTY_LINKED_KEY]:"Unexpected empty linked key",[ve.UNEXPECTED_LEXICAL_ANALYSIS]:"Unexpected lexical analysis in token: '{0}'",[ve.UNHANDLED_CODEGEN_NODE_TYPE]:"unhandled codegen node type: '{0}'",[ve.UNHANDLED_MINIFIER_NODE_TYPE]:"unhandled mimifier node type: '{0}'"};function sr(e,t,n={}){const{domain:o,messages:r,args:i}=n,l=Uf((r||eg)[e]||"",...i||[]),s=new SyntaxError(String(l));return s.code=e,t&&(s.location=t),s.domain=o,s}function tg(e){throw e}const En=" ",ng="\r",Lt=`
`,og="\u2028",rg="\u2029";function ig(e){const t=e;let n=0,o=1,r=1,i=0;const l=N=>t[N]===ng&&t[N+1]===Lt,s=N=>t[N]===Lt,a=N=>t[N]===rg,c=N=>t[N]===og,f=N=>l(N)||s(N)||a(N)||c(N),u=()=>n,d=()=>o,p=()=>r,h=()=>i,g=N=>l(N)||a(N)||c(N)?Lt:t[N],O=()=>g(n),A=()=>g(n+i);function V(){return i=0,f(n)&&(o++,r=0),l(n)&&n++,n++,r++,t[n]}function v(){return l(n+i)&&i++,i++,t[n+i]}function b(){n=0,o=1,r=1,i=0}function w(N=0){i=N}function S(){const N=n+i;for(;N!==n;)V();i=0}return{index:u,line:d,column:p,peekOffset:h,charAt:g,currentChar:O,currentPeek:A,next:V,peek:v,reset:b,resetPeek:w,skipToPeek:S}}const Nn=void 0,lg=".",ec="'",ag="tokenizer";function sg(e,t={}){const n=t.location!==!1,o=ig(e),r=()=>o.index(),i=()=>Xm(o.line(),o.column(),o.index()),l=i(),s=r(),a={currentType:14,offset:s,startLoc:l,endLoc:l,lastType:14,lastOffset:s,lastStartLoc:l,lastEndLoc:l,braceNest:0,inLinked:!1,text:""},c=()=>a,{onError:f}=t;function u(y,C,R,...Y){const oe=c();if(C.column+=R,C.offset+=R,f){const Z=n?Hi(oe.startLoc,C):null,F=sr(y,Z,{domain:ag,args:Y});f(F)}}function d(y,C,R){y.endLoc=i(),y.currentType=C;const Y={type:C};return n&&(Y.loc=Hi(y.startLoc,y.endLoc)),R!=null&&(Y.value=R),Y}const p=y=>d(y,14);function h(y,C){return y.currentChar()===C?(y.next(),C):(u(ve.EXPECTED_TOKEN,i(),0,C),"")}function g(y){let C="";for(;y.currentPeek()===En||y.currentPeek()===Lt;)C+=y.currentPeek(),y.peek();return C}function O(y){const C=g(y);return y.skipToPeek(),C}function A(y){if(y===Nn)return!1;const C=y.charCodeAt(0);return C>=97&&C<=122||C>=65&&C<=90||C===95}function V(y){if(y===Nn)return!1;const C=y.charCodeAt(0);return C>=48&&C<=57}function v(y,C){const{currentType:R}=C;if(R!==2)return!1;g(y);const Y=A(y.currentPeek());return y.resetPeek(),Y}function b(y,C){const{currentType:R}=C;if(R!==2)return!1;g(y);const Y=y.currentPeek()==="-"?y.peek():y.currentPeek(),oe=V(Y);return y.resetPeek(),oe}function w(y,C){const{currentType:R}=C;if(R!==2)return!1;g(y);const Y=y.currentPeek()===ec;return y.resetPeek(),Y}function S(y,C){const{currentType:R}=C;if(R!==8)return!1;g(y);const Y=y.currentPeek()===".";return y.resetPeek(),Y}function N(y,C){const{currentType:R}=C;if(R!==9)return!1;g(y);const Y=A(y.currentPeek());return y.resetPeek(),Y}function M(y,C){const{currentType:R}=C;if(!(R===8||R===12))return!1;g(y);const Y=y.currentPeek()===":";return y.resetPeek(),Y}function x(y,C){const{currentType:R}=C;if(R!==10)return!1;const Y=()=>{const Z=y.currentPeek();return Z==="{"?A(y.peek()):Z==="@"||Z==="%"||Z==="|"||Z===":"||Z==="."||Z===En||!Z?!1:Z===Lt?(y.peek(),Y()):I(y,!1)},oe=Y();return y.resetPeek(),oe}function L(y){g(y);const C=y.currentPeek()==="|";return y.resetPeek(),C}function D(y){const C=g(y),R=y.currentPeek()==="%"&&y.peek()==="{";return y.resetPeek(),{isModulo:R,hasSpace:C.length>0}}function I(y,C=!0){const R=(oe=!1,Z="",F=!1)=>{const H=y.currentPeek();return H==="{"?Z==="%"?!1:oe:H==="@"||!H?Z==="%"?!0:oe:H==="%"?(y.peek(),R(oe,"%",!0)):H==="|"?Z==="%"||F?!0:!(Z===En||Z===Lt):H===En?(y.peek(),R(!0,En,F)):H===Lt?(y.peek(),R(!0,Lt,F)):!0},Y=R();return C&&y.resetPeek(),Y}function j(y,C){const R=y.currentChar();return R===Nn?Nn:C(R)?(y.next(),R):null}function Q(y){const C=y.charCodeAt(0);return C>=97&&C<=122||C>=65&&C<=90||C>=48&&C<=57||C===95||C===36}function G(y){return j(y,Q)}function U(y){const C=y.charCodeAt(0);return C>=97&&C<=122||C>=65&&C<=90||C>=48&&C<=57||C===95||C===36||C===45}function X(y){return j(y,U)}function W(y){const C=y.charCodeAt(0);return C>=48&&C<=57}function ie(y){return j(y,W)}function ce(y){const C=y.charCodeAt(0);return C>=48&&C<=57||C>=65&&C<=70||C>=97&&C<=102}function ge(y){return j(y,ce)}function Ce(y){let C="",R="";for(;C=ie(y);)R+=C;return R}function Re(y){O(y);const C=y.currentChar();return C!=="%"&&u(ve.EXPECTED_TOKEN,i(),0,C),y.next(),"%"}function Me(y){let C="";for(;;){const R=y.currentChar();if(R==="{"||R==="}"||R==="@"||R==="|"||!R)break;if(R==="%")if(I(y))C+=R,y.next();else break;else if(R===En||R===Lt)if(I(y))C+=R,y.next();else{if(L(y))break;C+=R,y.next()}else C+=R,y.next()}return C}function ze(y){O(y);let C="",R="";for(;C=X(y);)R+=C;return y.currentChar()===Nn&&u(ve.UNTERMINATED_CLOSING_BRACE,i(),0),R}function We(y){O(y);let C="";return y.currentChar()==="-"?(y.next(),C+=`-${Ce(y)}`):C+=Ce(y),y.currentChar()===Nn&&u(ve.UNTERMINATED_CLOSING_BRACE,i(),0),C}function P(y){return y!==ec&&y!==Lt}function q(y){O(y),h(y,"'");let C="",R="";for(;C=j(y,P);)C==="\\"?R+=z(y):R+=C;const Y=y.currentChar();return Y===Lt||Y===Nn?(u(ve.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,i(),0),Y===Lt&&(y.next(),h(y,"'")),R):(h(y,"'"),R)}function z(y){const C=y.currentChar();switch(C){case"\\":case"'":return y.next(),`\\${C}`;case"u":return ee(y,C,4);case"U":return ee(y,C,6);default:return u(ve.UNKNOWN_ESCAPE_SEQUENCE,i(),0,C),""}}function ee(y,C,R){h(y,C);let Y="";for(let oe=0;oe<R;oe++){const Z=ge(y);if(!Z){u(ve.INVALID_UNICODE_ESCAPE_SEQUENCE,i(),0,`\\${C}${Y}${y.currentChar()}`);break}Y+=Z}return`\\${C}${Y}`}function se(y){return y!=="{"&&y!=="}"&&y!==En&&y!==Lt}function ke(y){O(y);let C="",R="";for(;C=j(y,se);)R+=C;return R}function T(y){let C="",R="";for(;C=G(y);)R+=C;return R}function k(y){const C=R=>{const Y=y.currentChar();return Y==="{"||Y==="%"||Y==="@"||Y==="|"||Y==="("||Y===")"||!Y||Y===En?R:(R+=Y,y.next(),C(R))};return C("")}function B(y){O(y);const C=h(y,"|");return O(y),C}function J(y,C){let R=null;switch(y.currentChar()){case"{":return C.braceNest>=1&&u(ve.NOT_ALLOW_NEST_PLACEHOLDER,i(),0),y.next(),R=d(C,2,"{"),O(y),C.braceNest++,R;case"}":return C.braceNest>0&&C.currentType===2&&u(ve.EMPTY_PLACEHOLDER,i(),0),y.next(),R=d(C,3,"}"),C.braceNest--,C.braceNest>0&&O(y),C.inLinked&&C.braceNest===0&&(C.inLinked=!1),R;case"@":return C.braceNest>0&&u(ve.UNTERMINATED_CLOSING_BRACE,i(),0),R=K(y,C)||p(C),C.braceNest=0,R;default:{let oe=!0,Z=!0,F=!0;if(L(y))return C.braceNest>0&&u(ve.UNTERMINATED_CLOSING_BRACE,i(),0),R=d(C,1,B(y)),C.braceNest=0,C.inLinked=!1,R;if(C.braceNest>0&&(C.currentType===5||C.currentType===6||C.currentType===7))return u(ve.UNTERMINATED_CLOSING_BRACE,i(),0),C.braceNest=0,te(y,C);if(oe=v(y,C))return R=d(C,5,ze(y)),O(y),R;if(Z=b(y,C))return R=d(C,6,We(y)),O(y),R;if(F=w(y,C))return R=d(C,7,q(y)),O(y),R;if(!oe&&!Z&&!F)return R=d(C,13,ke(y)),u(ve.INVALID_TOKEN_IN_PLACEHOLDER,i(),0,R.value),O(y),R;break}}return R}function K(y,C){const{currentType:R}=C;let Y=null;const oe=y.currentChar();switch((R===8||R===9||R===12||R===10)&&(oe===Lt||oe===En)&&u(ve.INVALID_LINKED_FORMAT,i(),0),oe){case"@":return y.next(),Y=d(C,8,"@"),C.inLinked=!0,Y;case".":return O(y),y.next(),d(C,9,".");case":":return O(y),y.next(),d(C,10,":");default:return L(y)?(Y=d(C,1,B(y)),C.braceNest=0,C.inLinked=!1,Y):S(y,C)||M(y,C)?(O(y),K(y,C)):N(y,C)?(O(y),d(C,12,T(y))):x(y,C)?(O(y),oe==="{"?J(y,C)||Y:d(C,11,k(y))):(R===8&&u(ve.INVALID_LINKED_FORMAT,i(),0),C.braceNest=0,C.inLinked=!1,te(y,C))}}function te(y,C){let R={type:14};if(C.braceNest>0)return J(y,C)||p(C);if(C.inLinked)return K(y,C)||p(C);switch(y.currentChar()){case"{":return J(y,C)||p(C);case"}":return u(ve.UNBALANCED_CLOSING_BRACE,i(),0),y.next(),d(C,3,"}");case"@":return K(y,C)||p(C);default:{if(L(y))return R=d(C,1,B(y)),C.braceNest=0,C.inLinked=!1,R;const{isModulo:oe,hasSpace:Z}=D(y);if(oe)return Z?d(C,0,Me(y)):d(C,4,Re(y));if(I(y))return d(C,0,Me(y));break}}return R}function re(){const{currentType:y,offset:C,startLoc:R,endLoc:Y}=a;return a.lastType=y,a.lastOffset=C,a.lastStartLoc=R,a.lastEndLoc=Y,a.offset=r(),a.startLoc=i(),o.currentChar()===Nn?d(a,14):te(o,a)}return{nextToken:re,currentOffset:r,currentPosition:i,context:c}}const cg="parser",ug=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function fg(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const o=parseInt(t||n,16);return o<=55295||o>=57344?String.fromCodePoint(o):"�"}}}function dg(e={}){const t=e.location!==!1,{onError:n,onWarn:o}=e;function r(v,b,w,S,...N){const M=v.currentPosition();if(M.offset+=S,M.column+=S,n){const x=t?Hi(w,M):null,L=sr(b,x,{domain:cg,args:N});n(L)}}function i(v,b,w,S,...N){const M=v.currentPosition();if(M.offset+=S,M.column+=S,o){const x=t?Hi(w,M):null;o(Zm(b,x,N))}}function l(v,b,w){const S={type:v};return t&&(S.start=b,S.end=b,S.loc={start:w,end:w}),S}function s(v,b,w,S){t&&(v.end=b,v.loc&&(v.loc.end=w))}function a(v,b){const w=v.context(),S=l(3,w.offset,w.startLoc);return S.value=b,s(S,v.currentOffset(),v.currentPosition()),S}function c(v,b){const w=v.context(),{lastOffset:S,lastStartLoc:N}=w,M=l(5,S,N);return M.index=parseInt(b,10),v.nextToken(),s(M,v.currentOffset(),v.currentPosition()),M}function f(v,b,w){const S=v.context(),{lastOffset:N,lastStartLoc:M}=S,x=l(4,N,M);return x.key=b,w===!0&&(x.modulo=!0),v.nextToken(),s(x,v.currentOffset(),v.currentPosition()),x}function u(v,b){const w=v.context(),{lastOffset:S,lastStartLoc:N}=w,M=l(9,S,N);return M.value=b.replace(ug,fg),v.nextToken(),s(M,v.currentOffset(),v.currentPosition()),M}function d(v){const b=v.nextToken(),w=v.context(),{lastOffset:S,lastStartLoc:N}=w,M=l(8,S,N);return b.type!==12?(r(v,ve.UNEXPECTED_EMPTY_LINKED_MODIFIER,w.lastStartLoc,0),M.value="",s(M,S,N),{nextConsumeToken:b,node:M}):(b.value==null&&r(v,ve.UNEXPECTED_LEXICAL_ANALYSIS,w.lastStartLoc,0,rn(b)),M.value=b.value||"",s(M,v.currentOffset(),v.currentPosition()),{node:M})}function p(v,b){const w=v.context(),S=l(7,w.offset,w.startLoc);return S.value=b,s(S,v.currentOffset(),v.currentPosition()),S}function h(v){const b=v.context(),w=l(6,b.offset,b.startLoc);let S=v.nextToken();if(S.type===9){const N=d(v);w.modifier=N.node,S=N.nextConsumeToken||v.nextToken()}switch(S.type!==10&&r(v,ve.UNEXPECTED_LEXICAL_ANALYSIS,b.lastStartLoc,0,rn(S)),S=v.nextToken(),S.type===2&&(S=v.nextToken()),S.type){case 11:S.value==null&&r(v,ve.UNEXPECTED_LEXICAL_ANALYSIS,b.lastStartLoc,0,rn(S)),w.key=p(v,S.value||"");break;case 5:S.value==null&&r(v,ve.UNEXPECTED_LEXICAL_ANALYSIS,b.lastStartLoc,0,rn(S)),w.key=f(v,S.value||"");break;case 6:S.value==null&&r(v,ve.UNEXPECTED_LEXICAL_ANALYSIS,b.lastStartLoc,0,rn(S)),w.key=c(v,S.value||"");break;case 7:S.value==null&&r(v,ve.UNEXPECTED_LEXICAL_ANALYSIS,b.lastStartLoc,0,rn(S)),w.key=u(v,S.value||"");break;default:{r(v,ve.UNEXPECTED_EMPTY_LINKED_KEY,b.lastStartLoc,0);const N=v.context(),M=l(7,N.offset,N.startLoc);return M.value="",s(M,N.offset,N.startLoc),w.key=M,s(w,N.offset,N.startLoc),{nextConsumeToken:S,node:w}}}return s(w,v.currentOffset(),v.currentPosition()),{node:w}}function g(v){const b=v.context(),w=b.currentType===1?v.currentOffset():b.offset,S=b.currentType===1?b.endLoc:b.startLoc,N=l(2,w,S);N.items=[];let M=null,x=null;do{const I=M||v.nextToken();switch(M=null,I.type){case 0:I.value==null&&r(v,ve.UNEXPECTED_LEXICAL_ANALYSIS,b.lastStartLoc,0,rn(I)),N.items.push(a(v,I.value||""));break;case 6:I.value==null&&r(v,ve.UNEXPECTED_LEXICAL_ANALYSIS,b.lastStartLoc,0,rn(I)),N.items.push(c(v,I.value||""));break;case 4:x=!0;break;case 5:I.value==null&&r(v,ve.UNEXPECTED_LEXICAL_ANALYSIS,b.lastStartLoc,0,rn(I)),N.items.push(f(v,I.value||"",!!x)),x&&(i(v,ns.USE_MODULO_SYNTAX,b.lastStartLoc,0,rn(I)),x=null);break;case 7:I.value==null&&r(v,ve.UNEXPECTED_LEXICAL_ANALYSIS,b.lastStartLoc,0,rn(I)),N.items.push(u(v,I.value||""));break;case 8:{const j=h(v);N.items.push(j.node),M=j.nextConsumeToken||null;break}}}while(b.currentType!==14&&b.currentType!==1);const L=b.currentType===1?b.lastOffset:v.currentOffset(),D=b.currentType===1?b.lastEndLoc:v.currentPosition();return s(N,L,D),N}function O(v,b,w,S){const N=v.context();let M=S.items.length===0;const x=l(1,b,w);x.cases=[],x.cases.push(S);do{const L=g(v);M||(M=L.items.length===0),x.cases.push(L)}while(N.currentType!==14);return M&&r(v,ve.MUST_HAVE_MESSAGES_IN_PLURAL,w,0),s(x,v.currentOffset(),v.currentPosition()),x}function A(v){const b=v.context(),{offset:w,startLoc:S}=b,N=g(v);return b.currentType===14?N:O(v,w,S,N)}function V(v){const b=sg(v,jf({},e)),w=b.context(),S=l(0,w.offset,w.startLoc);return t&&S.loc&&(S.loc.source=v),S.body=A(b),e.onCacheKey&&(S.cacheKey=e.onCacheKey(v)),w.currentType!==14&&r(b,ve.UNEXPECTED_LEXICAL_ANALYSIS,w.lastStartLoc,0,v[w.offset]||""),s(S,b.currentOffset(),b.currentPosition()),S}return{parse:V}}function rn(e){if(e.type===14)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function pg(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:i=>(n.helpers.add(i),i)}}function tc(e,t){for(let n=0;n<e.length;n++)os(e[n],t)}function os(e,t){switch(e.type){case 1:tc(e.cases,t),t.helper("plural");break;case 2:tc(e.items,t);break;case 6:{os(e.key,t),t.helper("linked"),t.helper("type");break}case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named");break}}function hg(e,t={}){const n=pg(e);n.helper("normalize"),e.body&&os(e.body,n);const o=n.context();e.helpers=Array.from(o.helpers)}function mg(e){const t=e.body;return t.type===2?nc(t):t.cases.forEach(n=>nc(n)),e}function nc(e){if(e.items.length===1){const t=e.items[0];(t.type===3||t.type===9)&&(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const o=e.items[n];if(!(o.type===3||o.type===9)||o.value==null)break;t.push(o.value)}if(t.length===e.items.length){e.static=Hf(t);for(let n=0;n<e.items.length;n++){const o=e.items[n];(o.type===3||o.type===9)&&delete o.value}}}}const gg="minifier";function Do(e){switch(e.t=e.type,e.type){case 0:{const t=e;Do(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let o=0;o<n.length;o++)Do(n[o]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let o=0;o<n.length;o++)Do(n[o]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;Do(t.key),t.k=t.key,delete t.key,t.modifier&&(Do(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}default:throw sr(ve.UNHANDLED_MINIFIER_NODE_TYPE,null,{domain:gg,args:[e.type]})}delete e.type}const vg="parser";function _g(e,t){const{sourceMap:n,filename:o,breakLineCode:r,needIndent:i}=t,l=t.location!==!1,s={filename:o,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:r,needIndent:i,indentLevel:0};l&&e.loc&&(s.source=e.loc.source);const a=()=>s;function c(O,A){s.code+=O}function f(O,A=!0){const V=A?r:"";c(i?V+"  ".repeat(O):V)}function u(O=!0){const A=++s.indentLevel;O&&f(A)}function d(O=!0){const A=--s.indentLevel;O&&f(A)}function p(){f(s.indentLevel)}return{context:a,push:c,indent:u,deindent:d,newline:p,helper:O=>`_${O}`,needIndent:()=>s.needIndent}}function bg(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),qo(e,t.key),t.modifier?(e.push(", "),qo(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}function yg(e,t){const{helper:n,needIndent:o}=e;e.push(`${n("normalize")}([`),e.indent(o());const r=t.items.length;for(let i=0;i<r&&(qo(e,t.items[i]),i!==r-1);i++)e.push(", ");e.deindent(o()),e.push("])")}function Eg(e,t){const{helper:n,needIndent:o}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(o());const r=t.cases.length;for(let i=0;i<r&&(qo(e,t.cases[i]),i!==r-1);i++)e.push(", ");e.deindent(o()),e.push("])")}}function Sg(e,t){t.body?qo(e,t.body):e.push("null")}function qo(e,t){const{helper:n}=e;switch(t.type){case 0:Sg(e,t);break;case 1:Eg(e,t);break;case 2:yg(e,t);break;case 6:bg(e,t);break;case 8:e.push(JSON.stringify(t.value),t);break;case 7:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t);break;case 9:e.push(JSON.stringify(t.value),t);break;case 3:e.push(JSON.stringify(t.value),t);break;default:throw sr(ve.UNHANDLED_CODEGEN_NODE_TYPE,null,{domain:vg,args:[t.type]})}}const Cg=(e,t={})=>{const n=Zs(t.mode)?t.mode:"normal",o=Zs(t.filename)?t.filename:"message.intl",r=!!t.sourceMap,i=t.breakLineCode!=null?t.breakLineCode:n==="arrow"?";":`
`,l=t.needIndent?t.needIndent:n!=="arrow",s=e.helpers||[],a=_g(e,{mode:n,filename:o,sourceMap:r,breakLineCode:i,needIndent:l});a.push(n==="normal"?"function __msg__ (ctx) {":"(ctx) => {"),a.indent(l),s.length>0&&(a.push(`const { ${Hf(s.map(u=>`${u}: _${u}`),", ")} } = ctx`),a.newline()),a.push("return "),qo(a,e),a.deindent(l),a.push("}"),delete e.helpers;const{code:c,map:f}=a.context();return{ast:e,code:c,map:f?f.toJSON():void 0}};function Tg(e,t={}){const n=jf({},t),o=!!n.jit,r=!!n.minify,i=n.optimize==null?!0:n.optimize,s=dg(n).parse(e);return o?(i&&mg(s),r&&Do(s),{ast:s,code:""}):(hg(s,n),Cg(s,n))}/*!
  * core-base v9.14.0
  * (c) 2024 kazuya kawaguchi
  * Released under the MIT License.
  */function wg(){typeof __INTLIFY_PROD_DEVTOOLS__!="boolean"&&(ts().__INTLIFY_PROD_DEVTOOLS__=!1)}const ro=[];ro[0]={w:[0],i:[3,0],"[":[4],o:[7]};ro[1]={w:[1],".":[2],"[":[4],o:[7]};ro[2]={w:[2],i:[3,0],0:[3,0]};ro[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]};ro[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]};ro[5]={"'":[4,0],o:8,l:[5,0]};ro[6]={'"':[4,0],o:8,l:[6,0]};const kg=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function Og(e){return kg.test(e)}function Pg(e){const t=e.charCodeAt(0),n=e.charCodeAt(e.length-1);return t===n&&(t===34||t===39)?e.slice(1,-1):e}function Ag(e){if(e==null)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function Ig(e){const t=e.trim();return e.charAt(0)==="0"&&isNaN(parseInt(e))?!1:Og(t)?Pg(t):"*"+t}function xg(e){const t=[];let n=-1,o=0,r=0,i,l,s,a,c,f,u;const d=[];d[0]=()=>{l===void 0?l=s:l+=s},d[1]=()=>{l!==void 0&&(t.push(l),l=void 0)},d[2]=()=>{d[0](),r++},d[3]=()=>{if(r>0)r--,o=4,d[0]();else{if(r=0,l===void 0||(l=Ig(l),l===!1))return!1;d[1]()}};function p(){const h=e[n+1];if(o===5&&h==="'"||o===6&&h==='"')return n++,s="\\"+h,d[0](),!0}for(;o!==null;)if(n++,i=e[n],!(i==="\\"&&p())){if(a=Ag(i),u=ro[o],c=u[a]||u.l||8,c===8||(o=c[0],c[1]!==void 0&&(f=d[c[1]],f&&(s=i,f()===!1))))return;if(o===7)return t}}const oc=new Map;function Lg(e,t){return Ve(e)?e[t]:null}function Dg(e,t){if(!Ve(e))return null;let n=oc.get(t);if(n||(n=xg(t),n&&oc.set(t,n)),!n)return null;const o=n.length;let r=e,i=0;for(;i<o;){const l=r[n[i]];if(l===void 0||Ze(r))return null;r=l,i++}return r}const Rg=e=>e,Ng=e=>"",Mg="text",Vg=e=>e.length===0?"":Ym(e),$g=Km;function rc(e,t){return e=Math.abs(e),t===2?e?e>1?1:0:1:e?Math.min(e,2):0}function Fg(e){const t=dt(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(dt(e.named.count)||dt(e.named.n))?dt(e.named.count)?e.named.count:dt(e.named.n)?e.named.n:t:t}function Bg(e,t){t.count||(t.count=e),t.n||(t.n=e)}function Ug(e={}){const t=e.locale,n=Fg(e),o=Ve(e.pluralRules)&&ae(t)&&Ze(e.pluralRules[t])?e.pluralRules[t]:rc,r=Ve(e.pluralRules)&&ae(t)&&Ze(e.pluralRules[t])?rc:void 0,i=A=>A[o(n,A.length,r)],l=e.list||[],s=A=>l[A],a=e.named||{};dt(e.pluralIndex)&&Bg(n,a);const c=A=>a[A];function f(A){const V=Ze(e.messages)?e.messages(A):Ve(e.messages)?e.messages[A]:!1;return V||(e.parent?e.parent.message(A):Ng)}const u=A=>e.modifiers?e.modifiers[A]:Rg,d=Ae(e.processor)&&Ze(e.processor.normalize)?e.processor.normalize:Vg,p=Ae(e.processor)&&Ze(e.processor.interpolate)?e.processor.interpolate:$g,h=Ae(e.processor)&&ae(e.processor.type)?e.processor.type:Mg,O={list:s,named:c,plural:i,linked:(A,...V)=>{const[v,b]=V;let w="text",S="";V.length===1?Ve(v)?(S=v.modifier||S,w=v.type||w):ae(v)&&(S=v||S):V.length===2&&(ae(v)&&(S=v||S),ae(b)&&(w=b||w));const N=f(A)(O),M=w==="vnode"&&ct(N)&&S?N[0]:N;return S?u(S)(M,w):M},message:f,type:h,interpolate:p,normalize:d,values:Ot({},l,a)};return O}let Fr=null;function jg(e){Fr=e}function Hg(e,t,n){Fr&&Fr.emit("i18n:init",{timestamp:Date.now(),i18n:e,version:t,meta:n})}const Wg=Gg("function:translate");function Gg(e){return t=>Fr&&Fr.emit(e,t)}const Wf=ns.__EXTEND_POINT__,fo=dl(Wf),Kg={NOT_FOUND_KEY:Wf,FALLBACK_TO_TRANSLATE:fo(),CANNOT_FORMAT_NUMBER:fo(),FALLBACK_TO_NUMBER_FORMAT:fo(),CANNOT_FORMAT_DATE:fo(),FALLBACK_TO_DATE_FORMAT:fo(),EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER:fo(),__EXTEND_POINT__:fo()},Gf=ve.__EXTEND_POINT__,po=dl(Gf),_n={INVALID_ARGUMENT:Gf,INVALID_DATE_ARGUMENT:po(),INVALID_ISO_DATE_ARGUMENT:po(),NOT_SUPPORT_NON_STRING_MESSAGE:po(),NOT_SUPPORT_LOCALE_PROMISE_VALUE:po(),NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:po(),NOT_SUPPORT_LOCALE_TYPE:po(),__EXTEND_POINT__:po()};function Pn(e){return sr(e,null,void 0)}function rs(e,t){return t.locale!=null?ic(t.locale):ic(e.locale)}let Rl;function ic(e){if(ae(e))return e;if(Ze(e)){if(e.resolvedOnce&&Rl!=null)return Rl;if(e.constructor.name==="Function"){const t=e();if(Gm(t))throw Pn(_n.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return Rl=t}else throw Pn(_n.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}else throw Pn(_n.NOT_SUPPORT_LOCALE_TYPE)}function Yg(e,t,n){return[...new Set([n,...ct(t)?t:Ve(t)?Object.keys(t):ae(t)?[t]:[n]])]}function Kf(e,t,n){const o=ae(n)?n:Wi,r=e;r.__localeChainCache||(r.__localeChainCache=new Map);let i=r.__localeChainCache.get(o);if(!i){i=[];let l=[n];for(;ct(l);)l=lc(i,l,t);const s=ct(t)||!Ae(t)?t:t.default?t.default:null;l=ae(s)?[s]:s,ct(l)&&lc(i,l,!1),r.__localeChainCache.set(o,i)}return i}function lc(e,t,n){let o=!0;for(let r=0;r<t.length&&qe(o);r++){const i=t[r];ae(i)&&(o=zg(e,t[r],n))}return o}function zg(e,t,n){let o;const r=t.split("-");do{const i=r.join("-");o=Xg(e,i,n),r.splice(-1,1)}while(r.length&&o===!0);return o}function Xg(e,t,n){let o=!1;if(!e.includes(t)&&(o=!0,t)){o=t[t.length-1]!=="!";const r=t.replace(/!/g,"");e.push(r),(ct(n)||Ae(n))&&n[r]&&(o=n[r])}return o}const qg="9.14.0",pl=-1,Wi="en-US",ac="",sc=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;function Jg(){return{upper:(e,t)=>t==="text"&&ae(e)?e.toUpperCase():t==="vnode"&&Ve(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>t==="text"&&ae(e)?e.toLowerCase():t==="vnode"&&Ve(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>t==="text"&&ae(e)?sc(e):t==="vnode"&&Ve(e)&&"__v_isVNode"in e?sc(e.children):e}}let Yf;function Qg(e){Yf=e}let zf;function Zg(e){zf=e}let Xf;function ev(e){Xf=e}let qf=null;const tv=e=>{qf=e},nv=()=>qf;let Jf=null;const cc=e=>{Jf=e},ov=()=>Jf;let uc=0;function rv(e={}){const t=Ze(e.onWarn)?e.onWarn:zm,n=ae(e.version)?e.version:qg,o=ae(e.locale)||Ze(e.locale)?e.locale:Wi,r=Ze(o)?Wi:o,i=ct(e.fallbackLocale)||Ae(e.fallbackLocale)||ae(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:r,l=Ae(e.messages)?e.messages:{[r]:{}},s=Ae(e.datetimeFormats)?e.datetimeFormats:{[r]:{}},a=Ae(e.numberFormats)?e.numberFormats:{[r]:{}},c=Ot({},e.modifiers||{},Jg()),f=e.pluralRules||{},u=Ze(e.missing)?e.missing:null,d=qe(e.missingWarn)||Ui(e.missingWarn)?e.missingWarn:!0,p=qe(e.fallbackWarn)||Ui(e.fallbackWarn)?e.fallbackWarn:!0,h=!!e.fallbackFormat,g=!!e.unresolving,O=Ze(e.postTranslation)?e.postTranslation:null,A=Ae(e.processor)?e.processor:null,V=qe(e.warnHtmlMessage)?e.warnHtmlMessage:!0,v=!!e.escapeParameter,b=Ze(e.messageCompiler)?e.messageCompiler:Yf,w=Ze(e.messageResolver)?e.messageResolver:zf||Lg,S=Ze(e.localeFallbacker)?e.localeFallbacker:Xf||Yg,N=Ve(e.fallbackContext)?e.fallbackContext:void 0,M=e,x=Ve(M.__datetimeFormatters)?M.__datetimeFormatters:new Map,L=Ve(M.__numberFormatters)?M.__numberFormatters:new Map,D=Ve(M.__meta)?M.__meta:{};uc++;const I={version:n,cid:uc,locale:o,fallbackLocale:i,messages:l,modifiers:c,pluralRules:f,missing:u,missingWarn:d,fallbackWarn:p,fallbackFormat:h,unresolving:g,postTranslation:O,processor:A,warnHtmlMessage:V,escapeParameter:v,messageCompiler:b,messageResolver:w,localeFallbacker:S,fallbackContext:N,onWarn:t,__meta:D};return I.datetimeFormats=s,I.numberFormats=a,I.__datetimeFormatters=x,I.__numberFormatters=L,__INTLIFY_PROD_DEVTOOLS__&&Hg(I,n,D),I}function is(e,t,n,o,r){const{missing:i,onWarn:l}=e;if(i!==null){const s=i(e,n,t,r);return ae(s)?s:t}else return t}function hr(e,t,n){const o=e;o.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}function iv(e,t){return e===t?!1:e.split("-")[0]===t.split("-")[0]}function lv(e,t){const n=t.indexOf(e);if(n===-1)return!1;for(let o=n+1;o<t.length;o++)if(iv(e,t[o]))return!0;return!1}function Nl(e){return n=>av(n,e)}function av(e,t){const n=t.b||t.body;if((n.t||n.type)===1){const o=n,r=o.c||o.cases;return e.plural(r.reduce((i,l)=>[...i,fc(e,l)],[]))}else return fc(e,n)}function fc(e,t){const n=t.s||t.static;if(n)return e.type==="text"?n:e.normalize([n]);{const o=(t.i||t.items).reduce((r,i)=>[...r,va(e,i)],[]);return e.normalize(o)}}function va(e,t){const n=t.t||t.type;switch(n){case 3:{const o=t;return o.v||o.value}case 9:{const o=t;return o.v||o.value}case 4:{const o=t;return e.interpolate(e.named(o.k||o.key))}case 5:{const o=t;return e.interpolate(e.list(o.i!=null?o.i:o.index))}case 6:{const o=t,r=o.m||o.modifier;return e.linked(va(e,o.k||o.key),r?va(e,r):void 0,e.type)}case 7:{const o=t;return o.v||o.value}case 8:{const o=t;return o.v||o.value}default:throw new Error(`unhandled node type on format message part: ${n}`)}}const sv=e=>e;let ui=Object.create(null);const Jo=e=>Ve(e)&&(e.t===0||e.type===0)&&("b"in e||"body"in e);function cv(e,t={}){let n=!1;const o=t.onError||tg;return t.onError=r=>{n=!0,o(r)},{...Tg(e,t),detectError:n}}function uv(e,t){if(ae(e)){qe(t.warnHtmlMessage)&&t.warnHtmlMessage;const o=(t.onCacheKey||sv)(e),r=ui[o];if(r)return r;const{ast:i,detectError:l}=cv(e,{...t,location:!1,jit:!0}),s=Nl(i);return l?s:ui[o]=s}else{const n=e.cacheKey;if(n){const o=ui[n];return o||(ui[n]=Nl(e))}else return Nl(e)}}const dc=()=>"",Qt=e=>Ze(e);function pc(e,...t){const{fallbackFormat:n,postTranslation:o,unresolving:r,messageCompiler:i,fallbackLocale:l,messages:s}=e,[a,c]=_a(...t),f=qe(c.missingWarn)?c.missingWarn:e.missingWarn,u=qe(c.fallbackWarn)?c.fallbackWarn:e.fallbackWarn,d=qe(c.escapeParameter)?c.escapeParameter:e.escapeParameter,p=!!c.resolvedMessage,h=ae(c.default)||qe(c.default)?qe(c.default)?i?a:()=>a:c.default:n?i?a:()=>a:"",g=n||h!=="",O=rs(e,c);d&&fv(c);let[A,V,v]=p?[a,O,s[O]||{}]:Qf(e,a,O,l,u,f),b=A,w=a;if(!p&&!(ae(b)||Jo(b)||Qt(b))&&g&&(b=h,w=b),!p&&(!(ae(b)||Jo(b)||Qt(b))||!ae(V)))return r?pl:a;let S=!1;const N=()=>{S=!0},M=Qt(b)?b:Zf(e,a,V,b,w,N);if(S)return b;const x=hv(e,V,v,c),L=Ug(x),D=dv(e,M,L),I=o?o(D,a):D;if(__INTLIFY_PROD_DEVTOOLS__){const j={timestamp:Date.now(),key:ae(a)?a:Qt(b)?b.key:"",locale:V||(Qt(b)?b.locale:""),format:ae(b)?b:Qt(b)?b.source:"",message:I};j.meta=Ot({},e.__meta,nv()||{}),Wg(j)}return I}function fv(e){ct(e.list)?e.list=e.list.map(t=>ae(t)?Qs(t):t):Ve(e.named)&&Object.keys(e.named).forEach(t=>{ae(e.named[t])&&(e.named[t]=Qs(e.named[t]))})}function Qf(e,t,n,o,r,i){const{messages:l,onWarn:s,messageResolver:a,localeFallbacker:c}=e,f=c(e,o,n);let u={},d,p=null;const h="translate";for(let g=0;g<f.length&&(d=f[g],u=l[d]||{},(p=a(u,t))===null&&(p=u[t]),!(ae(p)||Jo(p)||Qt(p)));g++)if(!lv(d,f)){const O=is(e,t,d,i,h);O!==t&&(p=O)}return[p,d,u]}function Zf(e,t,n,o,r,i){const{messageCompiler:l,warnHtmlMessage:s}=e;if(Qt(o)){const c=o;return c.locale=c.locale||n,c.key=c.key||t,c}if(l==null){const c=()=>o;return c.locale=n,c.key=t,c}const a=l(o,pv(e,n,r,o,s,i));return a.locale=n,a.key=t,a.source=o,a}function dv(e,t,n){return t(n)}function _a(...e){const[t,n,o]=e,r={};if(!ae(t)&&!dt(t)&&!Qt(t)&&!Jo(t))throw Pn(_n.INVALID_ARGUMENT);const i=dt(t)?String(t):(Qt(t),t);return dt(n)?r.plural=n:ae(n)?r.default=n:Ae(n)&&!fl(n)?r.named=n:ct(n)&&(r.list=n),dt(o)?r.plural=o:ae(o)?r.default=o:Ae(o)&&Ot(r,o),[i,r]}function pv(e,t,n,o,r,i){return{locale:t,key:n,warnHtmlMessage:r,onError:l=>{throw i&&i(l),l},onCacheKey:l=>Um(t,n,l)}}function hv(e,t,n,o){const{modifiers:r,pluralRules:i,messageResolver:l,fallbackLocale:s,fallbackWarn:a,missingWarn:c,fallbackContext:f}=e,d={locale:t,modifiers:r,pluralRules:i,messages:p=>{let h=l(n,p);if(h==null&&f){const[,,g]=Qf(f,p,t,s,a,c);h=l(g,p)}if(ae(h)||Jo(h)){let g=!1;const A=Zf(e,p,t,h,p,()=>{g=!0});return g?dc:A}else return Qt(h)?h:dc}};return e.processor&&(d.processor=e.processor),o.list&&(d.list=o.list),o.named&&(d.named=o.named),dt(o.plural)&&(d.pluralIndex=o.plural),d}function hc(e,...t){const{datetimeFormats:n,unresolving:o,fallbackLocale:r,onWarn:i,localeFallbacker:l}=e,{__datetimeFormatters:s}=e,[a,c,f,u]=ba(...t),d=qe(f.missingWarn)?f.missingWarn:e.missingWarn;qe(f.fallbackWarn)?f.fallbackWarn:e.fallbackWarn;const p=!!f.part,h=rs(e,f),g=l(e,r,h);if(!ae(a)||a==="")return new Intl.DateTimeFormat(h,u).format(c);let O={},A,V=null;const v="datetime format";for(let S=0;S<g.length&&(A=g[S],O=n[A]||{},V=O[a],!Ae(V));S++)is(e,a,A,d,v);if(!Ae(V)||!ae(A))return o?pl:a;let b=`${A}__${a}`;fl(u)||(b=`${b}__${JSON.stringify(u)}`);let w=s.get(b);return w||(w=new Intl.DateTimeFormat(A,Ot({},V,u)),s.set(b,w)),p?w.formatToParts(c):w.format(c)}const ed=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function ba(...e){const[t,n,o,r]=e,i={};let l={},s;if(ae(t)){const a=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!a)throw Pn(_n.INVALID_ISO_DATE_ARGUMENT);const c=a[3]?a[3].trim().startsWith("T")?`${a[1].trim()}${a[3].trim()}`:`${a[1].trim()}T${a[3].trim()}`:a[1].trim();s=new Date(c);try{s.toISOString()}catch{throw Pn(_n.INVALID_ISO_DATE_ARGUMENT)}}else if(Hm(t)){if(isNaN(t.getTime()))throw Pn(_n.INVALID_DATE_ARGUMENT);s=t}else if(dt(t))s=t;else throw Pn(_n.INVALID_ARGUMENT);return ae(n)?i.key=n:Ae(n)&&Object.keys(n).forEach(a=>{ed.includes(a)?l[a]=n[a]:i[a]=n[a]}),ae(o)?i.locale=o:Ae(o)&&(l=o),Ae(r)&&(l=r),[i.key||"",s,i,l]}function mc(e,t,n){const o=e;for(const r in n){const i=`${t}__${r}`;o.__datetimeFormatters.has(i)&&o.__datetimeFormatters.delete(i)}}function gc(e,...t){const{numberFormats:n,unresolving:o,fallbackLocale:r,onWarn:i,localeFallbacker:l}=e,{__numberFormatters:s}=e,[a,c,f,u]=ya(...t),d=qe(f.missingWarn)?f.missingWarn:e.missingWarn;qe(f.fallbackWarn)?f.fallbackWarn:e.fallbackWarn;const p=!!f.part,h=rs(e,f),g=l(e,r,h);if(!ae(a)||a==="")return new Intl.NumberFormat(h,u).format(c);let O={},A,V=null;const v="number format";for(let S=0;S<g.length&&(A=g[S],O=n[A]||{},V=O[a],!Ae(V));S++)is(e,a,A,d,v);if(!Ae(V)||!ae(A))return o?pl:a;let b=`${A}__${a}`;fl(u)||(b=`${b}__${JSON.stringify(u)}`);let w=s.get(b);return w||(w=new Intl.NumberFormat(A,Ot({},V,u)),s.set(b,w)),p?w.formatToParts(c):w.format(c)}const td=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function ya(...e){const[t,n,o,r]=e,i={};let l={};if(!dt(t))throw Pn(_n.INVALID_ARGUMENT);const s=t;return ae(n)?i.key=n:Ae(n)&&Object.keys(n).forEach(a=>{td.includes(a)?l[a]=n[a]:i[a]=n[a]}),ae(o)?i.locale=o:Ae(o)&&(l=o),Ae(r)&&(l=r),[i.key||"",s,i,l]}function vc(e,t,n){const o=e;for(const r in n){const i=`${t}__${r}`;o.__numberFormatters.has(i)&&o.__numberFormatters.delete(i)}}wg();/**
* @vue/shared v3.4.38
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function ls(e,t){const n=new Set(e.split(","));return o=>n.has(o)}const Ye={},Uo=[],Zt=()=>{},mv=()=>!1,Xr=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),as=e=>e.startsWith("onUpdate:"),mt=Object.assign,ss=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},gv=Object.prototype.hasOwnProperty,xe=(e,t)=>gv.call(e,t),me=Array.isArray,jo=e=>hl(e)==="[object Map]",nd=e=>hl(e)==="[object Set]",Ee=e=>typeof e=="function",nt=e=>typeof e=="string",io=e=>typeof e=="symbol",He=e=>e!==null&&typeof e=="object",od=e=>(He(e)||Ee(e))&&Ee(e.then)&&Ee(e.catch),rd=Object.prototype.toString,hl=e=>rd.call(e),vv=e=>hl(e).slice(8,-1),id=e=>hl(e)==="[object Object]",cs=e=>nt(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Ho=ls(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),ml=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},_v=/-(\w)/g,cn=ml(e=>e.replace(_v,(t,n)=>n?n.toUpperCase():"")),bv=/\B([A-Z])/g,lo=ml(e=>e.replace(bv,"-$1").toLowerCase()),gl=ml(e=>e.charAt(0).toUpperCase()+e.slice(1)),Ml=ml(e=>e?`on${gl(e)}`:""),to=(e,t)=>!Object.is(e,t),Vl=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},ld=(e,t,n,o=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:o,value:n})},yv=e=>{const t=parseFloat(e);return isNaN(t)?e:t},ad=e=>{const t=nt(e)?Number(e):NaN;return isNaN(t)?e:t};let _c;const sd=()=>_c||(_c=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function us(e){if(me(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],r=nt(o)?Tv(o):us(o);if(r)for(const i in r)t[i]=r[i]}return t}else if(nt(e)||He(e))return e}const Ev=/;(?![^(]*\))/g,Sv=/:([^]+)/,Cv=/\/\*[^]*?\*\//g;function Tv(e){const t={};return e.replace(Cv,"").split(Ev).forEach(n=>{if(n){const o=n.split(Sv);o.length>1&&(t[o[0].trim()]=o[1].trim())}}),t}function fs(e){let t="";if(nt(e))t=e;else if(me(e))for(let n=0;n<e.length;n++){const o=fs(e[n]);o&&(t+=o+" ")}else if(He(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const wv="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",kv=ls(wv);function cd(e){return!!e||e===""}const ud=e=>!!(e&&e.__v_isRef===!0),xn=e=>nt(e)?e:e==null?"":me(e)||He(e)&&(e.toString===rd||!Ee(e.toString))?ud(e)?xn(e.value):JSON.stringify(e,fd,2):String(e),fd=(e,t)=>ud(t)?fd(e,t.value):jo(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[o,r],i)=>(n[$l(o,i)+" =>"]=r,n),{})}:nd(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>$l(n))}:io(t)?$l(t):He(t)&&!me(t)&&!id(t)?String(t):t,$l=(e,t="")=>{var n;return io(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.4.38
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Ht;class dd{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this.parent=Ht,!t&&Ht&&(this.index=(Ht.scopes||(Ht.scopes=[])).push(this)-1)}get active(){return this._active}run(t){if(this._active){const n=Ht;try{return Ht=this,t()}finally{Ht=n}}}on(){Ht=this}off(){Ht=this.parent}stop(t){if(this._active){let n,o;for(n=0,o=this.effects.length;n<o;n++)this.effects[n].stop();for(n=0,o=this.cleanups.length;n<o;n++)this.cleanups[n]();if(this.scopes)for(n=0,o=this.scopes.length;n<o;n++)this.scopes[n].stop(!0);if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0,this._active=!1}}}function pd(e){return new dd(e)}function Ov(e,t=Ht){t&&t.active&&t.effects.push(e)}function hd(){return Ht}function Pv(e){Ht&&Ht.cleanups.push(e)}let Eo;class ds{constructor(t,n,o,r){this.fn=t,this.trigger=n,this.scheduler=o,this.active=!0,this.deps=[],this._dirtyLevel=4,this._trackId=0,this._runnings=0,this._shouldSchedule=!1,this._depsLength=0,Ov(this,r)}get dirty(){if(this._dirtyLevel===2||this._dirtyLevel===3){this._dirtyLevel=1,ao();for(let t=0;t<this._depsLength;t++){const n=this.deps[t];if(n.computed&&(Av(n.computed),this._dirtyLevel>=4))break}this._dirtyLevel===1&&(this._dirtyLevel=0),so()}return this._dirtyLevel>=4}set dirty(t){this._dirtyLevel=t?4:0}run(){if(this._dirtyLevel=0,!this.active)return this.fn();let t=qn,n=Eo;try{return qn=!0,Eo=this,this._runnings++,bc(this),this.fn()}finally{yc(this),this._runnings--,Eo=n,qn=t}}stop(){this.active&&(bc(this),yc(this),this.onStop&&this.onStop(),this.active=!1)}}function Av(e){return e.value}function bc(e){e._trackId++,e._depsLength=0}function yc(e){if(e.deps.length>e._depsLength){for(let t=e._depsLength;t<e.deps.length;t++)md(e.deps[t],e);e.deps.length=e._depsLength}}function md(e,t){const n=e.get(t);n!==void 0&&t._trackId!==n&&(e.delete(t),e.size===0&&e.cleanup())}let qn=!0,Ea=0;const gd=[];function ao(){gd.push(qn),qn=!1}function so(){const e=gd.pop();qn=e===void 0?!0:e}function ps(){Ea++}function hs(){for(Ea--;!Ea&&Sa.length;)Sa.shift()()}function vd(e,t,n){if(t.get(e)!==e._trackId){t.set(e,e._trackId);const o=e.deps[e._depsLength];o!==t?(o&&md(o,e),e.deps[e._depsLength++]=t):e._depsLength++}}const Sa=[];function _d(e,t,n){ps();for(const o of e.keys()){let r;o._dirtyLevel<t&&(r??(r=e.get(o)===o._trackId))&&(o._shouldSchedule||(o._shouldSchedule=o._dirtyLevel===0),o._dirtyLevel=t),o._shouldSchedule&&(r??(r=e.get(o)===o._trackId))&&(o.trigger(),(!o._runnings||o.allowRecurse)&&o._dirtyLevel!==2&&(o._shouldSchedule=!1,o.scheduler&&Sa.push(o.scheduler)))}hs()}const bd=(e,t)=>{const n=new Map;return n.cleanup=e,n.computed=t,n},Gi=new WeakMap,So=Symbol(""),Ca=Symbol("");function $t(e,t,n){if(qn&&Eo){let o=Gi.get(e);o||Gi.set(e,o=new Map);let r=o.get(n);r||o.set(n,r=bd(()=>o.delete(n))),vd(Eo,r)}}function An(e,t,n,o,r,i){const l=Gi.get(e);if(!l)return;let s=[];if(t==="clear")s=[...l.values()];else if(n==="length"&&me(e)){const a=Number(o);l.forEach((c,f)=>{(f==="length"||!io(f)&&f>=a)&&s.push(c)})}else switch(n!==void 0&&s.push(l.get(n)),t){case"add":me(e)?cs(n)&&s.push(l.get("length")):(s.push(l.get(So)),jo(e)&&s.push(l.get(Ca)));break;case"delete":me(e)||(s.push(l.get(So)),jo(e)&&s.push(l.get(Ca)));break;case"set":jo(e)&&s.push(l.get(So));break}ps();for(const a of s)a&&_d(a,4);hs()}function Iv(e,t){const n=Gi.get(e);return n&&n.get(t)}const xv=ls("__proto__,__v_isRef,__isVue"),yd=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(io)),Ec=Lv();function Lv(){const e={};return["includes","indexOf","lastIndexOf"].forEach(t=>{e[t]=function(...n){const o=Le(this);for(let i=0,l=this.length;i<l;i++)$t(o,"get",i+"");const r=o[t](...n);return r===-1||r===!1?o[t](...n.map(Le)):r}}),["push","pop","shift","unshift","splice"].forEach(t=>{e[t]=function(...n){ao(),ps();const o=Le(this)[t].apply(this,n);return hs(),so(),o}}),e}function Dv(e){io(e)||(e=String(e));const t=Le(this);return $t(t,"has",e),t.hasOwnProperty(e)}class Ed{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,o){const r=this._isReadonly,i=this._isShallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return i;if(n==="__v_raw")return o===(r?i?Kv:wd:i?Td:Cd).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(o)?t:void 0;const l=me(t);if(!r){if(l&&xe(Ec,n))return Reflect.get(Ec,n,o);if(n==="hasOwnProperty")return Dv}const s=Reflect.get(t,n,o);return(io(n)?yd.has(n):xv(n))||(r||$t(t,"get",n),i)?s:Ge(s)?l&&cs(n)?s:s.value:He(s)?r?qr(s):un(s):s}}class Sd extends Ed{constructor(t=!1){super(!1,t)}set(t,n,o,r){let i=t[n];if(!this._isShallow){const a=To(i);if(!Qo(o)&&!To(o)&&(i=Le(i),o=Le(o)),!me(t)&&Ge(i)&&!Ge(o))return a?!1:(i.value=o,!0)}const l=me(t)&&cs(n)?Number(n)<t.length:xe(t,n),s=Reflect.set(t,n,o,r);return t===Le(r)&&(l?to(o,i)&&An(t,"set",n,o):An(t,"add",n,o)),s}deleteProperty(t,n){const o=xe(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&o&&An(t,"delete",n,void 0),r}has(t,n){const o=Reflect.has(t,n);return(!io(n)||!yd.has(n))&&$t(t,"has",n),o}ownKeys(t){return $t(t,"iterate",me(t)?"length":So),Reflect.ownKeys(t)}}class Rv extends Ed{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Nv=new Sd,Mv=new Rv,Vv=new Sd(!0);const ms=e=>e,vl=e=>Reflect.getPrototypeOf(e);function fi(e,t,n=!1,o=!1){e=e.__v_raw;const r=Le(e),i=Le(t);n||(to(t,i)&&$t(r,"get",t),$t(r,"get",i));const{has:l}=vl(r),s=o?ms:n?_s:Br;if(l.call(r,t))return s(e.get(t));if(l.call(r,i))return s(e.get(i));e!==r&&e.get(t)}function di(e,t=!1){const n=this.__v_raw,o=Le(n),r=Le(e);return t||(to(e,r)&&$t(o,"has",e),$t(o,"has",r)),e===r?n.has(e):n.has(e)||n.has(r)}function pi(e,t=!1){return e=e.__v_raw,!t&&$t(Le(e),"iterate",So),Reflect.get(e,"size",e)}function Sc(e,t=!1){!t&&!Qo(e)&&!To(e)&&(e=Le(e));const n=Le(this);return vl(n).has.call(n,e)||(n.add(e),An(n,"add",e,e)),this}function Cc(e,t,n=!1){!n&&!Qo(t)&&!To(t)&&(t=Le(t));const o=Le(this),{has:r,get:i}=vl(o);let l=r.call(o,e);l||(e=Le(e),l=r.call(o,e));const s=i.call(o,e);return o.set(e,t),l?to(t,s)&&An(o,"set",e,t):An(o,"add",e,t),this}function Tc(e){const t=Le(this),{has:n,get:o}=vl(t);let r=n.call(t,e);r||(e=Le(e),r=n.call(t,e)),o&&o.call(t,e);const i=t.delete(e);return r&&An(t,"delete",e,void 0),i}function wc(){const e=Le(this),t=e.size!==0,n=e.clear();return t&&An(e,"clear",void 0,void 0),n}function hi(e,t){return function(o,r){const i=this,l=i.__v_raw,s=Le(l),a=t?ms:e?_s:Br;return!e&&$t(s,"iterate",So),l.forEach((c,f)=>o.call(r,a(c),a(f),i))}}function mi(e,t,n){return function(...o){const r=this.__v_raw,i=Le(r),l=jo(i),s=e==="entries"||e===Symbol.iterator&&l,a=e==="keys"&&l,c=r[e](...o),f=n?ms:t?_s:Br;return!t&&$t(i,"iterate",a?Ca:So),{next(){const{value:u,done:d}=c.next();return d?{value:u,done:d}:{value:s?[f(u[0]),f(u[1])]:f(u),done:d}},[Symbol.iterator](){return this}}}}function Mn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function $v(){const e={get(i){return fi(this,i)},get size(){return pi(this)},has:di,add:Sc,set:Cc,delete:Tc,clear:wc,forEach:hi(!1,!1)},t={get(i){return fi(this,i,!1,!0)},get size(){return pi(this)},has:di,add(i){return Sc.call(this,i,!0)},set(i,l){return Cc.call(this,i,l,!0)},delete:Tc,clear:wc,forEach:hi(!1,!0)},n={get(i){return fi(this,i,!0)},get size(){return pi(this,!0)},has(i){return di.call(this,i,!0)},add:Mn("add"),set:Mn("set"),delete:Mn("delete"),clear:Mn("clear"),forEach:hi(!0,!1)},o={get(i){return fi(this,i,!0,!0)},get size(){return pi(this,!0)},has(i){return di.call(this,i,!0)},add:Mn("add"),set:Mn("set"),delete:Mn("delete"),clear:Mn("clear"),forEach:hi(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(i=>{e[i]=mi(i,!1,!1),n[i]=mi(i,!0,!1),t[i]=mi(i,!1,!0),o[i]=mi(i,!0,!0)}),[e,n,t,o]}const[Fv,Bv,Uv,jv]=$v();function gs(e,t){const n=t?e?jv:Uv:e?Bv:Fv;return(o,r,i)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?o:Reflect.get(xe(n,r)&&r in o?n:o,r,i)}const Hv={get:gs(!1,!1)},Wv={get:gs(!1,!0)},Gv={get:gs(!0,!1)};const Cd=new WeakMap,Td=new WeakMap,wd=new WeakMap,Kv=new WeakMap;function Yv(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function zv(e){return e.__v_skip||!Object.isExtensible(e)?0:Yv(vv(e))}function un(e){return To(e)?e:vs(e,!1,Nv,Hv,Cd)}function kd(e){return vs(e,!1,Vv,Wv,Td)}function qr(e){return vs(e,!0,Mv,Gv,wd)}function vs(e,t,n,o,r){if(!He(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=r.get(e);if(i)return i;const l=zv(e);if(l===0)return e;const s=new Proxy(e,l===2?o:n);return r.set(e,s),s}function Wo(e){return To(e)?Wo(e.__v_raw):!!(e&&e.__v_isReactive)}function To(e){return!!(e&&e.__v_isReadonly)}function Qo(e){return!!(e&&e.__v_isShallow)}function Od(e){return e?!!e.__v_raw:!1}function Le(e){const t=e&&e.__v_raw;return t?Le(t):e}function Pd(e){return Object.isExtensible(e)&&ld(e,"__v_skip",!0),e}const Br=e=>He(e)?un(e):e,_s=e=>He(e)?qr(e):e;class Ad{constructor(t,n,o,r){this.getter=t,this._setter=n,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this.effect=new ds(()=>t(this._value),()=>wr(this,this.effect._dirtyLevel===2?2:3)),this.effect.computed=this,this.effect.active=this._cacheable=!r,this.__v_isReadonly=o}get value(){const t=Le(this);return(!t._cacheable||t.effect.dirty)&&to(t._value,t._value=t.effect.run())&&wr(t,4),bs(t),t.effect._dirtyLevel>=2&&wr(t,2),t._value}set value(t){this._setter(t)}get _dirty(){return this.effect.dirty}set _dirty(t){this.effect.dirty=t}}function Xv(e,t,n=!1){let o,r;const i=Ee(e);return i?(o=e,r=Zt):(o=e.get,r=e.set),new Ad(o,r,i||!r,n)}function bs(e){var t;qn&&Eo&&(e=Le(e),vd(Eo,(t=e.dep)!=null?t:e.dep=bd(()=>e.dep=void 0,e instanceof Ad?e:void 0)))}function wr(e,t=4,n,o){e=Le(e);const r=e.dep;r&&_d(r,t)}function Ge(e){return!!(e&&e.__v_isRef===!0)}function fe(e){return Id(e,!1)}function ys(e){return Id(e,!0)}function Id(e,t){return Ge(e)?e:new qv(e,t)}class qv{constructor(t,n){this.__v_isShallow=n,this.dep=void 0,this.__v_isRef=!0,this._rawValue=n?t:Le(t),this._value=n?t:Br(t)}get value(){return bs(this),this._value}set value(t){const n=this.__v_isShallow||Qo(t)||To(t);t=n?t:Le(t),to(t,this._rawValue)&&(this._rawValue,this._rawValue=t,this._value=n?t:Br(t),wr(this,4))}}function E(e){return Ge(e)?e.value:e}const Jv={get:(e,t,n)=>E(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const r=e[t];return Ge(r)&&!Ge(n)?(r.value=n,!0):Reflect.set(e,t,n,o)}};function xd(e){return Wo(e)?e:new Proxy(e,Jv)}class Qv{constructor(t){this.dep=void 0,this.__v_isRef=!0;const{get:n,set:o}=t(()=>bs(this),()=>wr(this));this._get=n,this._set=o}get value(){return this._get()}set value(t){this._set(t)}}function Zv(e){return new Qv(e)}function e_(e){const t=me(e)?new Array(e.length):{};for(const n in e)t[n]=Ld(e,n);return t}class t_{constructor(t,n,o){this._object=t,this._key=n,this._defaultValue=o,this.__v_isRef=!0}get value(){const t=this._object[this._key];return t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Iv(Le(this._object),this._key)}}class n_{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0}get value(){return this._getter()}}function o_(e,t,n){return Ge(e)?e:Ee(e)?new n_(e):He(e)&&arguments.length>1?Ld(e,t,n):fe(e)}function Ld(e,t,n){const o=e[t];return Ge(o)?o:new t_(e,t,n)}/**
* @vue/runtime-core v3.4.38
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Jn(e,t,n,o){try{return o?e(...o):e()}catch(r){Jr(r,t,n)}}function en(e,t,n,o){if(Ee(e)){const r=Jn(e,t,n,o);return r&&od(r)&&r.catch(i=>{Jr(i,t,n)}),r}if(me(e)){const r=[];for(let i=0;i<e.length;i++)r.push(en(e[i],t,n,o));return r}}function Jr(e,t,n,o=!0){const r=t?t.vnode:null;if(t){let i=t.parent;const l=t.proxy,s=`https://vuejs.org/error-reference/#runtime-${n}`;for(;i;){const c=i.ec;if(c){for(let f=0;f<c.length;f++)if(c[f](e,l,s)===!1)return}i=i.parent}const a=t.appContext.config.errorHandler;if(a){ao(),Jn(a,null,10,[e,l,s]),so();return}}r_(e,n,r,o)}function r_(e,t,n,o=!0){console.error(e)}let Ur=!1,Ta=!1;const wt=[];let gn=0;const Go=[];let Un=null,yo=0;const Dd=Promise.resolve();let Es=null;function bt(e){const t=Es||Dd;return e?t.then(this?e.bind(this):e):t}function i_(e){let t=gn+1,n=wt.length;for(;t<n;){const o=t+n>>>1,r=wt[o],i=jr(r);i<e||i===e&&r.pre?t=o+1:n=o}return t}function Ss(e){(!wt.length||!wt.includes(e,Ur&&e.allowRecurse?gn+1:gn))&&(e.id==null?wt.push(e):wt.splice(i_(e.id),0,e),Rd())}function Rd(){!Ur&&!Ta&&(Ta=!0,Es=Dd.then(Nd))}function l_(e){const t=wt.indexOf(e);t>gn&&wt.splice(t,1)}function wa(e){me(e)?Go.push(...e):(!Un||!Un.includes(e,e.allowRecurse?yo+1:yo))&&Go.push(e),Rd()}function kc(e,t,n=Ur?gn+1:0){for(;n<wt.length;n++){const o=wt[n];if(o&&o.pre){if(e&&o.id!==e.uid)continue;wt.splice(n,1),n--,o()}}}function Ki(e){if(Go.length){const t=[...new Set(Go)].sort((n,o)=>jr(n)-jr(o));if(Go.length=0,Un){Un.push(...t);return}for(Un=t,yo=0;yo<Un.length;yo++){const n=Un[yo];n.active!==!1&&n()}Un=null,yo=0}}const jr=e=>e.id==null?1/0:e.id,a_=(e,t)=>{const n=jr(e)-jr(t);if(n===0){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function Nd(e){Ta=!1,Ur=!0,wt.sort(a_);try{for(gn=0;gn<wt.length;gn++){const t=wt[gn];t&&t.active!==!1&&Jn(t,t.i,t.i?15:14)}}finally{gn=0,wt.length=0,Ki(),Ur=!1,Es=null,(wt.length||Go.length)&&Nd()}}let pt=null,_l=null;function Yi(e){const t=pt;return pt=e,_l=e&&e.type.__scopeId||null,t}function Cs(e){_l=e}function Ts(){_l=null}function $(e,t=pt,n){if(!t||e._n)return e;const o=(...r)=>{o._d&&Bc(-1);const i=Yi(t);let l;try{l=e(...r)}finally{Yi(i),o._d&&Bc(1)}return l};return o._n=!0,o._c=!0,o._d=!0,o}function Md(e,t){if(pt===null)return e;const n=kl(pt),o=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[i,l,s,a=Ye]=t[r];i&&(Ee(i)&&(i={mounted:i,updated:i}),i.deep&&Yn(l),o.push({dir:i,instance:n,value:l,oldValue:void 0,arg:s,modifiers:a}))}return e}function mn(e,t,n,o){const r=e.dirs,i=t&&t.dirs;for(let l=0;l<r.length;l++){const s=r[l];i&&(s.oldValue=i[l].value);let a=s.dir[o];a&&(ao(),en(a,n,8,[e.el,s,e,t]),so())}}const jn=Symbol("_leaveCb"),gi=Symbol("_enterCb");function s_(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Pt(()=>{e.isMounted=!0}),Zr(()=>{e.isUnmounting=!0}),e}const Yt=[Function,Array],Vd={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Yt,onEnter:Yt,onAfterEnter:Yt,onEnterCancelled:Yt,onBeforeLeave:Yt,onLeave:Yt,onAfterLeave:Yt,onLeaveCancelled:Yt,onBeforeAppear:Yt,onAppear:Yt,onAfterAppear:Yt,onAppearCancelled:Yt},$d=e=>{const t=e.subTree;return t.component?$d(t.component):t},c_={name:"BaseTransition",props:Vd,setup(e,{slots:t}){const n=Et(),o=s_();return()=>{const r=t.default&&Bd(t.default(),!0);if(!r||!r.length)return;let i=r[0];if(r.length>1){for(const d of r)if(d.type!==vt){i=d;break}}const l=Le(e),{mode:s}=l;if(o.isLeaving)return Fl(i);const a=Oc(i);if(!a)return Fl(i);let c=ka(a,l,o,n,d=>c=d);zi(a,c);const f=n.subTree,u=f&&Oc(f);if(u&&u.type!==vt&&!vn(a,u)&&$d(n).type!==vt){const d=ka(u,l,o,n);if(zi(u,d),s==="out-in"&&a.type!==vt)return o.isLeaving=!0,d.afterLeave=()=>{o.isLeaving=!1,n.update.active!==!1&&(n.effect.dirty=!0,n.update())},Fl(i);s==="in-out"&&a.type!==vt&&(d.delayLeave=(p,h,g)=>{const O=Fd(o,u);O[String(u.key)]=u,p[jn]=()=>{h(),p[jn]=void 0,delete c.delayedLeave},c.delayedLeave=g})}return i}}},u_=c_;function Fd(e,t){const{leavingVNodes:n}=e;let o=n.get(t.type);return o||(o=Object.create(null),n.set(t.type,o)),o}function ka(e,t,n,o,r){const{appear:i,mode:l,persisted:s=!1,onBeforeEnter:a,onEnter:c,onAfterEnter:f,onEnterCancelled:u,onBeforeLeave:d,onLeave:p,onAfterLeave:h,onLeaveCancelled:g,onBeforeAppear:O,onAppear:A,onAfterAppear:V,onAppearCancelled:v}=t,b=String(e.key),w=Fd(n,e),S=(x,L)=>{x&&en(x,o,9,L)},N=(x,L)=>{const D=L[1];S(x,L),me(x)?x.every(I=>I.length<=1)&&D():x.length<=1&&D()},M={mode:l,persisted:s,beforeEnter(x){let L=a;if(!n.isMounted)if(i)L=O||a;else return;x[jn]&&x[jn](!0);const D=w[b];D&&vn(e,D)&&D.el[jn]&&D.el[jn](),S(L,[x])},enter(x){let L=c,D=f,I=u;if(!n.isMounted)if(i)L=A||c,D=V||f,I=v||u;else return;let j=!1;const Q=x[gi]=G=>{j||(j=!0,G?S(I,[x]):S(D,[x]),M.delayedLeave&&M.delayedLeave(),x[gi]=void 0)};L?N(L,[x,Q]):Q()},leave(x,L){const D=String(e.key);if(x[gi]&&x[gi](!0),n.isUnmounting)return L();S(d,[x]);let I=!1;const j=x[jn]=Q=>{I||(I=!0,L(),Q?S(g,[x]):S(h,[x]),x[jn]=void 0,w[D]===e&&delete w[D])};w[D]=e,p?N(p,[x,j]):j()},clone(x){const L=ka(x,t,n,o,r);return r&&r(L),L}};return M}function Fl(e){if(bl(e))return e=no(e),e.children=null,e}function Oc(e){if(!bl(e))return e;const{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&Ee(n.default))return n.default()}}function zi(e,t){e.shapeFlag&6&&e.component?zi(e.component.subTree,t):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Bd(e,t=!1,n){let o=[],r=0;for(let i=0;i<e.length;i++){let l=e[i];const s=n==null?l.key:String(n)+String(l.key!=null?l.key:i);l.type===Be?(l.patchFlag&128&&r++,o=o.concat(Bd(l.children,t,s))):(t||l.type!==vt)&&o.push(s!=null?no(l,{key:s}):l)}if(r>1)for(let i=0;i<o.length;i++)o[i].patchFlag=-2;return o}/*! #__NO_SIDE_EFFECTS__ */function ue(e,t){return Ee(e)?mt({name:e.name},t,{setup:e}):e}const Ko=e=>!!e.type.__asyncLoader,bl=e=>e.type.__isKeepAlive;function Qr(e,t){Ud(e,"a",t)}function cr(e,t){Ud(e,"da",t)}function Ud(e,t,n=_t){const o=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(yl(t,o,n),n){let r=n.parent;for(;r&&r.parent;)bl(r.parent.vnode)&&f_(o,t,n,r),r=r.parent}}function f_(e,t,n,o){const r=yl(t,e,o,!0);wo(()=>{ss(o[t],r)},n)}function yl(e,t,n=_t,o=!1){if(n){const r=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...l)=>{ao();const s=ei(n),a=en(t,n,e,l);return s(),so(),a});return o?r.unshift(i):r.push(i),i}}const Dn=e=>(t,n=_t)=>{(!wl||e==="sp")&&yl(e,(...o)=>t(...o),n)},d_=Dn("bm"),Pt=Dn("m"),p_=Dn("bu"),h_=Dn("u"),Zr=Dn("bum"),wo=Dn("um"),m_=Dn("sp"),g_=Dn("rtg"),v_=Dn("rtc");function __(e,t=_t){yl("ec",e,t)}const b_="components";function El(e,t){return E_(b_,e,!0,t)||e}const y_=Symbol.for("v-ndc");function E_(e,t,n=!0,o=!1){const r=pt||_t;if(r){const i=r.type;{const s=_b(i,!1);if(s&&(s===t||s===cn(t)||s===gl(cn(t))))return i}const l=Pc(r[e]||i[e],t)||Pc(r.appContext[e],t);return!l&&o?i:l}}function Pc(e,t){return e&&(e[t]||e[cn(t)]||e[gl(cn(t))])}function Sl(e,t,n,o){let r;const i=n;if(me(e)||nt(e)){r=new Array(e.length);for(let l=0,s=e.length;l<s;l++)r[l]=t(e[l],l,void 0,i)}else if(typeof e=="number"){r=new Array(e);for(let l=0;l<e;l++)r[l]=t(l+1,l,void 0,i)}else if(He(e))if(e[Symbol.iterator])r=Array.from(e,(l,s)=>t(l,s,void 0,i));else{const l=Object.keys(e);r=new Array(l.length);for(let s=0,a=l.length;s<a;s++){const c=l[s];r[s]=t(e[c],c,s,i)}}else r=[];return r}function jd(e,t,n={},o,r){if(pt.isCE||pt.parent&&Ko(pt.parent)&&pt.parent.isCE)return t!=="default"&&(n.name=t),m("slot",n,o&&o());let i=e[t];i&&i._c&&(i._d=!1),le();const l=i&&Hd(i(n)),s=_e(Be,{key:(n.key||l&&l.key||`_${t}`)+(!l&&o?"_fb":"")},l||(o?o():[]),l&&e._===1?64:-2);return s.scopeId&&(s.slotScopeIds=[s.scopeId+"-s"]),i&&i._c&&(i._d=!0),s}function Hd(e){return e.some(t=>er(t)?!(t.type===vt||t.type===Be&&!Hd(t.children)):!0)?e:null}const Oa=e=>e?mp(e)?kl(e):Oa(e.parent):null,kr=mt(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Oa(e.parent),$root:e=>Oa(e.root),$emit:e=>e.emit,$options:e=>ws(e),$forceUpdate:e=>e.f||(e.f=()=>{e.effect.dirty=!0,Ss(e.update)}),$nextTick:e=>e.n||(e.n=bt.bind(e.proxy)),$watch:e=>Y_.bind(e)}),Bl=(e,t)=>e!==Ye&&!e.__isScriptSetup&&xe(e,t),S_={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:o,data:r,props:i,accessCache:l,type:s,appContext:a}=e;let c;if(t[0]!=="$"){const p=l[t];if(p!==void 0)switch(p){case 1:return o[t];case 2:return r[t];case 4:return n[t];case 3:return i[t]}else{if(Bl(o,t))return l[t]=1,o[t];if(r!==Ye&&xe(r,t))return l[t]=2,r[t];if((c=e.propsOptions[0])&&xe(c,t))return l[t]=3,i[t];if(n!==Ye&&xe(n,t))return l[t]=4,n[t];Pa&&(l[t]=0)}}const f=kr[t];let u,d;if(f)return t==="$attrs"&&$t(e.attrs,"get",""),f(e);if((u=s.__cssModules)&&(u=u[t]))return u;if(n!==Ye&&xe(n,t))return l[t]=4,n[t];if(d=a.config.globalProperties,xe(d,t))return d[t]},set({_:e},t,n){const{data:o,setupState:r,ctx:i}=e;return Bl(r,t)?(r[t]=n,!0):o!==Ye&&xe(o,t)?(o[t]=n,!0):xe(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:o,appContext:r,propsOptions:i}},l){let s;return!!n[l]||e!==Ye&&xe(e,l)||Bl(t,l)||(s=i[0])&&xe(s,l)||xe(o,l)||xe(kr,l)||xe(r.config.globalProperties,l)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:xe(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Ac(e){return me(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Pa=!0;function C_(e){const t=ws(e),n=e.proxy,o=e.ctx;Pa=!1,t.beforeCreate&&Ic(t.beforeCreate,e,"bc");const{data:r,computed:i,methods:l,watch:s,provide:a,inject:c,created:f,beforeMount:u,mounted:d,beforeUpdate:p,updated:h,activated:g,deactivated:O,beforeDestroy:A,beforeUnmount:V,destroyed:v,unmounted:b,render:w,renderTracked:S,renderTriggered:N,errorCaptured:M,serverPrefetch:x,expose:L,inheritAttrs:D,components:I,directives:j,filters:Q}=t;if(c&&T_(c,o,null),l)for(const X in l){const W=l[X];Ee(W)&&(o[X]=W.bind(n))}if(r){const X=r.call(n,n);He(X)&&(e.data=un(X))}if(Pa=!0,i)for(const X in i){const W=i[X],ie=Ee(W)?W.bind(n,n):Ee(W.get)?W.get.bind(n,n):Zt,ce=!Ee(W)&&Ee(W.set)?W.set.bind(n):Zt,ge=he({get:ie,set:ce});Object.defineProperty(o,X,{enumerable:!0,configurable:!0,get:()=>ge.value,set:Ce=>ge.value=Ce})}if(s)for(const X in s)Wd(s[X],o,n,X);if(a){const X=Ee(a)?a.call(n):a;Reflect.ownKeys(X).forEach(W=>{Qn(W,X[W])})}f&&Ic(f,e,"c");function U(X,W){me(W)?W.forEach(ie=>X(ie.bind(n))):W&&X(W.bind(n))}if(U(d_,u),U(Pt,d),U(p_,p),U(h_,h),U(Qr,g),U(cr,O),U(__,M),U(v_,S),U(g_,N),U(Zr,V),U(wo,b),U(m_,x),me(L))if(L.length){const X=e.exposed||(e.exposed={});L.forEach(W=>{Object.defineProperty(X,W,{get:()=>n[W],set:ie=>n[W]=ie})})}else e.exposed||(e.exposed={});w&&e.render===Zt&&(e.render=w),D!=null&&(e.inheritAttrs=D),I&&(e.components=I),j&&(e.directives=j)}function T_(e,t,n=Zt){me(e)&&(e=Aa(e));for(const o in e){const r=e[o];let i;He(r)?"default"in r?i=yt(r.from||o,r.default,!0):i=yt(r.from||o):i=yt(r),Ge(i)?Object.defineProperty(t,o,{enumerable:!0,configurable:!0,get:()=>i.value,set:l=>i.value=l}):t[o]=i}}function Ic(e,t,n){en(me(e)?e.map(o=>o.bind(t.proxy)):e.bind(t.proxy),t,n)}function Wd(e,t,n,o){const r=o.includes(".")?sp(n,o):()=>n[o];if(nt(e)){const i=t[e];Ee(i)&&De(r,i)}else if(Ee(e))De(r,e.bind(n));else if(He(e))if(me(e))e.forEach(i=>Wd(i,t,n,o));else{const i=Ee(e.handler)?e.handler.bind(n):t[e.handler];Ee(i)&&De(r,i,e)}}function ws(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:l}}=e.appContext,s=i.get(t);let a;return s?a=s:!r.length&&!n&&!o?a=t:(a={},r.length&&r.forEach(c=>Xi(a,c,l,!0)),Xi(a,t,l)),He(t)&&i.set(t,a),a}function Xi(e,t,n,o=!1){const{mixins:r,extends:i}=t;i&&Xi(e,i,n,!0),r&&r.forEach(l=>Xi(e,l,n,!0));for(const l in t)if(!(o&&l==="expose")){const s=w_[l]||n&&n[l];e[l]=s?s(e[l],t[l]):t[l]}return e}const w_={data:xc,props:Lc,emits:Lc,methods:yr,computed:yr,beforeCreate:It,created:It,beforeMount:It,mounted:It,beforeUpdate:It,updated:It,beforeDestroy:It,beforeUnmount:It,destroyed:It,unmounted:It,activated:It,deactivated:It,errorCaptured:It,serverPrefetch:It,components:yr,directives:yr,watch:O_,provide:xc,inject:k_};function xc(e,t){return t?e?function(){return mt(Ee(e)?e.call(this,this):e,Ee(t)?t.call(this,this):t)}:t:e}function k_(e,t){return yr(Aa(e),Aa(t))}function Aa(e){if(me(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function It(e,t){return e?[...new Set([].concat(e,t))]:t}function yr(e,t){return e?mt(Object.create(null),e,t):t}function Lc(e,t){return e?me(e)&&me(t)?[...new Set([...e,...t])]:mt(Object.create(null),Ac(e),Ac(t??{})):t}function O_(e,t){if(!e)return t;if(!t)return e;const n=mt(Object.create(null),e);for(const o in t)n[o]=It(e[o],t[o]);return n}function Gd(){return{app:null,config:{isNativeTag:mv,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let P_=0;function A_(e,t){return function(o,r=null){Ee(o)||(o=mt({},o)),r!=null&&!He(r)&&(r=null);const i=Gd(),l=new WeakSet;let s=!1;const a=i.app={_uid:P_++,_component:o,_props:r,_container:null,_context:i,_instance:null,version:vp,get config(){return i.config},set config(c){},use(c,...f){return l.has(c)||(c&&Ee(c.install)?(l.add(c),c.install(a,...f)):Ee(c)&&(l.add(c),c(a,...f))),a},mixin(c){return i.mixins.includes(c)||i.mixins.push(c),a},component(c,f){return f?(i.components[c]=f,a):i.components[c]},directive(c,f){return f?(i.directives[c]=f,a):i.directives[c]},mount(c,f,u){if(!s){const d=m(o,r);return d.appContext=i,u===!0?u="svg":u===!1&&(u=void 0),f&&t?t(d,c):e(d,c,u),s=!0,a._container=c,c.__vue_app__=a,kl(d.component)}},unmount(){s&&(e(null,a._container),delete a._container.__vue_app__)},provide(c,f){return i.provides[c]=f,a},runWithContext(c){const f=Yo;Yo=a;try{return c()}finally{Yo=f}}};return a}}let Yo=null;function Qn(e,t){if(_t){let n=_t.provides;const o=_t.parent&&_t.parent.provides;o===n&&(n=_t.provides=Object.create(o)),n[e]=t}}function yt(e,t,n=!1){const o=_t||pt;if(o||Yo){const r=Yo?Yo._context.provides:o?o.parent==null?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&Ee(t)?t.call(o&&o.proxy):t}}const Kd={},Yd=()=>Object.create(Kd),zd=e=>Object.getPrototypeOf(e)===Kd;function I_(e,t,n,o=!1){const r={},i=Yd();e.propsDefaults=Object.create(null),Xd(e,t,r,i);for(const l in e.propsOptions[0])l in r||(r[l]=void 0);n?e.props=o?r:kd(r):e.type.props?e.props=r:e.props=i,e.attrs=i}function x_(e,t,n,o){const{props:r,attrs:i,vnode:{patchFlag:l}}=e,s=Le(r),[a]=e.propsOptions;let c=!1;if((o||l>0)&&!(l&16)){if(l&8){const f=e.vnode.dynamicProps;for(let u=0;u<f.length;u++){let d=f[u];if(Tl(e.emitsOptions,d))continue;const p=t[d];if(a)if(xe(i,d))p!==i[d]&&(i[d]=p,c=!0);else{const h=cn(d);r[h]=Ia(a,s,h,p,e,!1)}else p!==i[d]&&(i[d]=p,c=!0)}}}else{Xd(e,t,r,i)&&(c=!0);let f;for(const u in s)(!t||!xe(t,u)&&((f=lo(u))===u||!xe(t,f)))&&(a?n&&(n[u]!==void 0||n[f]!==void 0)&&(r[u]=Ia(a,s,u,void 0,e,!0)):delete r[u]);if(i!==s)for(const u in i)(!t||!xe(t,u))&&(delete i[u],c=!0)}c&&An(e.attrs,"set","")}function Xd(e,t,n,o){const[r,i]=e.propsOptions;let l=!1,s;if(t)for(let a in t){if(Ho(a))continue;const c=t[a];let f;r&&xe(r,f=cn(a))?!i||!i.includes(f)?n[f]=c:(s||(s={}))[f]=c:Tl(e.emitsOptions,a)||(!(a in o)||c!==o[a])&&(o[a]=c,l=!0)}if(i){const a=Le(n),c=s||Ye;for(let f=0;f<i.length;f++){const u=i[f];n[u]=Ia(r,a,u,c[u],e,!xe(c,u))}}return l}function Ia(e,t,n,o,r,i){const l=e[n];if(l!=null){const s=xe(l,"default");if(s&&o===void 0){const a=l.default;if(l.type!==Function&&!l.skipFactory&&Ee(a)){const{propsDefaults:c}=r;if(n in c)o=c[n];else{const f=ei(r);o=c[n]=a.call(null,t),f()}}else o=a}l[0]&&(i&&!s?o=!1:l[1]&&(o===""||o===lo(n))&&(o=!0))}return o}const L_=new WeakMap;function qd(e,t,n=!1){const o=n?L_:t.propsCache,r=o.get(e);if(r)return r;const i=e.props,l={},s=[];let a=!1;if(!Ee(e)){const f=u=>{a=!0;const[d,p]=qd(u,t,!0);mt(l,d),p&&s.push(...p)};!n&&t.mixins.length&&t.mixins.forEach(f),e.extends&&f(e.extends),e.mixins&&e.mixins.forEach(f)}if(!i&&!a)return He(e)&&o.set(e,Uo),Uo;if(me(i))for(let f=0;f<i.length;f++){const u=cn(i[f]);Dc(u)&&(l[u]=Ye)}else if(i)for(const f in i){const u=cn(f);if(Dc(u)){const d=i[f],p=l[u]=me(d)||Ee(d)?{type:d}:mt({},d),h=p.type;let g=!1,O=!0;if(me(h))for(let A=0;A<h.length;++A){const V=h[A],v=Ee(V)&&V.name;if(v==="Boolean"){g=!0;break}else v==="String"&&(O=!1)}else g=Ee(h)&&h.name==="Boolean";p[0]=g,p[1]=O,(g||xe(p,"default"))&&s.push(u)}}const c=[l,s];return He(e)&&o.set(e,c),c}function Dc(e){return e[0]!=="$"&&!Ho(e)}const Jd=e=>e[0]==="_"||e==="$stable",ks=e=>me(e)?e.map(Wt):[Wt(e)],D_=(e,t,n)=>{if(t._n)return t;const o=$((...r)=>ks(t(...r)),n);return o._c=!1,o},Qd=(e,t,n)=>{const o=e._ctx;for(const r in e){if(Jd(r))continue;const i=e[r];if(Ee(i))t[r]=D_(r,i,o);else if(i!=null){const l=ks(i);t[r]=()=>l}}},Zd=(e,t)=>{const n=ks(t);e.slots.default=()=>n},ep=(e,t,n)=>{for(const o in t)(n||o!=="_")&&(e[o]=t[o])},R_=(e,t,n)=>{const o=e.slots=Yd();if(e.vnode.shapeFlag&32){const r=t._;r?(ep(o,t,n),n&&ld(o,"_",r,!0)):Qd(t,o)}else t&&Zd(e,t)},N_=(e,t,n)=>{const{vnode:o,slots:r}=e;let i=!0,l=Ye;if(o.shapeFlag&32){const s=t._;s?n&&s===1?i=!1:ep(r,t,n):(i=!t.$stable,Qd(t,r)),l=t}else t&&(Zd(e,t),l={default:1});if(i)for(const s in r)!Jd(s)&&l[s]==null&&delete r[s]};function qi(e,t,n,o,r=!1){if(me(e)){e.forEach((d,p)=>qi(d,t&&(me(t)?t[p]:t),n,o,r));return}if(Ko(o)&&!r)return;const i=o.shapeFlag&4?kl(o.component):o.el,l=r?null:i,{i:s,r:a}=e,c=t&&t.r,f=s.refs===Ye?s.refs={}:s.refs,u=s.setupState;if(c!=null&&c!==a&&(nt(c)?(f[c]=null,xe(u,c)&&(u[c]=null)):Ge(c)&&(c.value=null)),Ee(a))Jn(a,s,12,[l,f]);else{const d=nt(a),p=Ge(a);if(d||p){const h=()=>{if(e.f){const g=d?xe(u,a)?u[a]:f[a]:a.value;r?me(g)&&ss(g,i):me(g)?g.includes(i)||g.push(i):d?(f[a]=[i],xe(u,a)&&(u[a]=f[a])):(a.value=[i],e.k&&(f[e.k]=a.value))}else d?(f[a]=l,xe(u,a)&&(u[a]=l)):p&&(a.value=l,e.k&&(f[e.k]=l))};l?(h.id=-1,Mt(h,n)):h()}}}const tp=Symbol("_vte"),M_=e=>e.__isTeleport,Or=e=>e&&(e.disabled||e.disabled===""),Rc=e=>typeof SVGElement<"u"&&e instanceof SVGElement,Nc=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,xa=(e,t)=>{const n=e&&e.to;return nt(n)?t?t(n):null:n},V_={name:"Teleport",__isTeleport:!0,process(e,t,n,o,r,i,l,s,a,c){const{mc:f,pc:u,pbc:d,o:{insert:p,querySelector:h,createText:g,createComment:O}}=c,A=Or(t.props);let{shapeFlag:V,children:v,dynamicChildren:b}=t;if(e==null){const w=t.el=g(""),S=t.anchor=g("");p(w,n,o),p(S,n,o);const N=t.target=xa(t.props,h),M=rp(N,t,g,p);N&&(l==="svg"||Rc(N)?l="svg":(l==="mathml"||Nc(N))&&(l="mathml"));const x=(L,D)=>{V&16&&f(v,L,D,r,i,l,s,a)};A?x(n,S):N&&x(N,M)}else{t.el=e.el,t.targetStart=e.targetStart;const w=t.anchor=e.anchor,S=t.target=e.target,N=t.targetAnchor=e.targetAnchor,M=Or(e.props),x=M?n:S,L=M?w:N;if(l==="svg"||Rc(S)?l="svg":(l==="mathml"||Nc(S))&&(l="mathml"),b?(d(e.dynamicChildren,b,x,r,i,l,s),Os(e,t,!0)):a||u(e,t,x,L,r,i,l,s,!1),A)M?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):vi(t,n,w,c,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const D=t.target=xa(t.props,h);D&&vi(t,D,null,c,0)}else M&&vi(t,S,N,c,1)}op(t)},remove(e,t,n,{um:o,o:{remove:r}},i){const{shapeFlag:l,children:s,anchor:a,targetStart:c,targetAnchor:f,target:u,props:d}=e;if(u&&(r(c),r(f)),i&&r(a),l&16){const p=i||!Or(d);for(let h=0;h<s.length;h++){const g=s[h];o(g,t,n,p,!!g.dynamicChildren)}}},move:vi,hydrate:$_};function vi(e,t,n,{o:{insert:o},m:r},i=2){i===0&&o(e.targetAnchor,t,n);const{el:l,anchor:s,shapeFlag:a,children:c,props:f}=e,u=i===2;if(u&&o(l,t,n),(!u||Or(f))&&a&16)for(let d=0;d<c.length;d++)r(c[d],t,n,2);u&&o(s,t,n)}function $_(e,t,n,o,r,i,{o:{nextSibling:l,parentNode:s,querySelector:a,insert:c,createText:f}},u){const d=t.target=xa(t.props,a);if(d){const p=d._lpa||d.firstChild;if(t.shapeFlag&16)if(Or(t.props))t.anchor=u(l(e),t,s(e),n,o,r,i),t.targetStart=p,t.targetAnchor=p&&l(p);else{t.anchor=l(e);let h=p;for(;h;){if(h&&h.nodeType===8){if(h.data==="teleport start anchor")t.targetStart=h;else if(h.data==="teleport anchor"){t.targetAnchor=h,d._lpa=t.targetAnchor&&l(t.targetAnchor);break}}h=l(h)}t.targetAnchor||rp(d,t,f,c),u(p&&l(p),t,d,n,o,r,i)}op(t)}return t.anchor&&l(t.anchor)}const np=V_;function op(e){const t=e.ctx;if(t&&t.ut){let n=e.children[0].el;for(;n&&n!==e.targetAnchor;)n.nodeType===1&&n.setAttribute("data-v-owner",t.uid),n=n.nextSibling;t.ut()}}function rp(e,t,n,o){const r=t.targetStart=n(""),i=t.targetAnchor=n("");return r[tp]=i,e&&(o(r,e),o(i,e)),i}let Mc=!1;const Oo=()=>{Mc||(console.error("Hydration completed but contains mismatches."),Mc=!0)},F_=e=>e.namespaceURI.includes("svg")&&e.tagName!=="foreignObject",B_=e=>e.namespaceURI.includes("MathML"),_i=e=>{if(F_(e))return"svg";if(B_(e))return"mathml"},bi=e=>e.nodeType===8;function U_(e){const{mt:t,p:n,o:{patchProp:o,createText:r,nextSibling:i,parentNode:l,remove:s,insert:a,createComment:c}}=e,f=(v,b)=>{if(!b.hasChildNodes()){n(null,v,b),Ki(),b._vnode=v;return}u(b.firstChild,v,null,null,null),Ki(),b._vnode=v},u=(v,b,w,S,N,M=!1)=>{M=M||!!b.dynamicChildren;const x=bi(v)&&v.data==="[",L=()=>g(v,b,w,S,N,x),{type:D,ref:I,shapeFlag:j,patchFlag:Q}=b;let G=v.nodeType;b.el=v,Q===-2&&(M=!1,b.dynamicChildren=null);let U=null;switch(D){case Zn:G!==3?b.children===""?(a(b.el=r(""),l(v),v),U=v):U=L():(v.data!==b.children&&(Oo(),v.data=b.children),U=i(v));break;case vt:V(v)?(U=i(v),A(b.el=v.content.firstChild,v,w)):G!==8||x?U=L():U=i(v);break;case Pr:if(x&&(v=i(v),G=v.nodeType),G===1||G===3){U=v;const X=!b.children.length;for(let W=0;W<b.staticCount;W++)X&&(b.children+=U.nodeType===1?U.outerHTML:U.data),W===b.staticCount-1&&(b.anchor=U),U=i(U);return x?i(U):U}else L();break;case Be:x?U=h(v,b,w,S,N,M):U=L();break;default:if(j&1)(G!==1||b.type.toLowerCase()!==v.tagName.toLowerCase())&&!V(v)?U=L():U=d(v,b,w,S,N,M);else if(j&6){b.slotScopeIds=N;const X=l(v);if(x?U=O(v):bi(v)&&v.data==="teleport start"?U=O(v,v.data,"teleport end"):U=i(v),t(b,X,null,w,S,_i(X),M),Ko(b)){let W;x?(W=m(Be),W.anchor=U?U.previousSibling:X.lastChild):W=v.nodeType===3?Oe(""):m("div"),W.el=v,b.component.subTree=W}}else j&64?G!==8?U=L():U=b.type.hydrate(v,b,w,S,N,M,e,p):j&128&&(U=b.type.hydrate(v,b,w,S,_i(l(v)),N,M,e,u))}return I!=null&&qi(I,null,S,b),U},d=(v,b,w,S,N,M)=>{M=M||!!b.dynamicChildren;const{type:x,props:L,patchFlag:D,shapeFlag:I,dirs:j,transition:Q}=b,G=x==="input"||x==="option";if(G||D!==-1){j&&mn(b,null,w,"created");let U=!1;if(V(v)){U=lp(S,Q)&&w&&w.vnode.props&&w.vnode.props.appear;const W=v.content.firstChild;U&&Q.beforeEnter(W),A(W,v,w),b.el=v=W}if(I&16&&!(L&&(L.innerHTML||L.textContent))){let W=p(v.firstChild,b,v,w,S,N,M);for(;W;){Oo();const ie=W;W=W.nextSibling,s(ie)}}else I&8&&v.textContent!==b.children&&(Oo(),v.textContent=b.children);if(L){if(G||!M||D&48){const W=v.tagName.includes("-");for(const ie in L)(G&&(ie.endsWith("value")||ie==="indeterminate")||Xr(ie)&&!Ho(ie)||ie[0]==="."||W)&&o(v,ie,null,L[ie],void 0,w)}else if(L.onClick)o(v,"onClick",null,L.onClick,void 0,w);else if(D&4&&Wo(L.style))for(const W in L.style)L.style[W]}let X;(X=L&&L.onVnodeBeforeMount)&&qt(X,w,b),j&&mn(b,null,w,"beforeMount"),((X=L&&L.onVnodeMounted)||j||U)&&fp(()=>{X&&qt(X,w,b),U&&Q.enter(v),j&&mn(b,null,w,"mounted")},S)}return v.nextSibling},p=(v,b,w,S,N,M,x)=>{x=x||!!b.dynamicChildren;const L=b.children,D=L.length;for(let I=0;I<D;I++){const j=x?L[I]:L[I]=Wt(L[I]),Q=j.type===Zn;if(v){if(Q&&!x){let G=L[I+1];G&&(G=Wt(G)).type===Zn&&(a(r(v.data.slice(j.children.length)),w,i(v)),v.data=j.children)}v=u(v,j,S,N,M,x)}else Q&&!j.children?a(j.el=r(""),w):(Oo(),n(null,j,w,null,S,N,_i(w),M))}return v},h=(v,b,w,S,N,M)=>{const{slotScopeIds:x}=b;x&&(N=N?N.concat(x):x);const L=l(v),D=p(i(v),b,L,w,S,N,M);return D&&bi(D)&&D.data==="]"?i(b.anchor=D):(Oo(),a(b.anchor=c("]"),L,D),D)},g=(v,b,w,S,N,M)=>{if(Oo(),b.el=null,M){const D=O(v);for(;;){const I=i(v);if(I&&I!==D)s(I);else break}}const x=i(v),L=l(v);return s(v),n(null,b,L,x,w,S,_i(L),N),x},O=(v,b="[",w="]")=>{let S=0;for(;v;)if(v=i(v),v&&bi(v)&&(v.data===b&&S++,v.data===w)){if(S===0)return i(v);S--}return v},A=(v,b,w)=>{const S=b.parentNode;S&&S.replaceChild(v,b);let N=w;for(;N;)N.vnode.el===b&&(N.vnode.el=N.subTree.el=v),N=N.parent},V=v=>v.nodeType===1&&v.tagName.toLowerCase()==="template";return[f,u]}const Mt=fp;function j_(e){return ip(e)}function H_(e){return ip(e,U_)}function ip(e,t){const n=sd();n.__VUE__=!0;const{insert:o,remove:r,patchProp:i,createElement:l,createText:s,createComment:a,setText:c,setElementText:f,parentNode:u,nextSibling:d,setScopeId:p=Zt,insertStaticContent:h}=e,g=(T,k,B,J=null,K=null,te=null,re=void 0,y=null,C=!!k.dynamicChildren)=>{if(T===k)return;T&&!vn(T,k)&&(J=P(T),Ce(T,K,te,!0),T=null),k.patchFlag===-2&&(C=!1,k.dynamicChildren=null);const{type:R,ref:Y,shapeFlag:oe}=k;switch(R){case Zn:O(T,k,B,J);break;case vt:A(T,k,B,J);break;case Pr:T==null&&V(k,B,J,re);break;case Be:I(T,k,B,J,K,te,re,y,C);break;default:oe&1?w(T,k,B,J,K,te,re,y,C):oe&6?j(T,k,B,J,K,te,re,y,C):(oe&64||oe&128)&&R.process(T,k,B,J,K,te,re,y,C,ee)}Y!=null&&K&&qi(Y,T&&T.ref,te,k||T,!k)},O=(T,k,B,J)=>{if(T==null)o(k.el=s(k.children),B,J);else{const K=k.el=T.el;k.children!==T.children&&c(K,k.children)}},A=(T,k,B,J)=>{T==null?o(k.el=a(k.children||""),B,J):k.el=T.el},V=(T,k,B,J)=>{[T.el,T.anchor]=h(T.children,k,B,J,T.el,T.anchor)},v=({el:T,anchor:k},B,J)=>{let K;for(;T&&T!==k;)K=d(T),o(T,B,J),T=K;o(k,B,J)},b=({el:T,anchor:k})=>{let B;for(;T&&T!==k;)B=d(T),r(T),T=B;r(k)},w=(T,k,B,J,K,te,re,y,C)=>{k.type==="svg"?re="svg":k.type==="math"&&(re="mathml"),T==null?S(k,B,J,K,te,re,y,C):x(T,k,K,te,re,y,C)},S=(T,k,B,J,K,te,re,y)=>{let C,R;const{props:Y,shapeFlag:oe,transition:Z,dirs:F}=T;if(C=T.el=l(T.type,te,Y&&Y.is,Y),oe&8?f(C,T.children):oe&16&&M(T.children,C,null,J,K,Ul(T,te),re,y),F&&mn(T,null,J,"created"),N(C,T,T.scopeId,re,J),Y){for(const de in Y)de!=="value"&&!Ho(de)&&i(C,de,null,Y[de],te,J);"value"in Y&&i(C,"value",null,Y.value,te),(R=Y.onVnodeBeforeMount)&&qt(R,J,T)}F&&mn(T,null,J,"beforeMount");const H=lp(K,Z);H&&Z.beforeEnter(C),o(C,k,B),((R=Y&&Y.onVnodeMounted)||H||F)&&Mt(()=>{R&&qt(R,J,T),H&&Z.enter(C),F&&mn(T,null,J,"mounted")},K)},N=(T,k,B,J,K)=>{if(B&&p(T,B),J)for(let te=0;te<J.length;te++)p(T,J[te]);if(K){let te=K.subTree;if(k===te){const re=K.vnode;N(T,re,re.scopeId,re.slotScopeIds,K.parent)}}},M=(T,k,B,J,K,te,re,y,C=0)=>{for(let R=C;R<T.length;R++){const Y=T[R]=y?Hn(T[R]):Wt(T[R]);g(null,Y,k,B,J,K,te,re,y)}},x=(T,k,B,J,K,te,re)=>{const y=k.el=T.el;let{patchFlag:C,dynamicChildren:R,dirs:Y}=k;C|=T.patchFlag&16;const oe=T.props||Ye,Z=k.props||Ye;let F;if(B&&ho(B,!1),(F=Z.onVnodeBeforeUpdate)&&qt(F,B,k,T),Y&&mn(k,T,B,"beforeUpdate"),B&&ho(B,!0),(oe.innerHTML&&Z.innerHTML==null||oe.textContent&&Z.textContent==null)&&f(y,""),R?L(T.dynamicChildren,R,y,B,J,Ul(k,K),te):re||W(T,k,y,null,B,J,Ul(k,K),te,!1),C>0){if(C&16)D(y,oe,Z,B,K);else if(C&2&&oe.class!==Z.class&&i(y,"class",null,Z.class,K),C&4&&i(y,"style",oe.style,Z.style,K),C&8){const H=k.dynamicProps;for(let de=0;de<H.length;de++){const pe=H[de],Ue=oe[pe],Ct=Z[pe];(Ct!==Ue||pe==="value")&&i(y,pe,Ue,Ct,K,B)}}C&1&&T.children!==k.children&&f(y,k.children)}else!re&&R==null&&D(y,oe,Z,B,K);((F=Z.onVnodeUpdated)||Y)&&Mt(()=>{F&&qt(F,B,k,T),Y&&mn(k,T,B,"updated")},J)},L=(T,k,B,J,K,te,re)=>{for(let y=0;y<k.length;y++){const C=T[y],R=k[y],Y=C.el&&(C.type===Be||!vn(C,R)||C.shapeFlag&70)?u(C.el):B;g(C,R,Y,null,J,K,te,re,!0)}},D=(T,k,B,J,K)=>{if(k!==B){if(k!==Ye)for(const te in k)!Ho(te)&&!(te in B)&&i(T,te,k[te],null,K,J);for(const te in B){if(Ho(te))continue;const re=B[te],y=k[te];re!==y&&te!=="value"&&i(T,te,y,re,K,J)}"value"in B&&i(T,"value",k.value,B.value,K)}},I=(T,k,B,J,K,te,re,y,C)=>{const R=k.el=T?T.el:s(""),Y=k.anchor=T?T.anchor:s("");let{patchFlag:oe,dynamicChildren:Z,slotScopeIds:F}=k;F&&(y=y?y.concat(F):F),T==null?(o(R,B,J),o(Y,B,J),M(k.children||[],B,Y,K,te,re,y,C)):oe>0&&oe&64&&Z&&T.dynamicChildren?(L(T.dynamicChildren,Z,B,K,te,re,y),(k.key!=null||K&&k===K.subTree)&&Os(T,k,!0)):W(T,k,B,Y,K,te,re,y,C)},j=(T,k,B,J,K,te,re,y,C)=>{k.slotScopeIds=y,T==null?k.shapeFlag&512?K.ctx.activate(k,B,J,re,C):Q(k,B,J,K,te,re,C):G(T,k,C)},Q=(T,k,B,J,K,te,re)=>{const y=T.component=pb(T,J,K);if(bl(T)&&(y.ctx.renderer=ee),hb(y,!1,re),y.asyncDep){if(K&&K.registerDep(y,U,re),!T.el){const C=y.subTree=m(vt);A(null,C,k,B)}}else U(y,T,k,B,K,te,re)},G=(T,k,B)=>{const J=k.component=T.component;if(Z_(T,k,B))if(J.asyncDep&&!J.asyncResolved){X(J,k,B);return}else J.next=k,l_(J.update),J.effect.dirty=!0,J.update();else k.el=T.el,J.vnode=k},U=(T,k,B,J,K,te,re)=>{const y=()=>{if(T.isMounted){let{next:Y,bu:oe,u:Z,parent:F,vnode:H}=T;{const Ft=ap(T);if(Ft){Y&&(Y.el=H.el,X(T,Y,re)),Ft.asyncDep.then(()=>{T.isUnmounted||y()});return}}let de=Y,pe;ho(T,!1),Y?(Y.el=H.el,X(T,Y,re)):Y=H,oe&&Vl(oe),(pe=Y.props&&Y.props.onVnodeBeforeUpdate)&&qt(pe,F,Y,H),ho(T,!0);const Ue=jl(T),Ct=T.subTree;T.subTree=Ue,g(Ct,Ue,u(Ct.el),P(Ct),T,K,te),Y.el=Ue.el,de===null&&As(T,Ue.el),Z&&Mt(Z,K),(pe=Y.props&&Y.props.onVnodeUpdated)&&Mt(()=>qt(pe,F,Y,H),K)}else{let Y;const{el:oe,props:Z}=k,{bm:F,m:H,parent:de}=T,pe=Ko(k);if(ho(T,!1),F&&Vl(F),!pe&&(Y=Z&&Z.onVnodeBeforeMount)&&qt(Y,de,k),ho(T,!0),oe&&ke){const Ue=()=>{T.subTree=jl(T),ke(oe,T.subTree,T,K,null)};pe?k.type.__asyncLoader().then(()=>!T.isUnmounted&&Ue()):Ue()}else{const Ue=T.subTree=jl(T);g(null,Ue,B,J,T,K,te),k.el=Ue.el}if(H&&Mt(H,K),!pe&&(Y=Z&&Z.onVnodeMounted)){const Ue=k;Mt(()=>qt(Y,de,Ue),K)}(k.shapeFlag&256||de&&Ko(de.vnode)&&de.vnode.shapeFlag&256)&&T.a&&Mt(T.a,K),T.isMounted=!0,k=B=J=null}},C=T.effect=new ds(y,Zt,()=>Ss(R),T.scope),R=T.update=()=>{C.dirty&&C.run()};R.i=T,R.id=T.uid,ho(T,!0),R()},X=(T,k,B)=>{k.component=T;const J=T.vnode.props;T.vnode=k,T.next=null,x_(T,k.props,J,B),N_(T,k.children,B),ao(),kc(T),so()},W=(T,k,B,J,K,te,re,y,C=!1)=>{const R=T&&T.children,Y=T?T.shapeFlag:0,oe=k.children,{patchFlag:Z,shapeFlag:F}=k;if(Z>0){if(Z&128){ce(R,oe,B,J,K,te,re,y,C);return}else if(Z&256){ie(R,oe,B,J,K,te,re,y,C);return}}F&8?(Y&16&&We(R,K,te),oe!==R&&f(B,oe)):Y&16?F&16?ce(R,oe,B,J,K,te,re,y,C):We(R,K,te,!0):(Y&8&&f(B,""),F&16&&M(oe,B,J,K,te,re,y,C))},ie=(T,k,B,J,K,te,re,y,C)=>{T=T||Uo,k=k||Uo;const R=T.length,Y=k.length,oe=Math.min(R,Y);let Z;for(Z=0;Z<oe;Z++){const F=k[Z]=C?Hn(k[Z]):Wt(k[Z]);g(T[Z],F,B,null,K,te,re,y,C)}R>Y?We(T,K,te,!0,!1,oe):M(k,B,J,K,te,re,y,C,oe)},ce=(T,k,B,J,K,te,re,y,C)=>{let R=0;const Y=k.length;let oe=T.length-1,Z=Y-1;for(;R<=oe&&R<=Z;){const F=T[R],H=k[R]=C?Hn(k[R]):Wt(k[R]);if(vn(F,H))g(F,H,B,null,K,te,re,y,C);else break;R++}for(;R<=oe&&R<=Z;){const F=T[oe],H=k[Z]=C?Hn(k[Z]):Wt(k[Z]);if(vn(F,H))g(F,H,B,null,K,te,re,y,C);else break;oe--,Z--}if(R>oe){if(R<=Z){const F=Z+1,H=F<Y?k[F].el:J;for(;R<=Z;)g(null,k[R]=C?Hn(k[R]):Wt(k[R]),B,H,K,te,re,y,C),R++}}else if(R>Z)for(;R<=oe;)Ce(T[R],K,te,!0),R++;else{const F=R,H=R,de=new Map;for(R=H;R<=Z;R++){const Bt=k[R]=C?Hn(k[R]):Wt(k[R]);Bt.key!=null&&de.set(Bt.key,R)}let pe,Ue=0;const Ct=Z-H+1;let Ft=!1,si=0;const ko=new Array(Ct);for(R=0;R<Ct;R++)ko[R]=0;for(R=F;R<=oe;R++){const Bt=T[R];if(Ue>=Ct){Ce(Bt,K,te,!0);continue}let hn;if(Bt.key!=null)hn=de.get(Bt.key);else for(pe=H;pe<=Z;pe++)if(ko[pe-H]===0&&vn(Bt,k[pe])){hn=pe;break}hn===void 0?Ce(Bt,K,te,!0):(ko[hn-H]=R+1,hn>=si?si=hn:Ft=!0,g(Bt,k[hn],B,null,K,te,re,y,C),Ue++)}const zs=Ft?W_(ko):Uo;for(pe=zs.length-1,R=Ct-1;R>=0;R--){const Bt=H+R,hn=k[Bt],Xs=Bt+1<Y?k[Bt+1].el:J;ko[R]===0?g(null,hn,B,Xs,K,te,re,y,C):Ft&&(pe<0||R!==zs[pe]?ge(hn,B,Xs,2):pe--)}}},ge=(T,k,B,J,K=null)=>{const{el:te,type:re,transition:y,children:C,shapeFlag:R}=T;if(R&6){ge(T.component.subTree,k,B,J);return}if(R&128){T.suspense.move(k,B,J);return}if(R&64){re.move(T,k,B,ee);return}if(re===Be){o(te,k,B);for(let oe=0;oe<C.length;oe++)ge(C[oe],k,B,J);o(T.anchor,k,B);return}if(re===Pr){v(T,k,B);return}if(J!==2&&R&1&&y)if(J===0)y.beforeEnter(te),o(te,k,B),Mt(()=>y.enter(te),K);else{const{leave:oe,delayLeave:Z,afterLeave:F}=y,H=()=>o(te,k,B),de=()=>{oe(te,()=>{H(),F&&F()})};Z?Z(te,H,de):de()}else o(te,k,B)},Ce=(T,k,B,J=!1,K=!1)=>{const{type:te,props:re,ref:y,children:C,dynamicChildren:R,shapeFlag:Y,patchFlag:oe,dirs:Z,cacheIndex:F}=T;if(oe===-2&&(K=!1),y!=null&&qi(y,null,B,T,!0),F!=null&&(k.renderCache[F]=void 0),Y&256){k.ctx.deactivate(T);return}const H=Y&1&&Z,de=!Ko(T);let pe;if(de&&(pe=re&&re.onVnodeBeforeUnmount)&&qt(pe,k,T),Y&6)ze(T.component,B,J);else{if(Y&128){T.suspense.unmount(B,J);return}H&&mn(T,null,k,"beforeUnmount"),Y&64?T.type.remove(T,k,B,ee,J):R&&!R.hasOnce&&(te!==Be||oe>0&&oe&64)?We(R,k,B,!1,!0):(te===Be&&oe&384||!K&&Y&16)&&We(C,k,B),J&&Re(T)}(de&&(pe=re&&re.onVnodeUnmounted)||H)&&Mt(()=>{pe&&qt(pe,k,T),H&&mn(T,null,k,"unmounted")},B)},Re=T=>{const{type:k,el:B,anchor:J,transition:K}=T;if(k===Be){Me(B,J);return}if(k===Pr){b(T);return}const te=()=>{r(B),K&&!K.persisted&&K.afterLeave&&K.afterLeave()};if(T.shapeFlag&1&&K&&!K.persisted){const{leave:re,delayLeave:y}=K,C=()=>re(B,te);y?y(T.el,te,C):C()}else te()},Me=(T,k)=>{let B;for(;T!==k;)B=d(T),r(T),T=B;r(k)},ze=(T,k,B)=>{const{bum:J,scope:K,update:te,subTree:re,um:y,m:C,a:R}=T;Vc(C),Vc(R),J&&Vl(J),K.stop(),te&&(te.active=!1,Ce(re,T,k,B)),y&&Mt(y,k),Mt(()=>{T.isUnmounted=!0},k),k&&k.pendingBranch&&!k.isUnmounted&&T.asyncDep&&!T.asyncResolved&&T.suspenseId===k.pendingId&&(k.deps--,k.deps===0&&k.resolve())},We=(T,k,B,J=!1,K=!1,te=0)=>{for(let re=te;re<T.length;re++)Ce(T[re],k,B,J,K)},P=T=>{if(T.shapeFlag&6)return P(T.component.subTree);if(T.shapeFlag&128)return T.suspense.next();const k=d(T.anchor||T.el),B=k&&k[tp];return B?d(B):k};let q=!1;const z=(T,k,B)=>{T==null?k._vnode&&Ce(k._vnode,null,null,!0):g(k._vnode||null,T,k,null,null,null,B),k._vnode=T,q||(q=!0,kc(),Ki(),q=!1)},ee={p:g,um:Ce,m:ge,r:Re,mt:Q,mc:M,pc:W,pbc:L,n:P,o:e};let se,ke;return t&&([se,ke]=t(ee)),{render:z,hydrate:se,createApp:A_(z,se)}}function Ul({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function ho({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function lp(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Os(e,t,n=!1){const o=e.children,r=t.children;if(me(o)&&me(r))for(let i=0;i<o.length;i++){const l=o[i];let s=r[i];s.shapeFlag&1&&!s.dynamicChildren&&((s.patchFlag<=0||s.patchFlag===32)&&(s=r[i]=Hn(r[i]),s.el=l.el),!n&&s.patchFlag!==-2&&Os(l,s)),s.type===Zn&&(s.el=l.el)}}function W_(e){const t=e.slice(),n=[0];let o,r,i,l,s;const a=e.length;for(o=0;o<a;o++){const c=e[o];if(c!==0){if(r=n[n.length-1],e[r]<c){t[o]=r,n.push(o);continue}for(i=0,l=n.length-1;i<l;)s=i+l>>1,e[n[s]]<c?i=s+1:l=s;c<e[n[i]]&&(i>0&&(t[o]=n[i-1]),n[i]=o)}}for(i=n.length,l=n[i-1];i-- >0;)n[i]=l,l=t[l];return n}function ap(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:ap(t)}function Vc(e){if(e)for(let t=0;t<e.length;t++)e[t].active=!1}const G_=Symbol.for("v-scx"),K_=()=>yt(G_);function Cl(e,t){return Ps(e,null,t)}const yi={};function De(e,t,n){return Ps(e,t,n)}function Ps(e,t,{immediate:n,deep:o,flush:r,once:i,onTrack:l,onTrigger:s}=Ye){if(t&&i){const S=t;t=(...N)=>{S(...N),w()}}const a=_t,c=S=>o===!0?S:Yn(S,o===!1?1:void 0);let f,u=!1,d=!1;if(Ge(e)?(f=()=>e.value,u=Qo(e)):Wo(e)?(f=()=>c(e),u=!0):me(e)?(d=!0,u=e.some(S=>Wo(S)||Qo(S)),f=()=>e.map(S=>{if(Ge(S))return S.value;if(Wo(S))return c(S);if(Ee(S))return Jn(S,a,2)})):Ee(e)?t?f=()=>Jn(e,a,2):f=()=>(p&&p(),en(e,a,3,[h])):f=Zt,t&&o){const S=f;f=()=>Yn(S())}let p,h=S=>{p=v.onStop=()=>{Jn(S,a,4),p=v.onStop=void 0}},g;if(wl)if(h=Zt,t?n&&en(t,a,3,[f(),d?[]:void 0,h]):f(),r==="sync"){const S=K_();g=S.__watcherHandles||(S.__watcherHandles=[])}else return Zt;let O=d?new Array(e.length).fill(yi):yi;const A=()=>{if(!(!v.active||!v.dirty))if(t){const S=v.run();(o||u||(d?S.some((N,M)=>to(N,O[M])):to(S,O)))&&(p&&p(),en(t,a,3,[S,O===yi?void 0:d&&O[0]===yi?[]:O,h]),O=S)}else v.run()};A.allowRecurse=!!t;let V;r==="sync"?V=A:r==="post"?V=()=>Mt(A,a&&a.suspense):(A.pre=!0,a&&(A.id=a.uid),V=()=>Ss(A));const v=new ds(f,Zt,V),b=hd(),w=()=>{v.stop(),b&&ss(b.effects,v)};return t?n?A():O=v.run():r==="post"?Mt(v.run.bind(v),a&&a.suspense):v.run(),g&&g.push(w),w}function Y_(e,t,n){const o=this.proxy,r=nt(e)?e.includes(".")?sp(o,e):()=>o[e]:e.bind(o,o);let i;Ee(t)?i=t:(i=t.handler,n=t);const l=ei(this),s=Ps(r,i.bind(o),n);return l(),s}function sp(e,t){const n=t.split(".");return()=>{let o=e;for(let r=0;r<n.length&&o;r++)o=o[n[r]];return o}}function Yn(e,t=1/0,n){if(t<=0||!He(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,Ge(e))Yn(e.value,t,n);else if(me(e))for(let o=0;o<e.length;o++)Yn(e[o],t,n);else if(nd(e)||jo(e))e.forEach(o=>{Yn(o,t,n)});else if(id(e)){for(const o in e)Yn(e[o],t,n);for(const o of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,o)&&Yn(e[o],t,n)}return e}const z_=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${cn(t)}Modifiers`]||e[`${lo(t)}Modifiers`];function X_(e,t,...n){if(e.isUnmounted)return;const o=e.vnode.props||Ye;let r=n;const i=t.startsWith("update:"),l=i&&z_(o,t.slice(7));l&&(l.trim&&(r=n.map(f=>nt(f)?f.trim():f)),l.number&&(r=n.map(yv)));let s,a=o[s=Ml(t)]||o[s=Ml(cn(t))];!a&&i&&(a=o[s=Ml(lo(t))]),a&&en(a,e,6,r);const c=o[s+"Once"];if(c){if(!e.emitted)e.emitted={};else if(e.emitted[s])return;e.emitted[s]=!0,en(c,e,6,r)}}function cp(e,t,n=!1){const o=t.emitsCache,r=o.get(e);if(r!==void 0)return r;const i=e.emits;let l={},s=!1;if(!Ee(e)){const a=c=>{const f=cp(c,t,!0);f&&(s=!0,mt(l,f))};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}return!i&&!s?(He(e)&&o.set(e,null),null):(me(i)?i.forEach(a=>l[a]=null):mt(l,i),He(e)&&o.set(e,l),l)}function Tl(e,t){return!e||!Xr(t)?!1:(t=t.slice(2).replace(/Once$/,""),xe(e,t[0].toLowerCase()+t.slice(1))||xe(e,lo(t))||xe(e,t))}function jl(e){const{type:t,vnode:n,proxy:o,withProxy:r,propsOptions:[i],slots:l,attrs:s,emit:a,render:c,renderCache:f,props:u,data:d,setupState:p,ctx:h,inheritAttrs:g}=e,O=Yi(e);let A,V;try{if(n.shapeFlag&4){const b=r||o,w=b;A=Wt(c.call(w,b,f,u,p,d,h)),V=s}else{const b=t;A=Wt(b.length>1?b(u,{attrs:s,slots:l,emit:a}):b(u,null)),V=t.props?s:J_(s)}}catch(b){Ar.length=0,Jr(b,e,1),A=m(vt)}let v=A;if(V&&g!==!1){const b=Object.keys(V),{shapeFlag:w}=v;b.length&&w&7&&(i&&b.some(as)&&(V=Q_(V,i)),v=no(v,V,!1,!0))}return n.dirs&&(v=no(v,null,!1,!0),v.dirs=v.dirs?v.dirs.concat(n.dirs):n.dirs),n.transition&&(v.transition=n.transition),A=v,Yi(O),A}function q_(e,t=!0){let n;for(let o=0;o<e.length;o++){const r=e[o];if(er(r)){if(r.type!==vt||r.children==="v-if"){if(n)return;n=r}}else return}return n}const J_=e=>{let t;for(const n in e)(n==="class"||n==="style"||Xr(n))&&((t||(t={}))[n]=e[n]);return t},Q_=(e,t)=>{const n={};for(const o in e)(!as(o)||!(o.slice(9)in t))&&(n[o]=e[o]);return n};function Z_(e,t,n){const{props:o,children:r,component:i}=e,{props:l,children:s,patchFlag:a}=t,c=i.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&a>=0){if(a&1024)return!0;if(a&16)return o?$c(o,l,c):!!l;if(a&8){const f=t.dynamicProps;for(let u=0;u<f.length;u++){const d=f[u];if(l[d]!==o[d]&&!Tl(c,d))return!0}}}else return(r||s)&&(!s||!s.$stable)?!0:o===l?!1:o?l?$c(o,l,c):!0:!!l;return!1}function $c(e,t,n){const o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let r=0;r<o.length;r++){const i=o[r];if(t[i]!==e[i]&&!Tl(n,i))return!0}return!1}function As({vnode:e,parent:t},n){for(;t;){const o=t.subTree;if(o.suspense&&o.suspense.activeBranch===e&&(o.el=e.el),o===e)(e=t.vnode).el=n,t=t.parent;else break}}const eb=e=>e.__isSuspense;let La=0;const tb={name:"Suspense",__isSuspense:!0,process(e,t,n,o,r,i,l,s,a,c){if(e==null)ob(t,n,o,r,i,l,s,a,c);else{if(i&&i.deps>0&&!e.suspense.isInFallback){t.suspense=e.suspense,t.suspense.vnode=t,t.el=e.el;return}rb(e,t,n,o,r,l,s,a,c)}},hydrate:ib,normalize:lb},nb=tb;function Hr(e,t){const n=e.props&&e.props[t];Ee(n)&&n()}function ob(e,t,n,o,r,i,l,s,a){const{p:c,o:{createElement:f}}=a,u=f("div"),d=e.suspense=up(e,r,o,t,u,n,i,l,s,a);c(null,d.pendingBranch=e.ssContent,u,null,o,d,i,l),d.deps>0?(Hr(e,"onPending"),Hr(e,"onFallback"),c(null,e.ssFallback,t,n,o,null,i,l),zo(d,e.ssFallback)):d.resolve(!1,!0)}function rb(e,t,n,o,r,i,l,s,{p:a,um:c,o:{createElement:f}}){const u=t.suspense=e.suspense;u.vnode=t,t.el=e.el;const d=t.ssContent,p=t.ssFallback,{activeBranch:h,pendingBranch:g,isInFallback:O,isHydrating:A}=u;if(g)u.pendingBranch=d,vn(d,g)?(a(g,d,u.hiddenContainer,null,r,u,i,l,s),u.deps<=0?u.resolve():O&&(A||(a(h,p,n,o,r,null,i,l,s),zo(u,p)))):(u.pendingId=La++,A?(u.isHydrating=!1,u.activeBranch=g):c(g,r,u),u.deps=0,u.effects.length=0,u.hiddenContainer=f("div"),O?(a(null,d,u.hiddenContainer,null,r,u,i,l,s),u.deps<=0?u.resolve():(a(h,p,n,o,r,null,i,l,s),zo(u,p))):h&&vn(d,h)?(a(h,d,n,o,r,u,i,l,s),u.resolve(!0)):(a(null,d,u.hiddenContainer,null,r,u,i,l,s),u.deps<=0&&u.resolve()));else if(h&&vn(d,h))a(h,d,n,o,r,u,i,l,s),zo(u,d);else if(Hr(t,"onPending"),u.pendingBranch=d,d.shapeFlag&512?u.pendingId=d.component.suspenseId:u.pendingId=La++,a(null,d,u.hiddenContainer,null,r,u,i,l,s),u.deps<=0)u.resolve();else{const{timeout:V,pendingId:v}=u;V>0?setTimeout(()=>{u.pendingId===v&&u.fallback(p)},V):V===0&&u.fallback(p)}}function up(e,t,n,o,r,i,l,s,a,c,f=!1){const{p:u,m:d,um:p,n:h,o:{parentNode:g,remove:O}}=c;let A;const V=ab(e);V&&t&&t.pendingBranch&&(A=t.pendingId,t.deps++);const v=e.props?ad(e.props.timeout):void 0,b=i,w={vnode:e,parent:t,parentComponent:n,namespace:l,container:o,hiddenContainer:r,deps:0,pendingId:La++,timeout:typeof v=="number"?v:-1,activeBranch:null,pendingBranch:null,isInFallback:!f,isHydrating:f,isUnmounted:!1,effects:[],resolve(S=!1,N=!1){const{vnode:M,activeBranch:x,pendingBranch:L,pendingId:D,effects:I,parentComponent:j,container:Q}=w;let G=!1;w.isHydrating?w.isHydrating=!1:S||(G=x&&L.transition&&L.transition.mode==="out-in",G&&(x.transition.afterLeave=()=>{D===w.pendingId&&(d(L,Q,i===b?h(x):i,0),wa(I))}),x&&(g(x.el)!==w.hiddenContainer&&(i=h(x)),p(x,j,w,!0)),G||d(L,Q,i,0)),zo(w,L),w.pendingBranch=null,w.isInFallback=!1;let U=w.parent,X=!1;for(;U;){if(U.pendingBranch){U.effects.push(...I),X=!0;break}U=U.parent}!X&&!G&&wa(I),w.effects=[],V&&t&&t.pendingBranch&&A===t.pendingId&&(t.deps--,t.deps===0&&!N&&t.resolve()),Hr(M,"onResolve")},fallback(S){if(!w.pendingBranch)return;const{vnode:N,activeBranch:M,parentComponent:x,container:L,namespace:D}=w;Hr(N,"onFallback");const I=h(M),j=()=>{w.isInFallback&&(u(null,S,L,I,x,null,D,s,a),zo(w,S))},Q=S.transition&&S.transition.mode==="out-in";Q&&(M.transition.afterLeave=j),w.isInFallback=!0,p(M,x,null,!0),Q||j()},move(S,N,M){w.activeBranch&&d(w.activeBranch,S,N,M),w.container=S},next(){return w.activeBranch&&h(w.activeBranch)},registerDep(S,N,M){const x=!!w.pendingBranch;x&&w.deps++;const L=S.vnode.el;S.asyncDep.catch(D=>{Jr(D,S,0)}).then(D=>{if(S.isUnmounted||w.isUnmounted||w.pendingId!==S.suspenseId)return;S.asyncResolved=!0;const{vnode:I}=S;Ra(S,D,!1),L&&(I.el=L);const j=!L&&S.subTree.el;N(S,I,g(L||S.subTree.el),L?null:h(S.subTree),w,l,M),j&&O(j),As(S,I.el),x&&--w.deps===0&&w.resolve()})},unmount(S,N){w.isUnmounted=!0,w.activeBranch&&p(w.activeBranch,n,S,N),w.pendingBranch&&p(w.pendingBranch,n,S,N)}};return w}function ib(e,t,n,o,r,i,l,s,a){const c=t.suspense=up(t,o,n,e.parentNode,document.createElement("div"),null,r,i,l,s,!0),f=a(e,c.pendingBranch=t.ssContent,n,c,i,l);return c.deps===0&&c.resolve(!1,!0),f}function lb(e){const{shapeFlag:t,children:n}=e,o=t&32;e.ssContent=Fc(o?n.default:n),e.ssFallback=o?Fc(n.fallback):m(vt)}function Fc(e){let t;if(Ee(e)){const n=Zo&&e._c;n&&(e._d=!1,le()),e=e(),n&&(e._d=!0,t=Vt,dp())}return me(e)&&(e=q_(e)),e=Wt(e),t&&!e.dynamicChildren&&(e.dynamicChildren=t.filter(n=>n!==e)),e}function fp(e,t){t&&t.pendingBranch?me(e)?t.effects.push(...e):t.effects.push(e):wa(e)}function zo(e,t){e.activeBranch=t;const{vnode:n,parentComponent:o}=e;let r=t.el;for(;!r&&t.component;)t=t.component.subTree,r=t.el;n.el=r,o&&o.subTree===n&&(o.vnode.el=r,As(o,r))}function ab(e){const t=e.props&&e.props.suspensible;return t!=null&&t!==!1}const Be=Symbol.for("v-fgt"),Zn=Symbol.for("v-txt"),vt=Symbol.for("v-cmt"),Pr=Symbol.for("v-stc"),Ar=[];let Vt=null;function le(e=!1){Ar.push(Vt=e?null:[])}function dp(){Ar.pop(),Vt=Ar[Ar.length-1]||null}let Zo=1;function Bc(e){Zo+=e,e<0&&Vt&&(Vt.hasOnce=!0)}function pp(e){return e.dynamicChildren=Zo>0?Vt||Uo:null,dp(),Zo>0&&Vt&&Vt.push(e),e}function kt(e,t,n,o,r,i){return pp(Pe(e,t,n,o,r,i,!0))}function _e(e,t,n,o,r){return pp(m(e,t,n,o,r,!0))}function er(e){return e?e.__v_isVNode===!0:!1}function vn(e,t){return e.type===t.type&&e.key===t.key}const hp=({key:e})=>e??null,xi=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?nt(e)||Ge(e)||Ee(e)?{i:pt,r:e,k:t,f:!!n}:e:null);function Pe(e,t=null,n=null,o=0,r=null,i=e===Be?0:1,l=!1,s=!1){const a={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&hp(t),ref:t&&xi(t),scopeId:_l,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:o,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:pt};return s?(Is(a,n),i&128&&e.normalize(a)):n&&(a.shapeFlag|=nt(n)?8:16),Zo>0&&!l&&Vt&&(a.patchFlag>0||i&6)&&a.patchFlag!==32&&Vt.push(a),a}const m=sb;function sb(e,t=null,n=null,o=0,r=null,i=!1){if((!e||e===y_)&&(e=vt),er(e)){const s=no(e,t,!0);return n&&Is(s,n),Zo>0&&!i&&Vt&&(s.shapeFlag&6?Vt[Vt.indexOf(e)]=s:Vt.push(s)),s.patchFlag=-2,s}if(bb(e)&&(e=e.__vccOpts),t){t=cb(t);let{class:s,style:a}=t;s&&!nt(s)&&(t.class=fs(s)),He(a)&&(Od(a)&&!me(a)&&(a=mt({},a)),t.style=us(a))}const l=nt(e)?1:eb(e)?128:M_(e)?64:He(e)?4:Ee(e)?2:0;return Pe(e,t,n,o,r,l,i,!0)}function cb(e){return e?Od(e)||zd(e)?mt({},e):e:null}function no(e,t,n=!1,o=!1){const{props:r,ref:i,patchFlag:l,children:s,transition:a}=e,c=t?fn(r||{},t):r,f={__v_isVNode:!0,__v_skip:!0,type:e.type,props:c,key:c&&hp(c),ref:t&&t.ref?n&&i?me(i)?i.concat(xi(t)):[i,xi(t)]:xi(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:s,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Be?l===-1?16:l|16:l,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:a,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&no(e.ssContent),ssFallback:e.ssFallback&&no(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return a&&o&&zi(f,a.clone(f)),f}function Oe(e=" ",t=0){return m(Zn,null,e,t)}function ub(e,t){const n=m(Pr,null,e);return n.staticCount=t,n}function at(e="",t=!1){return t?(le(),_e(vt,null,e)):m(vt,null,e)}function Wt(e){return e==null||typeof e=="boolean"?m(vt):me(e)?m(Be,null,e.slice()):typeof e=="object"?Hn(e):m(Zn,null,String(e))}function Hn(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:no(e)}function Is(e,t){let n=0;const{shapeFlag:o}=e;if(t==null)t=null;else if(me(t))n=16;else if(typeof t=="object")if(o&65){const r=t.default;r&&(r._c&&(r._d=!1),Is(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!zd(t)?t._ctx=pt:r===3&&pt&&(pt.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else Ee(t)?(t={default:t,_ctx:pt},n=32):(t=String(t),o&64?(n=16,t=[Oe(t)]):n=8);e.children=t,e.shapeFlag|=n}function fn(...e){const t={};for(let n=0;n<e.length;n++){const o=e[n];for(const r in o)if(r==="class")t.class!==o.class&&(t.class=fs([t.class,o.class]));else if(r==="style")t.style=us([t.style,o.style]);else if(Xr(r)){const i=t[r],l=o[r];l&&i!==l&&!(me(i)&&i.includes(l))&&(t[r]=i?[].concat(i,l):l)}else r!==""&&(t[r]=o[r])}return t}function qt(e,t,n,o=null){en(e,t,7,[n,o])}const fb=Gd();let db=0;function pb(e,t,n){const o=e.type,r=(t?t.appContext:e.appContext)||fb,i={uid:db++,vnode:e,type:o,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,scope:new dd(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:qd(o,r),emitsOptions:cp(o,r),emit:null,emitted:null,propsDefaults:Ye,inheritAttrs:o.inheritAttrs,ctx:Ye,data:Ye,props:Ye,attrs:Ye,slots:Ye,refs:Ye,setupState:Ye,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=X_.bind(null,i),e.ce&&e.ce(i),i}let _t=null;const Et=()=>_t||pt;let Ji,Da;{const e=sd(),t=(n,o)=>{let r;return(r=e[n])||(r=e[n]=[]),r.push(o),i=>{r.length>1?r.forEach(l=>l(i)):r[0](i)}};Ji=t("__VUE_INSTANCE_SETTERS__",n=>_t=n),Da=t("__VUE_SSR_SETTERS__",n=>wl=n)}const ei=e=>{const t=_t;return Ji(e),e.scope.on(),()=>{e.scope.off(),Ji(t)}},Uc=()=>{_t&&_t.scope.off(),Ji(null)};function mp(e){return e.vnode.shapeFlag&4}let wl=!1;function hb(e,t=!1,n=!1){t&&Da(t);const{props:o,children:r}=e.vnode,i=mp(e);I_(e,o,i,t),R_(e,r,n);const l=i?mb(e,t):void 0;return t&&Da(!1),l}function mb(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,S_);const{setup:o}=n;if(o){const r=e.setupContext=o.length>1?vb(e):null,i=ei(e);ao();const l=Jn(o,e,0,[e.props,r]);if(so(),i(),od(l)){if(l.then(Uc,Uc),t)return l.then(s=>{Ra(e,s,t)}).catch(s=>{Jr(s,e,0)});e.asyncDep=l}else Ra(e,l,t)}else gp(e,t)}function Ra(e,t,n){Ee(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:He(t)&&(e.setupState=xd(t)),gp(e,n)}let jc;function gp(e,t,n){const o=e.type;if(!e.render){if(!t&&jc&&!o.render){const r=o.template||ws(e).template;if(r){const{isCustomElement:i,compilerOptions:l}=e.appContext.config,{delimiters:s,compilerOptions:a}=o,c=mt(mt({isCustomElement:i,delimiters:s},l),a);o.render=jc(r,c)}}e.render=o.render||Zt}{const r=ei(e);ao();try{C_(e)}finally{so(),r()}}}const gb={get(e,t){return $t(e,"get",""),e[t]}};function vb(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,gb),slots:e.slots,emit:e.emit,expose:t}}function kl(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(xd(Pd(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in kr)return kr[n](e)},has(t,n){return n in t||n in kr}})):e.proxy}function _b(e,t=!0){return Ee(e)?e.displayName||e.name:e.name||t&&e.__name}function bb(e){return Ee(e)&&"__vccOpts"in e}const he=(e,t)=>Xv(e,t,wl);function ur(e,t,n){const o=arguments.length;return o===2?He(t)&&!me(t)?er(t)?m(e,null,[t]):m(e,t):m(e,null,t):(o>3?n=Array.prototype.slice.call(arguments,2):o===3&&er(n)&&(n=[n]),m(e,t,n))}const vp="3.4.38";/**
* @vue/runtime-dom v3.4.38
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const yb="http://www.w3.org/2000/svg",Eb="http://www.w3.org/1998/Math/MathML",kn=typeof document<"u"?document:null,Hc=kn&&kn.createElement("template"),Sb={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,o)=>{const r=t==="svg"?kn.createElementNS(yb,e):t==="mathml"?kn.createElementNS(Eb,e):n?kn.createElement(e,{is:n}):kn.createElement(e);return e==="select"&&o&&o.multiple!=null&&r.setAttribute("multiple",o.multiple),r},createText:e=>kn.createTextNode(e),createComment:e=>kn.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>kn.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,o,r,i){const l=n?n.previousSibling:t.lastChild;if(r&&(r===i||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===i||!(r=r.nextSibling)););else{Hc.innerHTML=o==="svg"?`<svg>${e}</svg>`:o==="mathml"?`<math>${e}</math>`:e;const s=Hc.content;if(o==="svg"||o==="mathml"){const a=s.firstChild;for(;a.firstChild;)s.appendChild(a.firstChild);s.removeChild(a)}t.insertBefore(s,n)}return[l?l.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Vn="transition",mr="animation",Wr=Symbol("_vtc"),ti=(e,{slots:t})=>ur(u_,Cb(e),t);ti.displayName="Transition";const _p={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String};ti.props=mt({},Vd,_p);const mo=(e,t=[])=>{me(e)?e.forEach(n=>n(...t)):e&&e(...t)},Wc=e=>e?me(e)?e.some(t=>t.length>1):e.length>1:!1;function Cb(e){const t={};for(const I in e)I in _p||(t[I]=e[I]);if(e.css===!1)return t;const{name:n="v",type:o,duration:r,enterFromClass:i=`${n}-enter-from`,enterActiveClass:l=`${n}-enter-active`,enterToClass:s=`${n}-enter-to`,appearFromClass:a=i,appearActiveClass:c=l,appearToClass:f=s,leaveFromClass:u=`${n}-leave-from`,leaveActiveClass:d=`${n}-leave-active`,leaveToClass:p=`${n}-leave-to`}=e,h=Tb(r),g=h&&h[0],O=h&&h[1],{onBeforeEnter:A,onEnter:V,onEnterCancelled:v,onLeave:b,onLeaveCancelled:w,onBeforeAppear:S=A,onAppear:N=V,onAppearCancelled:M=v}=t,x=(I,j,Q)=>{go(I,j?f:s),go(I,j?c:l),Q&&Q()},L=(I,j)=>{I._isLeaving=!1,go(I,u),go(I,p),go(I,d),j&&j()},D=I=>(j,Q)=>{const G=I?N:V,U=()=>x(j,I,Q);mo(G,[j,U]),Gc(()=>{go(j,I?a:i),$n(j,I?f:s),Wc(G)||Kc(j,o,g,U)})};return mt(t,{onBeforeEnter(I){mo(A,[I]),$n(I,i),$n(I,l)},onBeforeAppear(I){mo(S,[I]),$n(I,a),$n(I,c)},onEnter:D(!1),onAppear:D(!0),onLeave(I,j){I._isLeaving=!0;const Q=()=>L(I,j);$n(I,u),$n(I,d),Ob(),Gc(()=>{I._isLeaving&&(go(I,u),$n(I,p),Wc(b)||Kc(I,o,O,Q))}),mo(b,[I,Q])},onEnterCancelled(I){x(I,!1),mo(v,[I])},onAppearCancelled(I){x(I,!0),mo(M,[I])},onLeaveCancelled(I){L(I),mo(w,[I])}})}function Tb(e){if(e==null)return null;if(He(e))return[Hl(e.enter),Hl(e.leave)];{const t=Hl(e);return[t,t]}}function Hl(e){return ad(e)}function $n(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[Wr]||(e[Wr]=new Set)).add(t)}function go(e,t){t.split(/\s+/).forEach(o=>o&&e.classList.remove(o));const n=e[Wr];n&&(n.delete(t),n.size||(e[Wr]=void 0))}function Gc(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let wb=0;function Kc(e,t,n,o){const r=e._endId=++wb,i=()=>{r===e._endId&&o()};if(n)return setTimeout(i,n);const{type:l,timeout:s,propCount:a}=kb(e,t);if(!l)return o();const c=l+"end";let f=0;const u=()=>{e.removeEventListener(c,d),i()},d=p=>{p.target===e&&++f>=a&&u()};setTimeout(()=>{f<a&&u()},s+1),e.addEventListener(c,d)}function kb(e,t){const n=window.getComputedStyle(e),o=h=>(n[h]||"").split(", "),r=o(`${Vn}Delay`),i=o(`${Vn}Duration`),l=Yc(r,i),s=o(`${mr}Delay`),a=o(`${mr}Duration`),c=Yc(s,a);let f=null,u=0,d=0;t===Vn?l>0&&(f=Vn,u=l,d=i.length):t===mr?c>0&&(f=mr,u=c,d=a.length):(u=Math.max(l,c),f=u>0?l>c?Vn:mr:null,d=f?f===Vn?i.length:a.length:0);const p=f===Vn&&/\b(transform|all)(,|$)/.test(o(`${Vn}Property`).toString());return{type:f,timeout:u,propCount:d,hasTransform:p}}function Yc(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,o)=>zc(n)+zc(e[o])))}function zc(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function Ob(){return document.body.offsetHeight}function Pb(e,t,n){const o=e[Wr];o&&(t=(t?[t,...o]:[...o]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Qi=Symbol("_vod"),bp=Symbol("_vsh"),yp={beforeMount(e,{value:t},{transition:n}){e[Qi]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):gr(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:o}){!t!=!n&&(o?t?(o.beforeEnter(e),gr(e,!0),o.enter(e)):o.leave(e,()=>{gr(e,!1)}):gr(e,t))},beforeUnmount(e,{value:t}){gr(e,t)}};function gr(e,t){e.style.display=t?e[Qi]:"none",e[bp]=!t}const Ab=Symbol(""),Ib=/(^|;)\s*display\s*:/;function xb(e,t,n){const o=e.style,r=nt(n);let i=!1;if(n&&!r){if(t)if(nt(t))for(const l of t.split(";")){const s=l.slice(0,l.indexOf(":")).trim();n[s]==null&&Li(o,s,"")}else for(const l in t)n[l]==null&&Li(o,l,"");for(const l in n)l==="display"&&(i=!0),Li(o,l,n[l])}else if(r){if(t!==n){const l=o[Ab];l&&(n+=";"+l),o.cssText=n,i=Ib.test(n)}}else t&&e.removeAttribute("style");Qi in e&&(e[Qi]=i?o.display:"",e[bp]&&(o.display="none"))}const Xc=/\s*!important$/;function Li(e,t,n){if(me(n))n.forEach(o=>Li(e,t,o));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const o=Lb(e,t);Xc.test(n)?e.setProperty(lo(o),n.replace(Xc,""),"important"):e[o]=n}}const qc=["Webkit","Moz","ms"],Wl={};function Lb(e,t){const n=Wl[t];if(n)return n;let o=cn(t);if(o!=="filter"&&o in e)return Wl[t]=o;o=gl(o);for(let r=0;r<qc.length;r++){const i=qc[r]+o;if(i in e)return Wl[t]=i}return t}const Jc="http://www.w3.org/1999/xlink";function Qc(e,t,n,o,r,i=kv(t)){o&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(Jc,t.slice(6,t.length)):e.setAttributeNS(Jc,t,n):n==null||i&&!cd(n)?e.removeAttribute(t):e.setAttribute(t,i?"":io(n)?String(n):n)}function Db(e,t,n,o){if(t==="innerHTML"||t==="textContent"){if(n==null)return;e[t]=n;return}const r=e.tagName;if(t==="value"&&r!=="PROGRESS"&&!r.includes("-")){const l=r==="OPTION"?e.getAttribute("value")||"":e.value,s=n==null?"":String(n);(l!==s||!("_value"in e))&&(e.value=s),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=cd(n):n==null&&l==="string"?(n="",i=!0):l==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(t)}function Rb(e,t,n,o){e.addEventListener(t,n,o)}function Nb(e,t,n,o){e.removeEventListener(t,n,o)}const Zc=Symbol("_vei");function Mb(e,t,n,o,r=null){const i=e[Zc]||(e[Zc]={}),l=i[t];if(o&&l)l.value=o;else{const[s,a]=Vb(t);if(o){const c=i[t]=Bb(o,r);Rb(e,s,c,a)}else l&&(Nb(e,s,l,a),i[t]=void 0)}}const eu=/(?:Once|Passive|Capture)$/;function Vb(e){let t;if(eu.test(e)){t={};let o;for(;o=e.match(eu);)e=e.slice(0,e.length-o[0].length),t[o[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):lo(e.slice(2)),t]}let Gl=0;const $b=Promise.resolve(),Fb=()=>Gl||($b.then(()=>Gl=0),Gl=Date.now());function Bb(e,t){const n=o=>{if(!o._vts)o._vts=Date.now();else if(o._vts<=n.attached)return;en(Ub(o,n.value),t,5,[o])};return n.value=e,n.attached=Fb(),n}function Ub(e,t){if(me(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(o=>r=>!r._stopped&&o&&o(r))}else return t}const tu=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,jb=(e,t,n,o,r,i)=>{const l=r==="svg";t==="class"?Pb(e,o,l):t==="style"?xb(e,n,o):Xr(t)?as(t)||Mb(e,t,n,o,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Hb(e,t,o,l))?(Db(e,t,o),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Qc(e,t,o,l,i,t!=="value")):(t==="true-value"?e._trueValue=o:t==="false-value"&&(e._falseValue=o),Qc(e,t,o,l))};function Hb(e,t,n,o){if(o)return!!(t==="innerHTML"||t==="textContent"||t in e&&tu(t)&&Ee(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return tu(t)&&nt(n)?!1:t in e}const Wb={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},Gb=(e,t)=>{const n=e._withKeys||(e._withKeys={}),o=t.join(".");return n[o]||(n[o]=r=>{if(!("key"in r))return;const i=lo(r.key);if(t.some(l=>l===i||Wb[l]===i))return e(r)})},Ep=mt({patchProp:jb},Sb);let Ir,nu=!1;function Kb(){return Ir||(Ir=j_(Ep))}function Yb(){return Ir=nu?Ir:H_(Ep),nu=!0,Ir}const Sp=(...e)=>{const t=Kb().createApp(...e),{mount:n}=t;return t.mount=o=>{const r=Tp(o);if(!r)return;const i=t._component;!Ee(i)&&!i.render&&!i.template&&(i.template=r.innerHTML),r.innerHTML="";const l=n(r,!1,Cp(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),l},t},zb=(...e)=>{const t=Yb().createApp(...e),{mount:n}=t;return t.mount=o=>{const r=Tp(o);if(r)return n(r,!0,Cp(r))},t};function Cp(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Tp(e){return nt(e)?document.querySelector(e):e}/*!
  * vue-i18n v9.14.0
  * (c) 2024 kazuya kawaguchi
  * Released under the MIT License.
  */const Xb="9.14.0";function qb(){typeof __INTLIFY_PROD_DEVTOOLS__!="boolean"&&(ts().__INTLIFY_PROD_DEVTOOLS__=!1)}const wp=Kg.__EXTEND_POINT__,Sn=dl(wp);Sn(),Sn(),Sn(),Sn(),Sn(),Sn(),Sn(),Sn(),Sn();const kp=_n.__EXTEND_POINT__,Rt=dl(kp),tn={UNEXPECTED_RETURN_TYPE:kp,INVALID_ARGUMENT:Rt(),MUST_BE_CALL_SETUP_TOP:Rt(),NOT_INSTALLED:Rt(),NOT_AVAILABLE_IN_LEGACY_MODE:Rt(),REQUIRED_VALUE:Rt(),INVALID_VALUE:Rt(),CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:Rt(),NOT_INSTALLED_WITH_PROVIDE:Rt(),UNEXPECTED_ERROR:Rt(),NOT_COMPATIBLE_LEGACY_VUE_I18N:Rt(),BRIDGE_SUPPORT_VUE_2_ONLY:Rt(),MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION:Rt(),NOT_AVAILABLE_COMPOSITION_IN_LEGACY:Rt(),__EXTEND_POINT__:Rt()};function dn(e,...t){return sr(e,null,void 0)}const Na=oo("__translateVNode"),Ma=oo("__datetimeParts"),Va=oo("__numberParts"),Jb=oo("__setPluralRules"),Qb=oo("__injectWithOption"),$a=oo("__dispose");function Gr(e){if(!Ve(e))return e;for(const t in e)if(ji(e,t))if(!t.includes("."))Ve(e[t])&&Gr(e[t]);else{const n=t.split("."),o=n.length-1;let r=e,i=!1;for(let l=0;l<o;l++){if(n[l]in r||(r[n[l]]={}),!Ve(r[n[l]])){i=!0;break}r=r[n[l]]}i||(r[n[o]]=e[t],delete e[t]),Ve(r[n[o]])&&Gr(r[n[o]])}return e}function Op(e,t){const{messages:n,__i18n:o,messageResolver:r,flatJson:i}=t,l=Ae(n)?n:ct(o)?{}:{[e]:{}};if(ct(o)&&o.forEach(s=>{if("locale"in s&&"resource"in s){const{locale:a,resource:c}=s;a?(l[a]=l[a]||{},Ii(c,l[a])):Ii(c,l)}else ae(s)&&Ii(JSON.parse(s),l)}),r==null&&i)for(const s in l)ji(l,s)&&Gr(l[s]);return l}function Pp(e){return e.type}function Zb(e,t,n){let o=Ve(t.messages)?t.messages:{};"__i18nGlobal"in n&&(o=Op(e.locale.value,{messages:o,__i18n:n.__i18nGlobal}));const r=Object.keys(o);r.length&&r.forEach(i=>{e.mergeLocaleMessage(i,o[i])});{if(Ve(t.datetimeFormats)){const i=Object.keys(t.datetimeFormats);i.length&&i.forEach(l=>{e.mergeDateTimeFormat(l,t.datetimeFormats[l])})}if(Ve(t.numberFormats)){const i=Object.keys(t.numberFormats);i.length&&i.forEach(l=>{e.mergeNumberFormat(l,t.numberFormats[l])})}}}function ou(e){return m(Zn,null,e,0)}const ru="__INTLIFY_META__",iu=()=>[],ey=()=>!1;let lu=0;function au(e){return(t,n,o,r)=>e(n,o,Et()||void 0,r)}const ty=()=>{const e=Et();let t=null;return e&&(t=Pp(e)[ru])?{[ru]:t}:null};function Ap(e={},t){const{__root:n,__injectWithOption:o}=e,r=n===void 0,i=e.flatJson,l=Bi?fe:ys,s=!!e.translateExistCompatible;let a=qe(e.inheritLocale)?e.inheritLocale:!0;const c=l(n&&a?n.locale.value:ae(e.locale)?e.locale:Wi),f=l(n&&a?n.fallbackLocale.value:ae(e.fallbackLocale)||ct(e.fallbackLocale)||Ae(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:c.value),u=l(Op(c.value,e)),d=l(Ae(e.datetimeFormats)?e.datetimeFormats:{[c.value]:{}}),p=l(Ae(e.numberFormats)?e.numberFormats:{[c.value]:{}});let h=n?n.missingWarn:qe(e.missingWarn)||Ui(e.missingWarn)?e.missingWarn:!0,g=n?n.fallbackWarn:qe(e.fallbackWarn)||Ui(e.fallbackWarn)?e.fallbackWarn:!0,O=n?n.fallbackRoot:qe(e.fallbackRoot)?e.fallbackRoot:!0,A=!!e.fallbackFormat,V=Ze(e.missing)?e.missing:null,v=Ze(e.missing)?au(e.missing):null,b=Ze(e.postTranslation)?e.postTranslation:null,w=n?n.warnHtmlMessage:qe(e.warnHtmlMessage)?e.warnHtmlMessage:!0,S=!!e.escapeParameter;const N=n?n.modifiers:Ae(e.modifiers)?e.modifiers:{};let M=e.pluralRules||n&&n.pluralRules,x;x=(()=>{r&&cc(null);const F={version:Xb,locale:c.value,fallbackLocale:f.value,messages:u.value,modifiers:N,pluralRules:M,missing:v===null?void 0:v,missingWarn:h,fallbackWarn:g,fallbackFormat:A,unresolving:!0,postTranslation:b===null?void 0:b,warnHtmlMessage:w,escapeParameter:S,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};F.datetimeFormats=d.value,F.numberFormats=p.value,F.__datetimeFormatters=Ae(x)?x.__datetimeFormatters:void 0,F.__numberFormatters=Ae(x)?x.__numberFormatters:void 0;const H=rv(F);return r&&cc(H),H})(),hr(x,c.value,f.value);function D(){return[c.value,f.value,u.value,d.value,p.value]}const I=he({get:()=>c.value,set:F=>{c.value=F,x.locale=c.value}}),j=he({get:()=>f.value,set:F=>{f.value=F,x.fallbackLocale=f.value,hr(x,c.value,F)}}),Q=he(()=>u.value),G=he(()=>d.value),U=he(()=>p.value);function X(){return Ze(b)?b:null}function W(F){b=F,x.postTranslation=F}function ie(){return V}function ce(F){F!==null&&(v=au(F)),V=F,x.missing=v}const ge=(F,H,de,pe,Ue,Ct)=>{D();let Ft;try{__INTLIFY_PROD_DEVTOOLS__,r||(x.fallbackContext=n?ov():void 0),Ft=F(x)}finally{__INTLIFY_PROD_DEVTOOLS__,r||(x.fallbackContext=void 0)}if(de!=="translate exists"&&dt(Ft)&&Ft===pl||de==="translate exists"&&!Ft){const[si,ko]=H();return n&&O?pe(n):Ue(si)}else{if(Ct(Ft))return Ft;throw dn(tn.UNEXPECTED_RETURN_TYPE)}};function Ce(...F){return ge(H=>Reflect.apply(pc,null,[H,...F]),()=>_a(...F),"translate",H=>Reflect.apply(H.t,H,[...F]),H=>H,H=>ae(H))}function Re(...F){const[H,de,pe]=F;if(pe&&!Ve(pe))throw dn(tn.INVALID_ARGUMENT);return Ce(H,de,Ot({resolvedMessage:!0},pe||{}))}function Me(...F){return ge(H=>Reflect.apply(hc,null,[H,...F]),()=>ba(...F),"datetime format",H=>Reflect.apply(H.d,H,[...F]),()=>ac,H=>ae(H))}function ze(...F){return ge(H=>Reflect.apply(gc,null,[H,...F]),()=>ya(...F),"number format",H=>Reflect.apply(H.n,H,[...F]),()=>ac,H=>ae(H))}function We(F){return F.map(H=>ae(H)||dt(H)||qe(H)?ou(String(H)):H)}const q={normalize:We,interpolate:F=>F,type:"vnode"};function z(...F){return ge(H=>{let de;const pe=H;try{pe.processor=q,de=Reflect.apply(pc,null,[pe,...F])}finally{pe.processor=null}return de},()=>_a(...F),"translate",H=>H[Na](...F),H=>[ou(H)],H=>ct(H))}function ee(...F){return ge(H=>Reflect.apply(gc,null,[H,...F]),()=>ya(...F),"number format",H=>H[Va](...F),iu,H=>ae(H)||ct(H))}function se(...F){return ge(H=>Reflect.apply(hc,null,[H,...F]),()=>ba(...F),"datetime format",H=>H[Ma](...F),iu,H=>ae(H)||ct(H))}function ke(F){M=F,x.pluralRules=M}function T(F,H){return ge(()=>{if(!F)return!1;const de=ae(H)?H:c.value,pe=J(de),Ue=x.messageResolver(pe,F);return s?Ue!=null:Jo(Ue)||Qt(Ue)||ae(Ue)},()=>[F],"translate exists",de=>Reflect.apply(de.te,de,[F,H]),ey,de=>qe(de))}function k(F){let H=null;const de=Kf(x,f.value,c.value);for(let pe=0;pe<de.length;pe++){const Ue=u.value[de[pe]]||{},Ct=x.messageResolver(Ue,F);if(Ct!=null){H=Ct;break}}return H}function B(F){const H=k(F);return H??(n?n.tm(F)||{}:{})}function J(F){return u.value[F]||{}}function K(F,H){if(i){const de={[F]:H};for(const pe in de)ji(de,pe)&&Gr(de[pe]);H=de[F]}u.value[F]=H,x.messages=u.value}function te(F,H){u.value[F]=u.value[F]||{};const de={[F]:H};if(i)for(const pe in de)ji(de,pe)&&Gr(de[pe]);H=de[F],Ii(H,u.value[F]),x.messages=u.value}function re(F){return d.value[F]||{}}function y(F,H){d.value[F]=H,x.datetimeFormats=d.value,mc(x,F,H)}function C(F,H){d.value[F]=Ot(d.value[F]||{},H),x.datetimeFormats=d.value,mc(x,F,H)}function R(F){return p.value[F]||{}}function Y(F,H){p.value[F]=H,x.numberFormats=p.value,vc(x,F,H)}function oe(F,H){p.value[F]=Ot(p.value[F]||{},H),x.numberFormats=p.value,vc(x,F,H)}lu++,n&&Bi&&(De(n.locale,F=>{a&&(c.value=F,x.locale=F,hr(x,c.value,f.value))}),De(n.fallbackLocale,F=>{a&&(f.value=F,x.fallbackLocale=F,hr(x,c.value,f.value))}));const Z={id:lu,locale:I,fallbackLocale:j,get inheritLocale(){return a},set inheritLocale(F){a=F,F&&n&&(c.value=n.locale.value,f.value=n.fallbackLocale.value,hr(x,c.value,f.value))},get availableLocales(){return Object.keys(u.value).sort()},messages:Q,get modifiers(){return N},get pluralRules(){return M||{}},get isGlobal(){return r},get missingWarn(){return h},set missingWarn(F){h=F,x.missingWarn=h},get fallbackWarn(){return g},set fallbackWarn(F){g=F,x.fallbackWarn=g},get fallbackRoot(){return O},set fallbackRoot(F){O=F},get fallbackFormat(){return A},set fallbackFormat(F){A=F,x.fallbackFormat=A},get warnHtmlMessage(){return w},set warnHtmlMessage(F){w=F,x.warnHtmlMessage=F},get escapeParameter(){return S},set escapeParameter(F){S=F,x.escapeParameter=F},t:Ce,getLocaleMessage:J,setLocaleMessage:K,mergeLocaleMessage:te,getPostTranslationHandler:X,setPostTranslationHandler:W,getMissingHandler:ie,setMissingHandler:ce,[Jb]:ke};return Z.datetimeFormats=G,Z.numberFormats=U,Z.rt=Re,Z.te=T,Z.tm=B,Z.d=Me,Z.n=ze,Z.getDateTimeFormat=re,Z.setDateTimeFormat=y,Z.mergeDateTimeFormat=C,Z.getNumberFormat=R,Z.setNumberFormat=Y,Z.mergeNumberFormat=oe,Z[Qb]=o,Z[Na]=z,Z[Ma]=se,Z[Va]=ee,Z}const xs={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>e==="parent"||e==="global",default:"parent"},i18n:{type:Object}};function ny({slots:e},t){return t.length===1&&t[0]==="default"?(e.default?e.default():[]).reduce((o,r)=>[...o,...r.type===Be?r.children:[r]],[]):t.reduce((n,o)=>{const r=e[o];return r&&(n[o]=r()),n},{})}function Ip(e){return Be}const oy=ue({name:"i18n-t",props:Ot({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>dt(e)||!isNaN(e)}},xs),setup(e,t){const{slots:n,attrs:o}=t,r=e.i18n||ni({useScope:e.scope,__useComponent:!0});return()=>{const i=Object.keys(n).filter(u=>u!=="_"),l={};e.locale&&(l.locale=e.locale),e.plural!==void 0&&(l.plural=ae(e.plural)?+e.plural:e.plural);const s=ny(t,i),a=r[Na](e.keypath,s,l),c=Ot({},o),f=ae(e.tag)||Ve(e.tag)?e.tag:Ip();return ur(f,c,a)}}}),su=oy;function ry(e){return ct(e)&&!ae(e[0])}function xp(e,t,n,o){const{slots:r,attrs:i}=t;return()=>{const l={part:!0};let s={};e.locale&&(l.locale=e.locale),ae(e.format)?l.key=e.format:Ve(e.format)&&(ae(e.format.key)&&(l.key=e.format.key),s=Object.keys(e.format).reduce((d,p)=>n.includes(p)?Ot({},d,{[p]:e.format[p]}):d,{}));const a=o(e.value,l,s);let c=[l.key];ct(a)?c=a.map((d,p)=>{const h=r[d.type],g=h?h({[d.type]:d.value,index:p,parts:a}):[d.value];return ry(g)&&(g[0].key=`${d.type}-${p}`),g}):ae(a)&&(c=[a]);const f=Ot({},i),u=ae(e.tag)||Ve(e.tag)?e.tag:Ip();return ur(u,f,c)}}const iy=ue({name:"i18n-n",props:Ot({value:{type:Number,required:!0},format:{type:[String,Object]}},xs),setup(e,t){const n=e.i18n||ni({useScope:e.scope,__useComponent:!0});return xp(e,t,td,(...o)=>n[Va](...o))}}),cu=iy,ly=ue({name:"i18n-d",props:Ot({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},xs),setup(e,t){const n=e.i18n||ni({useScope:e.scope,__useComponent:!0});return xp(e,t,ed,(...o)=>n[Ma](...o))}}),uu=ly;function ay(e,t){const n=e;if(e.mode==="composition")return n.__getInstance(t)||e.global;{const o=n.__getInstance(t);return o!=null?o.__composer:e.global.__composer}}function sy(e){const t=l=>{const{instance:s,modifiers:a,value:c}=l;if(!s||!s.$)throw dn(tn.UNEXPECTED_ERROR);const f=ay(e,s.$),u=fu(c);return[Reflect.apply(f.t,f,[...du(u)]),f]};return{created:(l,s)=>{const[a,c]=t(s);Bi&&e.global===c&&(l.__i18nWatcher=De(c.locale,()=>{s.instance&&s.instance.$forceUpdate()})),l.__composer=c,l.textContent=a},unmounted:l=>{Bi&&l.__i18nWatcher&&(l.__i18nWatcher(),l.__i18nWatcher=void 0,delete l.__i18nWatcher),l.__composer&&(l.__composer=void 0,delete l.__composer)},beforeUpdate:(l,{value:s})=>{if(l.__composer){const a=l.__composer,c=fu(s);l.textContent=Reflect.apply(a.t,a,[...du(c)])}},getSSRProps:l=>{const[s]=t(l);return{textContent:s}}}}function fu(e){if(ae(e))return{path:e};if(Ae(e)){if(!("path"in e))throw dn(tn.REQUIRED_VALUE,"path");return e}else throw dn(tn.INVALID_VALUE)}function du(e){const{path:t,locale:n,args:o,choice:r,plural:i}=e,l={},s=o||{};return ae(n)&&(l.locale=n),dt(r)&&(l.plural=r),dt(i)&&(l.plural=i),[t,s,l]}function cy(e,t,...n){const o=Ae(n[0])?n[0]:{},r=!!o.useI18nComponentName;(qe(o.globalInstall)?o.globalInstall:!0)&&([r?"i18n":su.name,"I18nT"].forEach(l=>e.component(l,su)),[cu.name,"I18nN"].forEach(l=>e.component(l,cu)),[uu.name,"I18nD"].forEach(l=>e.component(l,uu))),e.directive("t",sy(t))}const uy=oo("global-vue-i18n");function fy(e={},t){const n=qe(e.globalInjection)?e.globalInjection:!0,o=!0,r=new Map,[i,l]=dy(e),s=oo("");function a(u){return r.get(u)||null}function c(u,d){r.set(u,d)}function f(u){r.delete(u)}{const u={get mode(){return"composition"},get allowComposition(){return o},async install(d,...p){if(d.__VUE_I18N_SYMBOL__=s,d.provide(d.__VUE_I18N_SYMBOL__,u),Ae(p[0])){const O=p[0];u.__composerExtend=O.__composerExtend,u.__vueI18nExtend=O.__vueI18nExtend}let h=null;n&&(h=yy(d,u.global)),cy(d,u,...p);const g=d.unmount;d.unmount=()=>{h&&h(),u.dispose(),g()}},get global(){return l},dispose(){i.stop()},__instances:r,__getInstance:a,__setInstance:c,__deleteInstance:f};return u}}function ni(e={}){const t=Et();if(t==null)throw dn(tn.MUST_BE_CALL_SETUP_TOP);if(!t.isCE&&t.appContext.app!=null&&!t.appContext.app.__VUE_I18N_SYMBOL__)throw dn(tn.NOT_INSTALLED);const n=py(t),o=my(n),r=Pp(t),i=hy(e,r);if(i==="global")return Zb(o,e,r),o;if(i==="parent"){let a=gy(n,t,e.__useComponent);return a==null&&(a=o),a}const l=n;let s=l.__getInstance(t);if(s==null){const a=Ot({},e);"__i18n"in r&&(a.__i18n=r.__i18n),o&&(a.__root=o),s=Ap(a),l.__composerExtend&&(s[$a]=l.__composerExtend(s)),_y(l,t,s),l.__setInstance(t,s)}return s}function dy(e,t,n){const o=pd();{const r=o.run(()=>Ap(e));if(r==null)throw dn(tn.UNEXPECTED_ERROR);return[o,r]}}function py(e){{const t=yt(e.isCE?uy:e.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw dn(e.isCE?tn.NOT_INSTALLED_WITH_PROVIDE:tn.UNEXPECTED_ERROR);return t}}function hy(e,t){return fl(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}function my(e){return e.mode==="composition"?e.global:e.global.__composer}function gy(e,t,n=!1){let o=null;const r=t.root;let i=vy(t,n);for(;i!=null;){const l=e;if(e.mode==="composition"&&(o=l.__getInstance(i)),o!=null||r===i)break;i=i.parent}return o}function vy(e,t=!1){return e==null?null:t&&e.vnode.ctx||e.parent}function _y(e,t,n){Pt(()=>{},t),wo(()=>{const o=n;e.__deleteInstance(t);const r=o[$a];r&&(r(),delete o[$a])},t)}const by=["locale","fallbackLocale","availableLocales"],pu=["t","rt","d","n","tm","te"];function yy(e,t){const n=Object.create(null);return by.forEach(r=>{const i=Object.getOwnPropertyDescriptor(t,r);if(!i)throw dn(tn.UNEXPECTED_ERROR);const l=Ge(i.value)?{get(){return i.value.value},set(s){i.value.value=s}}:{get(){return i.get&&i.get()}};Object.defineProperty(n,r,l)}),e.config.globalProperties.$i18n=n,pu.forEach(r=>{const i=Object.getOwnPropertyDescriptor(t,r);if(!i||!i.value)throw dn(tn.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${r}`,i)}),()=>{delete e.config.globalProperties.$i18n,pu.forEach(r=>{delete e.config.globalProperties[`$${r}`]})}}qb();Qg(uv);Zg(Dg);ev(Kf);if(__INTLIFY_PROD_DEVTOOLS__){const e=ts();e.__INTLIFY__=!0,jg(e.__INTLIFY_DEVTOOLS_GLOBAL_HOOK__)}const Zi=fy({legacy:!1,locale:"",messages:{}}),Lp=Object.fromEntries(Object.entries(Object.assign({"../../locales/ar.yml":()=>Se(()=>Promise.resolve().then(()=>lS),void 0),"../../locales/de.yml":()=>Se(()=>Promise.resolve().then(()=>sS),void 0),"../../locales/en.yml":()=>Se(()=>Promise.resolve().then(()=>uS),void 0),"../../locales/es.yml":()=>Se(()=>Promise.resolve().then(()=>dS),void 0),"../../locales/fr.yml":()=>Se(()=>Promise.resolve().then(()=>hS),void 0),"../../locales/id.yml":()=>Se(()=>Promise.resolve().then(()=>gS),void 0),"../../locales/it.yml":()=>Se(()=>Promise.resolve().then(()=>_S),void 0),"../../locales/ja.yml":()=>Se(()=>Promise.resolve().then(()=>yS),void 0),"../../locales/ka.yml":()=>Se(()=>Promise.resolve().then(()=>SS),void 0),"../../locales/ko.yml":()=>Se(()=>Promise.resolve().then(()=>TS),void 0),"../../locales/pl.yml":()=>Se(()=>Promise.resolve().then(()=>kS),void 0),"../../locales/pt-BR.yml":()=>Se(()=>Promise.resolve().then(()=>PS),void 0),"../../locales/ru.yml":()=>Se(()=>Promise.resolve().then(()=>IS),void 0),"../../locales/tr.yml":()=>Se(()=>Promise.resolve().then(()=>LS),void 0),"../../locales/uk.yml":()=>Se(()=>Promise.resolve().then(()=>RS),void 0),"../../locales/uz.yml":()=>Se(()=>Promise.resolve().then(()=>MS),void 0),"../../locales/vi.yml":()=>Se(()=>Promise.resolve().then(()=>$S),void 0),"../../locales/zh-CN.yml":()=>Se(()=>Promise.resolve().then(()=>BS),void 0)})).map(([e,t])=>{var n;return[(n=e.match(/([\w-]*)\.yml$/))==null?void 0:n[1],t]})),Ey=Object.keys(Lp),hu=[];function Kl(e){var t;return Zi.global.locale.value=e,typeof document<"u"&&((t=document.querySelector("html"))==null||t.setAttribute("lang",e)),e}async function Dp(e){if(Zi.global.locale.value===e||hu.includes(e))return Kl(e);const t=await Lp[e]();return Zi.global.setLocaleMessage(e,t.default),hu.push(e),Kl(e)}const Sy=({app:e})=>{e.use(Zi),Dp("en")},Cy=Object.freeze(Object.defineProperty({__proto__:null,availableLocales:Ey,install:Sy,loadLanguageAsync:Dp},Symbol.toStringTag,{value:"Module"}));var Rp=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Np(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Mp={exports:{}};/* NProgress, (c) 2013, 2014 Rico Sta. Cruz - http://ricostacruz.com/nprogress
 * @license MIT */(function(e,t){(function(n,o){e.exports=o()})(Rp,function(){var n={};n.version="0.2.0";var o=n.settings={minimum:.08,easing:"ease",positionUsing:"",speed:200,trickle:!0,trickleRate:.02,trickleSpeed:800,showSpinner:!0,barSelector:'[role="bar"]',spinnerSelector:'[role="spinner"]',parent:"body",template:'<div class="bar" role="bar"><div class="peg"></div></div><div class="spinner" role="spinner"><div class="spinner-icon"></div></div>'};n.configure=function(h){var g,O;for(g in h)O=h[g],O!==void 0&&h.hasOwnProperty(g)&&(o[g]=O);return this},n.status=null,n.set=function(h){var g=n.isStarted();h=r(h,o.minimum,1),n.status=h===1?null:h;var O=n.render(!g),A=O.querySelector(o.barSelector),V=o.speed,v=o.easing;return O.offsetWidth,s(function(b){o.positionUsing===""&&(o.positionUsing=n.getPositioningCSS()),a(A,l(h,V,v)),h===1?(a(O,{transition:"none",opacity:1}),O.offsetWidth,setTimeout(function(){a(O,{transition:"all "+V+"ms linear",opacity:0}),setTimeout(function(){n.remove(),b()},V)},V)):setTimeout(b,V)}),this},n.isStarted=function(){return typeof n.status=="number"},n.start=function(){n.status||n.set(0);var h=function(){setTimeout(function(){n.status&&(n.trickle(),h())},o.trickleSpeed)};return o.trickle&&h(),this},n.done=function(h){return!h&&!n.status?this:n.inc(.3+.5*Math.random()).set(1)},n.inc=function(h){var g=n.status;return g?(typeof h!="number"&&(h=(1-g)*r(Math.random()*g,.1,.95)),g=r(g+h,0,.994),n.set(g)):n.start()},n.trickle=function(){return n.inc(Math.random()*o.trickleRate)},function(){var h=0,g=0;n.promise=function(O){return!O||O.state()==="resolved"?this:(g===0&&n.start(),h++,g++,O.always(function(){g--,g===0?(h=0,n.done()):n.set((h-g)/h)}),this)}}(),n.render=function(h){if(n.isRendered())return document.getElementById("nprogress");f(document.documentElement,"nprogress-busy");var g=document.createElement("div");g.id="nprogress",g.innerHTML=o.template;var O=g.querySelector(o.barSelector),A=h?"-100":i(n.status||0),V=document.querySelector(o.parent),v;return a(O,{transition:"all 0 linear",transform:"translate3d("+A+"%,0,0)"}),o.showSpinner||(v=g.querySelector(o.spinnerSelector),v&&p(v)),V!=document.body&&f(V,"nprogress-custom-parent"),V.appendChild(g),g},n.remove=function(){u(document.documentElement,"nprogress-busy"),u(document.querySelector(o.parent),"nprogress-custom-parent");var h=document.getElementById("nprogress");h&&p(h)},n.isRendered=function(){return!!document.getElementById("nprogress")},n.getPositioningCSS=function(){var h=document.body.style,g="WebkitTransform"in h?"Webkit":"MozTransform"in h?"Moz":"msTransform"in h?"ms":"OTransform"in h?"O":"";return g+"Perspective"in h?"translate3d":g+"Transform"in h?"translate":"margin"};function r(h,g,O){return h<g?g:h>O?O:h}function i(h){return(-1+h)*100}function l(h,g,O){var A;return o.positionUsing==="translate3d"?A={transform:"translate3d("+i(h)+"%,0,0)"}:o.positionUsing==="translate"?A={transform:"translate("+i(h)+"%,0)"}:A={"margin-left":i(h)+"%"},A.transition="all "+g+"ms "+O,A}var s=function(){var h=[];function g(){var O=h.shift();O&&O(g)}return function(O){h.push(O),h.length==1&&g()}}(),a=function(){var h=["Webkit","O","Moz","ms"],g={};function O(b){return b.replace(/^-ms-/,"ms-").replace(/-([\da-z])/gi,function(w,S){return S.toUpperCase()})}function A(b){var w=document.body.style;if(b in w)return b;for(var S=h.length,N=b.charAt(0).toUpperCase()+b.slice(1),M;S--;)if(M=h[S]+N,M in w)return M;return b}function V(b){return b=O(b),g[b]||(g[b]=A(b))}function v(b,w,S){w=V(w),b.style[w]=S}return function(b,w){var S=arguments,N,M;if(S.length==2)for(N in w)M=w[N],M!==void 0&&w.hasOwnProperty(N)&&v(b,N,M);else v(b,S[1],S[2])}}();function c(h,g){var O=typeof h=="string"?h:d(h);return O.indexOf(" "+g+" ")>=0}function f(h,g){var O=d(h),A=O+g;c(O,g)||(h.className=A.substring(1))}function u(h,g){var O=d(h),A;c(h,g)&&(A=O.replace(" "+g+" "," "),h.className=A.substring(1,A.length-1))}function d(h){return(" "+(h.className||"")+" ").replace(/\s+/gi," ")}function p(h){h&&h.parentNode&&h.parentNode.removeChild(h)}return n})})(Mp);var Ty=Mp.exports;const mu=Np(Ty),wy=({isClient:e,router:t})=>{e&&(t.beforeEach((n,o)=>{n.path!==o.path&&mu.start()}),t.afterEach(()=>{mu.done()}))},ky=Object.freeze(Object.defineProperty({__proto__:null,install:wy},Symbol.toStringTag,{value:"Module"}));var Oy=!1;/*!
 * pinia v2.2.2
 * (c) 2024 Eduardo San Martin Morote
 * @license MIT
 */const Py=Symbol();var gu;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(gu||(gu={}));function Ay(){const e=pd(!0),t=e.run(()=>fe({}));let n=[],o=[];const r=Pd({install(i){r._a=i,i.provide(Py,r),i.config.globalProperties.$pinia=r,o.forEach(l=>n.push(l)),o=[]},use(i){return!this._a&&!Oy?o.push(i):n.push(i),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return r}const Iy=({isClient:e,initialState:t,app:n})=>{const o=Ay();n.use(o),e?o.state.value=t.pinia||{}:t.pinia=o.state.value},xy=Object.freeze(Object.defineProperty({__proto__:null,install:Iy},Symbol.toStringTag,{value:"Module"})),Ly=({isClient:e,router:t})=>{e&&t.isReady().then(async()=>{const{registerSW:n}=await Se(async()=>{const{registerSW:o}=await Promise.resolve().then(()=>jS);return{registerSW:o}},void 0);n({immediate:!0})}).catch(()=>{})},Dy=Object.freeze(Object.defineProperty({__proto__:null,install:Ly},Symbol.toStringTag,{value:"Module"}));/*!
  * vue-router v4.4.3
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */const Ro=typeof document<"u";function Ry(e){return e.__esModule||e[Symbol.toStringTag]==="Module"}const $e=Object.assign;function Yl(e,t){const n={};for(const o in t){const r=t[o];n[o]=pn(r)?r.map(e):e(r)}return n}const xr=()=>{},pn=Array.isArray,Vp=/#/g,Ny=/&/g,My=/\//g,Vy=/=/g,$y=/\?/g,$p=/\+/g,Fy=/%5B/g,By=/%5D/g,Fp=/%5E/g,Uy=/%60/g,Bp=/%7B/g,jy=/%7C/g,Up=/%7D/g,Hy=/%20/g;function Ls(e){return encodeURI(""+e).replace(jy,"|").replace(Fy,"[").replace(By,"]")}function Wy(e){return Ls(e).replace(Bp,"{").replace(Up,"}").replace(Fp,"^")}function Fa(e){return Ls(e).replace($p,"%2B").replace(Hy,"+").replace(Vp,"%23").replace(Ny,"%26").replace(Uy,"`").replace(Bp,"{").replace(Up,"}").replace(Fp,"^")}function Gy(e){return Fa(e).replace(Vy,"%3D")}function Ky(e){return Ls(e).replace(Vp,"%23").replace($y,"%3F")}function Yy(e){return e==null?"":Ky(e).replace(My,"%2F")}function Kr(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const zy=/\/$/,Xy=e=>e.replace(zy,"");function zl(e,t,n="/"){let o,r={},i="",l="";const s=t.indexOf("#");let a=t.indexOf("?");return s<a&&s>=0&&(a=-1),a>-1&&(o=t.slice(0,a),i=t.slice(a+1,s>-1?s:t.length),r=e(i)),s>-1&&(o=o||t.slice(0,s),l=t.slice(s,t.length)),o=Zy(o??t,n),{fullPath:o+(i&&"?")+i+l,path:o,query:r,hash:Kr(l)}}function qy(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function vu(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Jy(e,t,n){const o=t.matched.length-1,r=n.matched.length-1;return o>-1&&o===r&&tr(t.matched[o],n.matched[r])&&jp(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function tr(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function jp(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Qy(e[n],t[n]))return!1;return!0}function Qy(e,t){return pn(e)?_u(e,t):pn(t)?_u(t,e):e===t}function _u(e,t){return pn(t)?e.length===t.length&&e.every((n,o)=>n===t[o]):e.length===1&&e[0]===t}function Zy(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),o=e.split("/"),r=o[o.length-1];(r===".."||r===".")&&o.push("");let i=n.length-1,l,s;for(l=0;l<o.length;l++)if(s=o[l],s!==".")if(s==="..")i>1&&i--;else break;return n.slice(0,i).join("/")+"/"+o.slice(l).join("/")}const Fn={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var nr;(function(e){e.pop="pop",e.push="push"})(nr||(nr={}));var Co;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Co||(Co={}));const Xl="";function Hp(e){if(!e)if(Ro){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Xy(e)}const e0=/^[^#]+#/;function Wp(e,t){return e.replace(e0,"#")+t}function t0(e,t){const n=document.documentElement.getBoundingClientRect(),o=e.getBoundingClientRect();return{behavior:t.behavior,left:o.left-n.left-(t.left||0),top:o.top-n.top-(t.top||0)}}const Ol=()=>({left:window.scrollX,top:window.scrollY});function n0(e){let t;if("el"in e){const n=e.el,o=typeof n=="string"&&n.startsWith("#"),r=typeof n=="string"?o?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=t0(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function bu(e,t){return(history.state?history.state.position-t:-1)+e}const Ba=new Map;function o0(e,t){Ba.set(e,t)}function r0(e){const t=Ba.get(e);return Ba.delete(e),t}let i0=()=>location.protocol+"//"+location.host;function Gp(e,t){const{pathname:n,search:o,hash:r}=t,i=e.indexOf("#");if(i>-1){let s=r.includes(e.slice(i))?e.slice(i).length:1,a=r.slice(s);return a[0]!=="/"&&(a="/"+a),vu(a,"")}return vu(n,e)+o+r}function l0(e,t,n,o){let r=[],i=[],l=null;const s=({state:d})=>{const p=Gp(e,location),h=n.value,g=t.value;let O=0;if(d){if(n.value=p,t.value=d,l&&l===h){l=null;return}O=g?d.position-g.position:0}else o(p);r.forEach(A=>{A(n.value,h,{delta:O,type:nr.pop,direction:O?O>0?Co.forward:Co.back:Co.unknown})})};function a(){l=n.value}function c(d){r.push(d);const p=()=>{const h=r.indexOf(d);h>-1&&r.splice(h,1)};return i.push(p),p}function f(){const{history:d}=window;d.state&&d.replaceState($e({},d.state,{scroll:Ol()}),"")}function u(){for(const d of i)d();i=[],window.removeEventListener("popstate",s),window.removeEventListener("beforeunload",f)}return window.addEventListener("popstate",s),window.addEventListener("beforeunload",f,{passive:!0}),{pauseListeners:a,listen:c,destroy:u}}function yu(e,t,n,o=!1,r=!1){return{back:e,current:t,forward:n,replaced:o,position:window.history.length,scroll:r?Ol():null}}function a0(e){const{history:t,location:n}=window,o={value:Gp(e,n)},r={value:t.state};r.value||i(o.value,{back:null,current:o.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function i(a,c,f){const u=e.indexOf("#"),d=u>-1?(n.host&&document.querySelector("base")?e:e.slice(u))+a:i0()+e+a;try{t[f?"replaceState":"pushState"](c,"",d),r.value=c}catch(p){console.error(p),n[f?"replace":"assign"](d)}}function l(a,c){const f=$e({},t.state,yu(r.value.back,a,r.value.forward,!0),c,{position:r.value.position});i(a,f,!0),o.value=a}function s(a,c){const f=$e({},r.value,t.state,{forward:a,scroll:Ol()});i(f.current,f,!0);const u=$e({},yu(o.value,a,null),{position:f.position+1},c);i(a,u,!1),o.value=a}return{location:o,state:r,push:s,replace:l}}function Kp(e){e=Hp(e);const t=a0(e),n=l0(e,t.state,t.location,t.replace);function o(i,l=!0){l||n.pauseListeners(),history.go(i)}const r=$e({location:"",base:e,go:o,createHref:Wp.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function s0(e=""){let t=[],n=[Xl],o=0;e=Hp(e);function r(s){o++,o!==n.length&&n.splice(o),n.push(s)}function i(s,a,{direction:c,delta:f}){const u={direction:c,delta:f,type:nr.pop};for(const d of t)d(s,a,u)}const l={location:Xl,state:{},base:e,createHref:Wp.bind(null,e),replace(s){n.splice(o--,1),r(s)},push(s,a){r(s)},listen(s){return t.push(s),()=>{const a=t.indexOf(s);a>-1&&t.splice(a,1)}},destroy(){t=[],n=[Xl],o=0},go(s,a=!0){const c=this.location,f=s<0?Co.back:Co.forward;o=Math.max(0,Math.min(o+s,n.length-1)),a&&i(this.location,c,{direction:f,delta:s})}};return Object.defineProperty(l,"location",{enumerable:!0,get:()=>n[o]}),l}function c0(e){return e=location.host?e||location.pathname+location.search:"",e.includes("#")||(e+="#"),Kp(e)}function u0(e){return typeof e=="string"||e&&typeof e=="object"}function Yp(e){return typeof e=="string"||typeof e=="symbol"}const zp=Symbol("");var Eu;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Eu||(Eu={}));function or(e,t){return $e(new Error,{type:e,[zp]:!0},t)}function Cn(e,t){return e instanceof Error&&zp in e&&(t==null||!!(e.type&t))}const Su="[^/]+?",f0={sensitive:!1,strict:!1,start:!0,end:!0},d0=/[.+*?^${}()[\]/\\]/g;function p0(e,t){const n=$e({},f0,t),o=[];let r=n.start?"^":"";const i=[];for(const c of e){const f=c.length?[]:[90];n.strict&&!c.length&&(r+="/");for(let u=0;u<c.length;u++){const d=c[u];let p=40+(n.sensitive?.25:0);if(d.type===0)u||(r+="/"),r+=d.value.replace(d0,"\\$&"),p+=40;else if(d.type===1){const{value:h,repeatable:g,optional:O,regexp:A}=d;i.push({name:h,repeatable:g,optional:O});const V=A||Su;if(V!==Su){p+=10;try{new RegExp(`(${V})`)}catch(b){throw new Error(`Invalid custom RegExp for param "${h}" (${V}): `+b.message)}}let v=g?`((?:${V})(?:/(?:${V}))*)`:`(${V})`;u||(v=O&&c.length<2?`(?:/${v})`:"/"+v),O&&(v+="?"),r+=v,p+=20,O&&(p+=-8),g&&(p+=-20),V===".*"&&(p+=-50)}f.push(p)}o.push(f)}if(n.strict&&n.end){const c=o.length-1;o[c][o[c].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&(r+="(?:/|$)");const l=new RegExp(r,n.sensitive?"":"i");function s(c){const f=c.match(l),u={};if(!f)return null;for(let d=1;d<f.length;d++){const p=f[d]||"",h=i[d-1];u[h.name]=p&&h.repeatable?p.split("/"):p}return u}function a(c){let f="",u=!1;for(const d of e){(!u||!f.endsWith("/"))&&(f+="/"),u=!1;for(const p of d)if(p.type===0)f+=p.value;else if(p.type===1){const{value:h,repeatable:g,optional:O}=p,A=h in c?c[h]:"";if(pn(A)&&!g)throw new Error(`Provided param "${h}" is an array but it is not repeatable (* or + modifiers)`);const V=pn(A)?A.join("/"):A;if(!V)if(O)d.length<2&&(f.endsWith("/")?f=f.slice(0,-1):u=!0);else throw new Error(`Missing required param "${h}"`);f+=V}}return f||"/"}return{re:l,score:o,keys:i,parse:s,stringify:a}}function h0(e,t){let n=0;for(;n<e.length&&n<t.length;){const o=t[n]-e[n];if(o)return o;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Xp(e,t){let n=0;const o=e.score,r=t.score;for(;n<o.length&&n<r.length;){const i=h0(o[n],r[n]);if(i)return i;n++}if(Math.abs(r.length-o.length)===1){if(Cu(o))return 1;if(Cu(r))return-1}return r.length-o.length}function Cu(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const m0={type:0,value:""},g0=/[a-zA-Z0-9_]/;function v0(e){if(!e)return[[]];if(e==="/")return[[m0]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(p){throw new Error(`ERR (${n})/"${c}": ${p}`)}let n=0,o=n;const r=[];let i;function l(){i&&r.push(i),i=[]}let s=0,a,c="",f="";function u(){c&&(n===0?i.push({type:0,value:c}):n===1||n===2||n===3?(i.length>1&&(a==="*"||a==="+")&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:c,regexp:f,repeatable:a==="*"||a==="+",optional:a==="*"||a==="?"})):t("Invalid state to consume buffer"),c="")}function d(){c+=a}for(;s<e.length;){if(a=e[s++],a==="\\"&&n!==2){o=n,n=4;continue}switch(n){case 0:a==="/"?(c&&u(),l()):a===":"?(u(),n=1):d();break;case 4:d(),n=o;break;case 1:a==="("?n=2:g0.test(a)?d():(u(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&s--);break;case 2:a===")"?f[f.length-1]=="\\"?f=f.slice(0,-1)+a:n=3:f+=a;break;case 3:u(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&s--,f="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${c}"`),u(),l(),r}function _0(e,t,n){const o=p0(v0(e.path),n),r=$e(o,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function b0(e,t){const n=[],o=new Map;t=ku({strict:!1,end:!0,sensitive:!1},t);function r(u){return o.get(u)}function i(u,d,p){const h=!p,g=y0(u);g.aliasOf=p&&p.record;const O=ku(t,u),A=[g];if("alias"in u){const b=typeof u.alias=="string"?[u.alias]:u.alias;for(const w of b)A.push($e({},g,{components:p?p.record.components:g.components,path:w,aliasOf:p?p.record:g}))}let V,v;for(const b of A){const{path:w}=b;if(d&&w[0]!=="/"){const S=d.record.path,N=S[S.length-1]==="/"?"":"/";b.path=d.record.path+(w&&N+w)}if(V=_0(b,d,O),p?p.alias.push(V):(v=v||V,v!==V&&v.alias.push(V),h&&u.name&&!wu(V)&&l(u.name)),qp(V)&&a(V),g.children){const S=g.children;for(let N=0;N<S.length;N++)i(S[N],V,p&&p.children[N])}p=p||V}return v?()=>{l(v)}:xr}function l(u){if(Yp(u)){const d=o.get(u);d&&(o.delete(u),n.splice(n.indexOf(d),1),d.children.forEach(l),d.alias.forEach(l))}else{const d=n.indexOf(u);d>-1&&(n.splice(d,1),u.record.name&&o.delete(u.record.name),u.children.forEach(l),u.alias.forEach(l))}}function s(){return n}function a(u){const d=C0(u,n);n.splice(d,0,u),u.record.name&&!wu(u)&&o.set(u.record.name,u)}function c(u,d){let p,h={},g,O;if("name"in u&&u.name){if(p=o.get(u.name),!p)throw or(1,{location:u});O=p.record.name,h=$e(Tu(d.params,p.keys.filter(v=>!v.optional).concat(p.parent?p.parent.keys.filter(v=>v.optional):[]).map(v=>v.name)),u.params&&Tu(u.params,p.keys.map(v=>v.name))),g=p.stringify(h)}else if(u.path!=null)g=u.path,p=n.find(v=>v.re.test(g)),p&&(h=p.parse(g),O=p.record.name);else{if(p=d.name?o.get(d.name):n.find(v=>v.re.test(d.path)),!p)throw or(1,{location:u,currentLocation:d});O=p.record.name,h=$e({},d.params,u.params),g=p.stringify(h)}const A=[];let V=p;for(;V;)A.unshift(V.record),V=V.parent;return{name:O,path:g,params:h,matched:A,meta:S0(A)}}e.forEach(u=>i(u));function f(){n.length=0,o.clear()}return{addRoute:i,resolve:c,removeRoute:l,clearRoutes:f,getRoutes:s,getRecordMatcher:r}}function Tu(e,t){const n={};for(const o of t)o in e&&(n[o]=e[o]);return n}function y0(e){return{path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:void 0,beforeEnter:e.beforeEnter,props:E0(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}}}function E0(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const o in e.components)t[o]=typeof n=="object"?n[o]:n;return t}function wu(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function S0(e){return e.reduce((t,n)=>$e(t,n.meta),{})}function ku(e,t){const n={};for(const o in e)n[o]=o in t?t[o]:e[o];return n}function C0(e,t){let n=0,o=t.length;for(;n!==o;){const i=n+o>>1;Xp(e,t[i])<0?o=i:n=i+1}const r=T0(e);return r&&(o=t.lastIndexOf(r,o-1)),o}function T0(e){let t=e;for(;t=t.parent;)if(qp(t)&&Xp(e,t)===0)return t}function qp({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function w0(e){const t={};if(e===""||e==="?")return t;const o=(e[0]==="?"?e.slice(1):e).split("&");for(let r=0;r<o.length;++r){const i=o[r].replace($p," "),l=i.indexOf("="),s=Kr(l<0?i:i.slice(0,l)),a=l<0?null:Kr(i.slice(l+1));if(s in t){let c=t[s];pn(c)||(c=t[s]=[c]),c.push(a)}else t[s]=a}return t}function Ou(e){let t="";for(let n in e){const o=e[n];if(n=Gy(n),o==null){o!==void 0&&(t+=(t.length?"&":"")+n);continue}(pn(o)?o.map(i=>i&&Fa(i)):[o&&Fa(o)]).forEach(i=>{i!==void 0&&(t+=(t.length?"&":"")+n,i!=null&&(t+="="+i))})}return t}function k0(e){const t={};for(const n in e){const o=e[n];o!==void 0&&(t[n]=pn(o)?o.map(r=>r==null?null:""+r):o==null?o:""+o)}return t}const O0=Symbol(""),Pu=Symbol(""),Pl=Symbol(""),Ds=Symbol(""),Ua=Symbol("");function vr(){let e=[];function t(o){return e.push(o),()=>{const r=e.indexOf(o);r>-1&&e.splice(r,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function Wn(e,t,n,o,r,i=l=>l()){const l=o&&(o.enterCallbacks[r]=o.enterCallbacks[r]||[]);return()=>new Promise((s,a)=>{const c=d=>{d===!1?a(or(4,{from:n,to:t})):d instanceof Error?a(d):u0(d)?a(or(2,{from:t,to:d})):(l&&o.enterCallbacks[r]===l&&typeof d=="function"&&l.push(d),s())},f=i(()=>e.call(o&&o.instances[r],t,n,c));let u=Promise.resolve(f);e.length<3&&(u=u.then(c)),u.catch(d=>a(d))})}function ql(e,t,n,o,r=i=>i()){const i=[];for(const l of e)for(const s in l.components){let a=l.components[s];if(!(t!=="beforeRouteEnter"&&!l.instances[s]))if(P0(a)){const f=(a.__vccOpts||a)[t];f&&i.push(Wn(f,n,o,l,s,r))}else{let c=a();i.push(()=>c.then(f=>{if(!f)return Promise.reject(new Error(`Couldn't resolve component "${s}" at "${l.path}"`));const u=Ry(f)?f.default:f;l.components[s]=u;const p=(u.__vccOpts||u)[t];return p&&Wn(p,n,o,l,s,r)()}))}}return i}function P0(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Au(e){const t=yt(Pl),n=yt(Ds),o=he(()=>{const a=E(e.to);return t.resolve(a)}),r=he(()=>{const{matched:a}=o.value,{length:c}=a,f=a[c-1],u=n.matched;if(!f||!u.length)return-1;const d=u.findIndex(tr.bind(null,f));if(d>-1)return d;const p=Iu(a[c-2]);return c>1&&Iu(f)===p&&u[u.length-1].path!==p?u.findIndex(tr.bind(null,a[c-2])):d}),i=he(()=>r.value>-1&&L0(n.params,o.value.params)),l=he(()=>r.value>-1&&r.value===n.matched.length-1&&jp(n.params,o.value.params));function s(a={}){return x0(a)?t[E(e.replace)?"replace":"push"](E(e.to)).catch(xr):Promise.resolve()}return{route:o,href:he(()=>o.value.href),isActive:i,isExactActive:l,navigate:s}}const A0=ue({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:Au,setup(e,{slots:t}){const n=un(Au(e)),{options:o}=yt(Pl),r=he(()=>({[xu(e.activeClass,o.linkActiveClass,"router-link-active")]:n.isActive,[xu(e.exactActiveClass,o.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const i=t.default&&t.default(n);return e.custom?i:ur("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},i)}}}),I0=A0;function x0(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function L0(e,t){for(const n in t){const o=t[n],r=e[n];if(typeof o=="string"){if(o!==r)return!1}else if(!pn(r)||r.length!==o.length||o.some((i,l)=>i!==r[l]))return!1}return!0}function Iu(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const xu=(e,t,n)=>e??t??n,D0=ue({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const o=yt(Ua),r=he(()=>e.route||o.value),i=yt(Pu,0),l=he(()=>{let c=E(i);const{matched:f}=r.value;let u;for(;(u=f[c])&&!u.components;)c++;return c}),s=he(()=>r.value.matched[l.value]);Qn(Pu,he(()=>l.value+1)),Qn(O0,s),Qn(Ua,r);const a=fe();return De(()=>[a.value,s.value,e.name],([c,f,u],[d,p,h])=>{f&&(f.instances[u]=c,p&&p!==f&&c&&c===d&&(f.leaveGuards.size||(f.leaveGuards=p.leaveGuards),f.updateGuards.size||(f.updateGuards=p.updateGuards))),c&&f&&(!p||!tr(f,p)||!d)&&(f.enterCallbacks[u]||[]).forEach(g=>g(c))},{flush:"post"}),()=>{const c=r.value,f=e.name,u=s.value,d=u&&u.components[f];if(!d)return Lu(n.default,{Component:d,route:c});const p=u.props[f],h=p?p===!0?c.params:typeof p=="function"?p(c):p:null,O=ur(d,$e({},h,t,{onVnodeUnmounted:A=>{A.component.isUnmounted&&(u.instances[f]=null)},ref:a}));return Lu(n.default,{Component:O,route:c})||O}}});function Lu(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const R0=D0;function N0(e){const t=b0(e.routes,e),n=e.parseQuery||w0,o=e.stringifyQuery||Ou,r=e.history,i=vr(),l=vr(),s=vr(),a=ys(Fn);let c=Fn;Ro&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const f=Yl.bind(null,P=>""+P),u=Yl.bind(null,Yy),d=Yl.bind(null,Kr);function p(P,q){let z,ee;return Yp(P)?(z=t.getRecordMatcher(P),ee=q):ee=P,t.addRoute(ee,z)}function h(P){const q=t.getRecordMatcher(P);q&&t.removeRoute(q)}function g(){return t.getRoutes().map(P=>P.record)}function O(P){return!!t.getRecordMatcher(P)}function A(P,q){if(q=$e({},q||a.value),typeof P=="string"){const k=zl(n,P,q.path),B=t.resolve({path:k.path},q),J=r.createHref(k.fullPath);return $e(k,B,{params:d(B.params),hash:Kr(k.hash),redirectedFrom:void 0,href:J})}let z;if(P.path!=null)z=$e({},P,{path:zl(n,P.path,q.path).path});else{const k=$e({},P.params);for(const B in k)k[B]==null&&delete k[B];z=$e({},P,{params:u(k)}),q.params=u(q.params)}const ee=t.resolve(z,q),se=P.hash||"";ee.params=f(d(ee.params));const ke=qy(o,$e({},P,{hash:Wy(se),path:ee.path})),T=r.createHref(ke);return $e({fullPath:ke,hash:se,query:o===Ou?k0(P.query):P.query||{}},ee,{redirectedFrom:void 0,href:T})}function V(P){return typeof P=="string"?zl(n,P,a.value.path):$e({},P)}function v(P,q){if(c!==P)return or(8,{from:q,to:P})}function b(P){return N(P)}function w(P){return b($e(V(P),{replace:!0}))}function S(P){const q=P.matched[P.matched.length-1];if(q&&q.redirect){const{redirect:z}=q;let ee=typeof z=="function"?z(P):z;return typeof ee=="string"&&(ee=ee.includes("?")||ee.includes("#")?ee=V(ee):{path:ee},ee.params={}),$e({query:P.query,hash:P.hash,params:ee.path!=null?{}:P.params},ee)}}function N(P,q){const z=c=A(P),ee=a.value,se=P.state,ke=P.force,T=P.replace===!0,k=S(z);if(k)return N($e(V(k),{state:typeof k=="object"?$e({},se,k.state):se,force:ke,replace:T}),q||z);const B=z;B.redirectedFrom=q;let J;return!ke&&Jy(o,ee,z)&&(J=or(16,{to:B,from:ee}),ge(ee,ee,!0,!1)),(J?Promise.resolve(J):L(B,ee)).catch(K=>Cn(K)?Cn(K,2)?K:ce(K):W(K,B,ee)).then(K=>{if(K){if(Cn(K,2))return N($e({replace:T},V(K.to),{state:typeof K.to=="object"?$e({},se,K.to.state):se,force:ke}),q||B)}else K=I(B,ee,!0,T,se);return D(B,ee,K),K})}function M(P,q){const z=v(P,q);return z?Promise.reject(z):Promise.resolve()}function x(P){const q=Me.values().next().value;return q&&typeof q.runWithContext=="function"?q.runWithContext(P):P()}function L(P,q){let z;const[ee,se,ke]=M0(P,q);z=ql(ee.reverse(),"beforeRouteLeave",P,q);for(const k of ee)k.leaveGuards.forEach(B=>{z.push(Wn(B,P,q))});const T=M.bind(null,P,q);return z.push(T),We(z).then(()=>{z=[];for(const k of i.list())z.push(Wn(k,P,q));return z.push(T),We(z)}).then(()=>{z=ql(se,"beforeRouteUpdate",P,q);for(const k of se)k.updateGuards.forEach(B=>{z.push(Wn(B,P,q))});return z.push(T),We(z)}).then(()=>{z=[];for(const k of ke)if(k.beforeEnter)if(pn(k.beforeEnter))for(const B of k.beforeEnter)z.push(Wn(B,P,q));else z.push(Wn(k.beforeEnter,P,q));return z.push(T),We(z)}).then(()=>(P.matched.forEach(k=>k.enterCallbacks={}),z=ql(ke,"beforeRouteEnter",P,q,x),z.push(T),We(z))).then(()=>{z=[];for(const k of l.list())z.push(Wn(k,P,q));return z.push(T),We(z)}).catch(k=>Cn(k,8)?k:Promise.reject(k))}function D(P,q,z){s.list().forEach(ee=>x(()=>ee(P,q,z)))}function I(P,q,z,ee,se){const ke=v(P,q);if(ke)return ke;const T=q===Fn,k=Ro?history.state:{};z&&(ee||T?r.replace(P.fullPath,$e({scroll:T&&k&&k.scroll},se)):r.push(P.fullPath,se)),a.value=P,ge(P,q,z,T),ce()}let j;function Q(){j||(j=r.listen((P,q,z)=>{if(!ze.listening)return;const ee=A(P),se=S(ee);if(se){N($e(se,{replace:!0}),ee).catch(xr);return}c=ee;const ke=a.value;Ro&&o0(bu(ke.fullPath,z.delta),Ol()),L(ee,ke).catch(T=>Cn(T,12)?T:Cn(T,2)?(N(T.to,ee).then(k=>{Cn(k,20)&&!z.delta&&z.type===nr.pop&&r.go(-1,!1)}).catch(xr),Promise.reject()):(z.delta&&r.go(-z.delta,!1),W(T,ee,ke))).then(T=>{T=T||I(ee,ke,!1),T&&(z.delta&&!Cn(T,8)?r.go(-z.delta,!1):z.type===nr.pop&&Cn(T,20)&&r.go(-1,!1)),D(ee,ke,T)}).catch(xr)}))}let G=vr(),U=vr(),X;function W(P,q,z){ce(P);const ee=U.list();return ee.length?ee.forEach(se=>se(P,q,z)):console.error(P),Promise.reject(P)}function ie(){return X&&a.value!==Fn?Promise.resolve():new Promise((P,q)=>{G.add([P,q])})}function ce(P){return X||(X=!P,Q(),G.list().forEach(([q,z])=>P?z(P):q()),G.reset()),P}function ge(P,q,z,ee){const{scrollBehavior:se}=e;if(!Ro||!se)return Promise.resolve();const ke=!z&&r0(bu(P.fullPath,0))||(ee||!z)&&history.state&&history.state.scroll||null;return bt().then(()=>se(P,q,ke)).then(T=>T&&n0(T)).catch(T=>W(T,P,q))}const Ce=P=>r.go(P);let Re;const Me=new Set,ze={currentRoute:a,listening:!0,addRoute:p,removeRoute:h,clearRoutes:t.clearRoutes,hasRoute:O,getRoutes:g,resolve:A,options:e,push:b,replace:w,go:Ce,back:()=>Ce(-1),forward:()=>Ce(1),beforeEach:i.add,beforeResolve:l.add,afterEach:s.add,onError:U.add,isReady:ie,install(P){const q=this;P.component("RouterLink",I0),P.component("RouterView",R0),P.config.globalProperties.$router=q,Object.defineProperty(P.config.globalProperties,"$route",{enumerable:!0,get:()=>E(a)}),Ro&&!Re&&a.value===Fn&&(Re=!0,b(r.location).catch(se=>{}));const z={};for(const se in Fn)Object.defineProperty(z,se,{get:()=>a.value[se],enumerable:!0});P.provide(Pl,q),P.provide(Ds,kd(z)),P.provide(Ua,a);const ee=P.unmount;Me.add(P),P.unmount=function(){Me.delete(P),Me.size<1&&(c=Fn,j&&j(),j=null,a.value=Fn,Re=!1,X=!1),ee()}}};function We(P){return P.reduce((q,z)=>q.then(()=>x(z)),Promise.resolve())}return ze}function M0(e,t){const n=[],o=[],r=[],i=Math.max(t.matched.length,e.matched.length);for(let l=0;l<i;l++){const s=t.matched[l];s&&(e.matched.find(c=>tr(c,s))?o.push(s):n.push(s));const a=e.matched[l];a&&(t.matched.find(c=>tr(c,a))||r.push(a))}return[n,o,r]}function co(){return yt(Pl)}function Rs(e){return yt(Ds)}function ja(e,t={},n){for(const o in e){const r=e[o],i=n?`${n}:${o}`:o;typeof r=="object"&&r!==null?ja(r,t,i):typeof r=="function"&&(t[i]=r)}return t}const V0={run:e=>e()},$0=()=>V0,Jp=typeof console.createTask<"u"?console.createTask:$0;function F0(e,t){const n=t.shift(),o=Jp(n);return e.reduce((r,i)=>r.then(()=>o.run(()=>i(...t))),Promise.resolve())}function B0(e,t){const n=t.shift(),o=Jp(n);return Promise.all(e.map(r=>o.run(()=>r(...t))))}function Jl(e,t){for(const n of[...e])n(t)}class U0{constructor(){this._hooks={},this._before=void 0,this._after=void 0,this._deprecatedMessages=void 0,this._deprecatedHooks={},this.hook=this.hook.bind(this),this.callHook=this.callHook.bind(this),this.callHookWith=this.callHookWith.bind(this)}hook(t,n,o={}){if(!t||typeof n!="function")return()=>{};const r=t;let i;for(;this._deprecatedHooks[t];)i=this._deprecatedHooks[t],t=i.to;if(i&&!o.allowDeprecated){let l=i.message;l||(l=`${r} hook has been deprecated`+(i.to?`, please use ${i.to}`:"")),this._deprecatedMessages||(this._deprecatedMessages=new Set),this._deprecatedMessages.has(l)||(console.warn(l),this._deprecatedMessages.add(l))}if(!n.name)try{Object.defineProperty(n,"name",{get:()=>"_"+t.replace(/\W+/g,"_")+"_hook_cb",configurable:!0})}catch{}return this._hooks[t]=this._hooks[t]||[],this._hooks[t].push(n),()=>{n&&(this.removeHook(t,n),n=void 0)}}hookOnce(t,n){let o,r=(...i)=>(typeof o=="function"&&o(),o=void 0,r=void 0,n(...i));return o=this.hook(t,r),o}removeHook(t,n){if(this._hooks[t]){const o=this._hooks[t].indexOf(n);o!==-1&&this._hooks[t].splice(o,1),this._hooks[t].length===0&&delete this._hooks[t]}}deprecateHook(t,n){this._deprecatedHooks[t]=typeof n=="string"?{to:n}:n;const o=this._hooks[t]||[];delete this._hooks[t];for(const r of o)this.hook(t,r)}deprecateHooks(t){Object.assign(this._deprecatedHooks,t);for(const n in t)this.deprecateHook(n,t[n])}addHooks(t){const n=ja(t),o=Object.keys(n).map(r=>this.hook(r,n[r]));return()=>{for(const r of o.splice(0,o.length))r()}}removeHooks(t){const n=ja(t);for(const o in n)this.removeHook(o,n[o])}removeAllHooks(){for(const t in this._hooks)delete this._hooks[t]}callHook(t,...n){return n.unshift(t),this.callHookWith(F0,t,...n)}callHookParallel(t,...n){return n.unshift(t),this.callHookWith(B0,t,...n)}callHookWith(t,n,...o){const r=this._before||this._after?{name:n,args:o,context:{}}:void 0;this._before&&Jl(this._before,r);const i=t(n in this._hooks?[...this._hooks[n]]:[],o);return i instanceof Promise?i.finally(()=>{this._after&&r&&Jl(this._after,r)}):(this._after&&r&&Jl(this._after,r),i)}beforeEach(t){return this._before=this._before||[],this._before.push(t),()=>{if(this._before!==void 0){const n=this._before.indexOf(t);n!==-1&&this._before.splice(n,1)}}}afterEach(t){return this._after=this._after||[],this._after.push(t),()=>{if(this._after!==void 0){const n=this._after.indexOf(t);n!==-1&&this._after.splice(n,1)}}}}function j0(){return new U0}const H0=new Set(["title","titleTemplate","script","style","noscript"]),Di=new Set(["base","meta","link","style","script","noscript"]),W0=new Set(["title","titleTemplate","templateParams","base","htmlAttrs","bodyAttrs","meta","link","style","script","noscript"]),G0=new Set(["base","title","titleTemplate","bodyAttrs","htmlAttrs","templateParams"]),Qp=new Set(["tagPosition","tagPriority","tagDuplicateStrategy","children","innerHTML","textContent","processTemplateParams"]),K0=typeof window<"u";function el(e){let t=9;for(let n=0;n<e.length;)t=Math.imul(t^e.charCodeAt(n++),9**9);return((t^t>>>9)+65536).toString(16).substring(1,8).toLowerCase()}function Du(e){if(e._h)return e._h;if(e._d)return el(e._d);let t=`${e.tag}:${e.textContent||e.innerHTML||""}:`;for(const n in e.props)t+=`${n}:${e.props[n]},`;return el(t)}const Y0=["name","property","http-equiv"];function Zp(e){const{props:t,tag:n}=e;if(G0.has(n))return n;if(n==="link"&&t.rel==="canonical")return"canonical";if(t.charset)return"charset";if(t.id)return`${n}:id:${t.id}`;for(const o of Y0)if(t[o]!==void 0)return`${n}:${o}:${t[o]}`;return!1}function Ru(e,t){return e==null?t||null:typeof e=="function"?e(t):e}function z0(e,t){return e instanceof Promise?e.then(t):t(e)}function Ha(e,t,n,o){const r=o||th(typeof t=="object"&&typeof t!="function"&&!(t instanceof Promise)?{...t}:{[e==="script"||e==="noscript"||e==="style"?"innerHTML":"textContent"]:t},e==="templateParams"||e==="titleTemplate");if(r instanceof Promise)return r.then(l=>Ha(e,t,n,l));const i={tag:e,props:r};for(const l of Qp){const s=i.props[l]!==void 0?i.props[l]:n[l];s!==void 0&&((!(l==="innerHTML"||l==="textContent"||l==="children")||H0.has(i.tag))&&(i[l==="children"?"innerHTML":l]=s),delete i.props[l])}return i.props.body&&(i.tagPosition="bodyClose",delete i.props.body),i.tag==="script"&&typeof i.innerHTML=="object"&&(i.innerHTML=JSON.stringify(i.innerHTML),i.props.type=i.props.type||"application/json"),Array.isArray(i.props.content)?i.props.content.map(l=>({...i,props:{...i.props,content:l}})):i}function X0(e,t){var o;const n=e==="class"?" ":";";return typeof t=="object"&&!Array.isArray(t)&&(t=Object.entries(t).filter(([,r])=>r).map(([r,i])=>e==="style"?`${r}:${i}`:r)),(o=String(Array.isArray(t)?t.join(n):t))==null?void 0:o.split(n).filter(r=>!!r.trim()).join(n)}function eh(e,t,n,o){for(let r=o;r<n.length;r+=1){const i=n[r];if(i==="class"||i==="style"){e[i]=X0(i,e[i]);continue}if(e[i]instanceof Promise)return e[i].then(l=>(e[i]=l,eh(e,t,n,r)));if(!t&&!Qp.has(i)){const l=String(e[i]),s=i.startsWith("data-");l==="true"||l===""?e[i]=s?"true":!0:e[i]||(s&&l==="false"?e[i]="false":delete e[i])}}}function th(e,t=!1){const n=eh(e,t,Object.keys(e),0);return n instanceof Promise?n.then(()=>e):e}const q0=10;function nh(e,t,n){for(let o=n;o<t.length;o+=1){const r=t[o];if(r instanceof Promise)return r.then(i=>(t[o]=i,nh(e,t,o)));Array.isArray(r)?e.push(...r):e.push(r)}}function J0(e){const t=[],n=e.resolvedInput;for(const r in n){if(!Object.prototype.hasOwnProperty.call(n,r))continue;const i=n[r];if(!(i===void 0||!W0.has(r))){if(Array.isArray(i)){for(const l of i)t.push(Ha(r,l,e));continue}t.push(Ha(r,i,e))}}if(t.length===0)return[];const o=[];return z0(nh(o,t,0),()=>o.map((r,i)=>(r._e=e._i,e.mode&&(r._m=e.mode),r._p=(e._i<<q0)+i,r)))}const Nu={base:-10,title:10},Mu={critical:-80,high:-10,low:20};function tl(e){const t=e.tagPriority;if(typeof t=="number")return t;let n=100;return e.tag==="meta"?e.props["http-equiv"]==="content-security-policy"?n=-30:e.props.charset?n=-20:e.props.name==="viewport"&&(n=-15):e.tag==="link"&&e.props.rel==="preconnect"?n=20:e.tag in Nu&&(n=Nu[e.tag]),t&&t in Mu?n+Mu[t]:n}const Q0=[{prefix:"before:",offset:-1},{prefix:"after:",offset:1}],Vu=new Set(["onload","onerror","onabort","onprogress","onloadstart"]),Gn="%separator";function Z0(e,t){var o;let n;if(t==="s"||t==="pageTitle")n=e.pageTitle;else if(t.includes(".")){const r=t.indexOf(".");n=(o=e[t.substring(0,r)])==null?void 0:o[t.substring(r+1)]}else n=e[t];return n!==void 0?(n||"").replace(/"/g,'\\"'):void 0}const eE=new RegExp(`${Gn}(?:\\s*${Gn})*`,"g");function Ql(e,t,n){if(typeof e!="string"||!e.includes("%"))return e;let o=e;try{o=decodeURI(e)}catch{}const r=o.match(/%\w+(?:\.\w+)?/g);if(!r)return e;const i=e.includes(Gn);return e=e.replace(/%\w+(?:\.\w+)?/g,l=>{if(l===Gn||!r.includes(l))return l;const s=Z0(t,l.slice(1));return s!==void 0?s:l}).trim(),i&&(e.endsWith(Gn)&&(e=e.slice(0,-Gn.length)),e.startsWith(Gn)&&(e=e.slice(Gn.length)),e=e.replace(eE,n).trim()),e}async function tE(e,t={}){var f;const n=t.document||e.resolvedOptions.document;if(!n||!e.dirty)return;const o={shouldRender:!0,tags:[]};if(await e.hooks.callHook("dom:beforeRender",o),!o.shouldRender)return;const r=(await e.resolveTags()).map(u=>({tag:u,id:Di.has(u.tag)?Du(u):u.tag,shouldRender:!0}));let i=e._dom;if(!i){i={elMap:{htmlAttrs:n.documentElement,bodyAttrs:n.body}};const u=new Set;for(const d of["body","head"]){const p=(f=n[d])==null?void 0:f.children;for(const h of p){const g=h.tagName.toLowerCase();if(!Di.has(g))continue;const O={tag:g,props:await th(h.getAttributeNames().reduce((b,w)=>({...b,[w]:h.getAttribute(w)}),{})),innerHTML:h.innerHTML},A=Zp(O);let V=A,v=1;for(;V&&u.has(V);)V=`${A}:${v++}`;V&&(O._d=V,u.add(V)),i.elMap[h.getAttribute("data-hid")||Du(O)]=h}}}i.pendingSideEffects={...i.sideEffects},i.sideEffects={};function l(u,d,p){const h=`${u}:${d}`;i.sideEffects[h]=p,delete i.pendingSideEffects[h]}function s({id:u,$el:d,tag:p}){const h=p.tag.endsWith("Attrs");if(i.elMap[u]=d,h||(p.textContent&&p.textContent!==d.textContent&&(d.textContent=p.textContent),p.innerHTML&&p.innerHTML!==d.innerHTML&&(d.innerHTML=p.innerHTML),l(u,"el",()=>{var g;(g=i.elMap[u])==null||g.remove(),delete i.elMap[u]})),p._eventHandlers)for(const g in p._eventHandlers)Object.prototype.hasOwnProperty.call(p._eventHandlers,g)&&d.getAttribute(`data-${g}`)!==""&&((p.tag==="bodyAttrs"?n.defaultView:d).addEventListener(g.substring(2),p._eventHandlers[g].bind(d)),d.setAttribute(`data-${g}`,""));for(const g in p.props){if(!Object.prototype.hasOwnProperty.call(p.props,g))continue;const O=p.props[g],A=`attr:${g}`;if(g==="class"){if(!O)continue;for(const V of O.split(" "))h&&l(u,`${A}:${V}`,()=>d.classList.remove(V)),!d.classList.contains(V)&&d.classList.add(V)}else if(g==="style"){if(!O)continue;for(const V of O.split(";")){const v=V.indexOf(":"),b=V.substring(0,v).trim(),w=V.substring(v+1).trim();l(u,`${A}:${b}`,()=>{d.style.removeProperty(b)}),d.style.setProperty(b,w)}}else d.getAttribute(g)!==O&&d.setAttribute(g,O===!0?"":String(O)),h&&l(u,A,()=>d.removeAttribute(g))}}const a=[],c={bodyClose:void 0,bodyOpen:void 0,head:void 0};for(const u of r){const{tag:d,shouldRender:p,id:h}=u;if(p){if(d.tag==="title"){n.title=d.textContent;continue}u.$el=u.$el||i.elMap[h],u.$el?s(u):Di.has(d.tag)&&a.push(u)}}for(const u of a){const d=u.tag.tagPosition||"head";u.$el=n.createElement(u.tag.tag),s(u),c[d]=c[d]||n.createDocumentFragment(),c[d].appendChild(u.$el)}for(const u of r)await e.hooks.callHook("dom:renderTag",u,n,l);c.head&&n.head.appendChild(c.head),c.bodyOpen&&n.body.insertBefore(c.bodyOpen,n.body.firstChild),c.bodyClose&&n.body.appendChild(c.bodyClose);for(const u in i.pendingSideEffects)i.pendingSideEffects[u]();e._dom=i,e.dirty=!1,await e.hooks.callHook("dom:rendered",{renders:r})}function nE(e,t={}){const n=t.delayFn||(o=>setTimeout(o,10));return e._domUpdatePromise=e._domUpdatePromise||new Promise(o=>n(()=>tE(e,t).then(()=>{delete e._domUpdatePromise,o()})))}function oE(e){return t=>{var o,r;const n=((r=(o=t.resolvedOptions.document)==null?void 0:o.head.querySelector('script[id="unhead:payload"]'))==null?void 0:r.innerHTML)||!1;return n&&t.push(JSON.parse(n)),{mode:"client",hooks:{"entries:updated":i=>{nE(i,e)}}}}}const rE=new Set(["templateParams","htmlAttrs","bodyAttrs"]),iE={hooks:{"tag:normalise":({tag:e})=>{e.props.hid&&(e.key=e.props.hid,delete e.props.hid),e.props.vmid&&(e.key=e.props.vmid,delete e.props.vmid),e.props.key&&(e.key=e.props.key,delete e.props.key);const n=Zp(e)||(e.key?`${e.tag}:${e.key}`:!1);n&&(e._d=n)},"tags:resolve":e=>{const t=Object.create(null);for(const o of e.tags){const r=(o.key?`${o.tag}:${o.key}`:o._d)||o._p,i=t[r];if(i){let s=o==null?void 0:o.tagDuplicateStrategy;if(!s&&rE.has(o.tag)&&(s="merge"),s==="merge"){const a=i.props;a.style&&o.props.style&&(a.style[a.style.length-1]!==";"&&(a.style+=";"),o.props.style=`${a.style} ${o.props.style}`),a.class&&o.props.class?o.props.class=`${a.class} ${o.props.class}`:a.class&&(o.props.class=a.class),t[r].props={...a,...o.props};continue}else if(o._e===i._e){i._duped=i._duped||[],o._d=`${i._d}:${i._duped.length+1}`,i._duped.push(o);continue}else if(tl(o)>tl(i))continue}if(!(o.innerHTML||o.textContent||Object.keys(o.props).length!==0)&&Di.has(o.tag)){delete t[r];continue}t[r]=o}const n=[];for(const o in t){const r=t[o],i=r._duped;n.push(r),i&&(delete r._duped,n.push(...i))}e.tags=n,e.tags=e.tags.filter(o=>!(o.tag==="meta"&&(o.props.name||o.props.property)&&!o.props.content))}}},lE={mode:"server",hooks:{"tags:resolve":e=>{const t={};let n=!1;for(const o of e.tags)o._m!=="server"||o.tag!=="titleTemplate"&&o.tag!=="templateParams"&&o.tag!=="title"||(t[o.tag]=o.tag==="title"||o.tag==="titleTemplate"?o.textContent:o.props,n=!0);n&&e.tags.push({tag:"script",innerHTML:JSON.stringify(t),props:{id:"unhead:payload",type:"application/json"}})}}},aE=new Set(["script","link","bodyAttrs"]),sE=e=>({hooks:{"tags:resolve":t=>{for(const n of t.tags){if(!aE.has(n.tag))continue;const o=n.props;for(const r in o){if(r[0]!=="o"||r[1]!=="n"||!Object.prototype.hasOwnProperty.call(o,r))continue;const i=o[r];typeof i=="function"&&(e.ssr&&Vu.has(r)?o[r]=`this.dataset.${r}fired = true`:delete o[r],n._eventHandlers=n._eventHandlers||{},n._eventHandlers[r]=i)}e.ssr&&n._eventHandlers&&(n.props.src||n.props.href)&&(n.key=n.key||el(n.props.src||n.props.href))}},"dom:renderTag":({$el:t,tag:n})=>{var r,i;const o=t==null?void 0:t.dataset;if(o)for(const l in o){if(!l.endsWith("fired"))continue;const s=l.slice(0,-5);Vu.has(s)&&((i=(r=n._eventHandlers)==null?void 0:r[s])==null||i.call(t,new Event(s.substring(2))))}}}}),cE=new Set(["link","style","script","noscript"]),uE={hooks:{"tag:normalise":({tag:e})=>{e.key&&cE.has(e.tag)&&(e.props["data-hid"]=e._h=el(e.key))}}},fE={hooks:{"tags:resolve":e=>{var t;for(const n of e.tags)if(typeof n.tagPriority=="string")for(const{prefix:o,offset:r}of Q0){if(!n.tagPriority.startsWith(o))continue;const i=n.tagPriority.substring(o.length),l=(t=e.tags.find(s=>s._d===i))==null?void 0:t._p;if(l!==void 0){n._p=l+r;break}}e.tags.sort((n,o)=>{const r=tl(n),i=tl(o);return r<i?-1:r>i?1:n._p-o._p})}}},dE={meta:"content",link:"href",htmlAttrs:"lang"},pE=["innerHTML","textContent"],hE=e=>({hooks:{"tags:resolve":t=>{var l;const{tags:n}=t;let o;for(let s=0;s<n.length;s+=1)n[s].tag==="templateParams"&&(o=t.tags.splice(s,1)[0].props,s-=1);const r=o||{},i=r.separator||"|";delete r.separator,r.pageTitle=Ql(r.pageTitle||((l=n.find(s=>s.tag==="title"))==null?void 0:l.textContent)||"",r,i);for(const s of n){if(s.processTemplateParams===!1)continue;const a=dE[s.tag];if(a&&typeof s.props[a]=="string")s.props[a]=Ql(s.props[a],r,i);else if(s.processTemplateParams||s.tag==="titleTemplate"||s.tag==="title")for(const c of pE)typeof s[c]=="string"&&(s[c]=Ql(s[c],r,i))}e._templateParams=r,e._separator=i}}}),mE={hooks:{"tags:resolve":e=>{const{tags:t}=e;let n,o;for(let r=0;r<t.length;r+=1){const i=t[r];i.tag==="title"?n=i:i.tag==="titleTemplate"&&(o=i)}if(o&&n){const r=Ru(o.textContent,n.textContent);r!==null?n.textContent=r||n.textContent:e.tags.splice(e.tags.indexOf(n),1)}else if(o){const r=Ru(o.textContent);r!==null&&(o.textContent=r,o.tag="title",o=void 0)}o&&e.tags.splice(e.tags.indexOf(o),1)}}},gE={hooks:{"tags:afterResolve":e=>{for(const t of e.tags)typeof t.innerHTML=="string"&&(t.innerHTML&&(t.props.type==="application/ld+json"||t.props.type==="application/json")?t.innerHTML=t.innerHTML.replace(/</g,"\\u003C"):t.innerHTML=t.innerHTML.replace(new RegExp(`</${t.tag}`,"g"),`<\\/${t.tag}`))}}};let oh;function vE(e={}){const t=_E(e);return t.use(oE()),oh=t}function $u(e,t){return!e||e==="server"&&t||e==="client"&&!t}function _E(e={}){const t=j0();t.addHooks(e.hooks||{}),e.document=e.document||(K0?document:void 0);const n=!e.document,o=()=>{s.dirty=!0,t.callHook("entries:updated",s)};let r=0,i=[];const l=[],s={plugins:l,dirty:!1,resolvedOptions:e,hooks:t,headEntries(){return i},use(a){const c=typeof a=="function"?a(s):a;(!c.key||!l.some(f=>f.key===c.key))&&(l.push(c),$u(c.mode,n)&&t.addHooks(c.hooks||{}))},push(a,c){c==null||delete c.head;const f={_i:r++,input:a,...c};return $u(f.mode,n)&&(i.push(f),o()),{dispose(){i=i.filter(u=>u._i!==f._i),t.callHook("entries:updated",s),o()},patch(u){for(const d of i)d._i===f._i&&(d.input=f.input=u);o()}}},async resolveTags(){const a={tags:[],entries:[...i]};await t.callHook("entries:resolve",a);for(const c of a.entries){const f=c.resolvedInput||c.input;if(c.resolvedInput=await(c.transform?c.transform(f):f),c.resolvedInput)for(const u of await J0(c)){const d={tag:u,entry:c,resolvedOptions:s.resolvedOptions};await t.callHook("tag:normalise",d),a.tags.push(d.tag)}}return await t.callHook("tags:beforeResolve",a),await t.callHook("tags:resolve",a),await t.callHook("tags:afterResolve",a),a.tags},ssr:n};return[iE,lE,sE,uE,fE,hE,mE,gE,...(e==null?void 0:e.plugins)||[]].forEach(a=>s.use(a)),s.hooks.callHook("init",s),s}function bE(){return oh}const yE=vp[0]==="3";function EE(e){return typeof e=="function"?e():E(e)}function nl(e){if(e instanceof Promise)return e;const t=EE(e);if(!e||!t)return t;if(Array.isArray(t))return t.map(n=>nl(n));if(typeof t=="object"){const n={};for(const o in t)if(Object.prototype.hasOwnProperty.call(t,o)){if(o==="titleTemplate"||o[0]==="o"&&o[1]==="n"){n[o]=E(t[o]);continue}n[o]=nl(t[o])}return n}return t}const SE={hooks:{"entries:resolve":e=>{for(const t of e.entries)t.resolvedInput=nl(t.input)}}},rh="usehead";function CE(e){return{install(n){yE&&(n.config.globalProperties.$unhead=e,n.config.globalProperties.$head=e,n.provide(rh,e))}}.install}function TE(e={}){e.domDelayFn=e.domDelayFn||(n=>bt(()=>setTimeout(()=>n(),0)));const t=vE(e);return t.use(SE),t.install=CE(t),t}const Fu=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},Bu="__unhead_injection_handler__";function wE(){if(Bu in Fu)return Fu[Bu]();const e=yt(rh);return e||bE()}function ih(e,t={}){const n=t.head||wE();if(n)return n.ssr?n.push(e,t):kE(n,e,t)}function kE(e,t,n={}){const o=fe(!1),r=fe({});Cl(()=>{r.value=o.value?{}:nl(t)});const i=e.push(r.value,n);return De(r,s=>{i.patch(s)}),Et()&&(Zr(()=>{i.dispose()}),cr(()=>{o.value=!0}),Qr(()=>{o.value=!1})),i}function OE(e){try{return JSON.parse(e||"{}")}catch(t){return console.error("[SSG] On state deserialization -",t,e),{}}}function PE(e){return document.readyState==="loading"?new Promise(t=>{document.addEventListener("DOMContentLoaded",()=>t(e))}):Promise.resolve(e)}const AE=ue({setup(e,{slots:t}){const n=fe(!1);return Pt(()=>n.value=!0),()=>n.value?t.default&&t.default({}):t.placeholder&&t.placeholder({})}});function IE(e,t,n,o={}){const{transformState:r,registerComponents:i=!0,useHead:l=!0,rootContainer:s="#app"}=o,a=typeof window<"u";async function c(f=!1,u){const d=f?Sp(e):zb(e);let p;l&&(p=TE(),d.use(p));const h=N0({history:f?Kp(t.base):s0(t.base),...t}),{routes:g}=t;i&&d.component("ClientOnly",AE);const O=[],v={app:d,head:p,isClient:a,router:h,routes:g,onSSRAppRendered:f?()=>{}:N=>O.push(N),triggerOnSSRAppRendered:()=>Promise.all(O.map(N=>N())),initialState:{},transformState:r,routePath:u};f&&(await PE(),v.initialState=(r==null?void 0:r(window.__INITIAL_STATE__||{}))||OE(window.__INITIAL_STATE__)),await(n==null?void 0:n(v)),d.use(h);let b,w=!0;if(h.beforeEach((N,M,x)=>{(w||b&&b===N.path)&&(w=!1,b=N.path,N.meta.state=v.initialState),x()}),!f){const N=v.routePath??"/";h.push(N),await h.isReady(),v.initialState=h.currentRoute.value.meta.state||{}}const S=v.initialState;return{...v,initialState:S}}return a&&(async()=>{const{app:f,router:u}=await c(!0);await u.isReady(),f.mount(s,!0)})(),c}const xE=e=>{const t={};Object.entries(Object.assign({"/src/layouts/404.vue":()=>Se(()=>Promise.resolve().then(()=>KS),void 0),"/src/layouts/default.vue":()=>Se(()=>Promise.resolve().then(()=>Uw),void 0),"/src/layouts/home.vue":()=>Se(()=>Promise.resolve().then(()=>Gw),void 0)})).forEach(([r,i])=>{let l=r.replace("/src/layouts/","").replace(".vue","");t[l]=i});function o(r,i=!0){return r.map(l=>{var s,a,c,f,u,d;if(((s=l.children)==null?void 0:s.length)>0&&(l.children=o(l.children,!1)),i){if(!l.component&&((a=l.children)==null?void 0:a.find(h=>{var g;return(h.path===""||h.path==="/")&&((g=h.meta)==null?void 0:g.isLayout)})))return l;if(((c=l.meta)==null?void 0:c.layout)!==!1)return{path:l.path,component:t[((f=l.meta)==null?void 0:f.layout)||"default"],children:l.path==="/"?[l]:[{...l,path:""}],meta:{isLayout:!0}}}return(u=l.meta)!=null&&u.layout?{path:l.path,component:t[(d=l.meta)==null?void 0:d.layout],children:[{...l,path:""}],meta:{isLayout:!0}}:l})}return o(e)},LE=[{path:"/",name:"/",component:()=>Se(()=>Promise.resolve().then(()=>Xw),void 0),meta:{layout:"home"}},{path:"/:all(.*)",name:"/[...all]",component:()=>Se(()=>Promise.resolve().then(()=>qw),void 0),meta:{layout:404}},{path:"/about",children:[{path:"",name:"/about/",component:()=>Se(()=>Promise.resolve().then(()=>Zw),void 0),meta:{layout:"default",title:"About"}}]},{path:"/app",children:[{path:"",name:"/app/",component:()=>Se(()=>Promise.resolve().then(()=>ek),void 0),meta:{layout:"default",title:"General"}}]},{path:"/cloudsync",children:[{path:"",name:"/cloudsync/",component:()=>Se(()=>Promise.resolve().then(()=>ok),void 0),meta:{layout:"default",title:"Cloud Sync"}}]},{path:"/control",children:[{path:"",name:"/control/",component:()=>Se(()=>Promise.resolve().then(()=>rk),void 0),meta:{layout:"default",title:"Control"}}]},{path:"/debug",children:[{path:"",name:"/debug/",component:()=>Se(()=>Promise.resolve().then(()=>lk),void 0),meta:{layout:"default",title:"Debug"}}]},{path:"/epg",children:[{path:"",name:"/epg/",component:()=>Se(()=>Promise.resolve().then(()=>sk),void 0),meta:{layout:"default",title:"EPG"}},{path:"epg-source-add",children:[{path:"",name:"/epg/epg-source-add/",component:()=>Se(()=>Promise.resolve().then(()=>uk),void 0),meta:{layout:"default",title:"Add EPG Source"}}]},{path:"epg-source-detail",children:[{path:":idx",name:"/epg/epg-source-detail/[idx]",component:()=>Se(()=>Promise.resolve().then(()=>pk),void 0),meta:{layout:"default",title:"EPG Source Detail"}}]},{path:"epg-source-list",children:[{path:"",name:"/epg/epg-source-list/",component:()=>Se(()=>Promise.resolve().then(()=>mk),void 0),meta:{layout:"default",title:"Custom EPG List"}}]}]},{path:"/iptv",children:[{path:"",name:"/iptv/",component:()=>Se(()=>Promise.resolve().then(()=>Ek),void 0),meta:{layout:"default",title:"Subscription Source"}},{path:"channel-alias",children:[{path:"",name:"/iptv/channel-alias/",component:()=>Se(()=>Promise.resolve().then(()=>Ck),void 0),meta:{layout:"default",title:"Channel Alias"}}]},{path:"favorites",children:[{path:"",name:"/iptv/favorites/",component:()=>Se(()=>Promise.resolve().then(()=>g1),void 0),meta:{layout:"default",title:"Favorite Channels"}}]},{path:"iptv-source-add",children:[{path:"",name:"/iptv/iptv-source-add/",component:()=>Se(()=>Promise.resolve().then(()=>_1),void 0),meta:{layout:"default",title:"Add IPTV Source"}}]},{path:"iptv-source-detail",children:[{path:":idx",name:"/iptv/iptv-source-detail/[idx]",component:()=>Se(()=>Promise.resolve().then(()=>E1),void 0),meta:{layout:"default",title:"IPTV Source Detail"}}]},{path:"iptv-source-list",children:[{path:"",name:"/iptv/iptv-source-list/",component:()=>Se(()=>Promise.resolve().then(()=>w1),void 0),meta:{layout:"default",title:"Custom IPTV Sources"}}]}]},{path:"/log",children:[{path:"",name:"/log/",component:()=>Se(()=>Promise.resolve().then(()=>I1),void 0),meta:{layout:"default",title:"Log"}}]},{path:"/player",children:[{path:"",name:"/player/",component:()=>Se(()=>Promise.resolve().then(()=>R1),void 0),meta:{layout:"default",title:"Player"}}]},{path:"/README",name:"/README",component:()=>Se(()=>Promise.resolve().then(()=>B1),void 0)},{path:"/theme",children:[{path:"",name:"/theme/",component:()=>Se(()=>Promise.resolve().then(()=>W1),void 0),meta:{layout:"default",title:"Theme"}}]},{path:"/ui",children:[{path:"",name:"/ui/",component:()=>Se(()=>Promise.resolve().then(()=>G1),void 0),meta:{layout:"default",title:"UI"}}]},{path:"/update",children:[{path:"",name:"/update/",component:()=>Se(()=>Promise.resolve().then(()=>K1),void 0),meta:{layout:"default",title:"Update"}}]}];function Ns(e){return hd()?(Pv(e),!0):!1}function DE(){const e=new Set,t=r=>{e.delete(r)};return{on:r=>{e.add(r);const i=()=>t(r);return Ns(i),{off:i}},off:t,trigger:(...r)=>Promise.all(Array.from(e).map(i=>i(...r)))}}function rr(e){return typeof e=="function"?e():E(e)}const lh=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const RE=Object.prototype.toString,NE=e=>RE.call(e)==="[object Object]",ah=()=>{},ME=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);function VE(e,t){function n(...o){return new Promise((r,i)=>{Promise.resolve(e(()=>t.apply(this,o),{fn:t,thisArg:this,args:o})).then(r).catch(i)})}return n}const sh=e=>e();function $E(e=sh){const t=fe(!0);function n(){t.value=!1}function o(){t.value=!0}const r=(...i)=>{t.value&&e(...i)};return{isActive:qr(t),pause:n,resume:o,eventFilter:r}}function FE(e){return Et()}function BE(...e){if(e.length!==1)return o_(...e);const t=e[0];return typeof t=="function"?qr(Zv(()=>({get:t,set:ah}))):fe(t)}function UE(e,t,n={}){const{eventFilter:o=sh,...r}=n;return De(e,VE(o,t),r)}function jE(e,t,n={}){const{eventFilter:o,...r}=n,{eventFilter:i,pause:l,resume:s,isActive:a}=$E(o);return{stop:UE(e,t,{...r,eventFilter:i}),pause:l,resume:s,isActive:a}}function ch(e,t=!0,n){FE()?Pt(e,n):t?e():bt(e)}function HE(e=!1,t={}){const{truthyValue:n=!0,falsyValue:o=!1}=t,r=Ge(e),i=fe(e);function l(s){if(arguments.length)return i.value=s,i.value;{const a=rr(n);return i.value=i.value===a?rr(o):a,i.value}}return r?l:[i,l]}function uh(e){var t;const n=rr(e);return(t=n==null?void 0:n.$el)!=null?t:n}const ir=lh?window:void 0,WE=lh?window.document:void 0;function Uu(...e){let t,n,o,r;if(typeof e[0]=="string"||Array.isArray(e[0])?([n,o,r]=e,t=ir):[t,n,o,r]=e,!t)return ah;Array.isArray(n)||(n=[n]),Array.isArray(o)||(o=[o]);const i=[],l=()=>{i.forEach(f=>f()),i.length=0},s=(f,u,d,p)=>(f.addEventListener(u,d,p),()=>f.removeEventListener(u,d,p)),a=De(()=>[uh(t),rr(r)],([f,u])=>{if(l(),!f)return;const d=NE(u)?{...u}:u;i.push(...n.flatMap(p=>o.map(h=>s(f,p,h,d))))},{immediate:!0,flush:"post"}),c=()=>{a(),l()};return Ns(c),c}function GE(){const e=fe(!1),t=Et();return t&&Pt(()=>{e.value=!0},t),e}function KE(e){const t=GE();return he(()=>(t.value,!!e()))}function YE(e,t={}){const{window:n=ir}=t,o=KE(()=>n&&"matchMedia"in n&&typeof n.matchMedia=="function");let r;const i=fe(!1),l=c=>{i.value=c.matches},s=()=>{r&&("removeEventListener"in r?r.removeEventListener("change",l):r.removeListener(l))},a=Cl(()=>{o.value&&(s(),r=n.matchMedia(rr(e)),"addEventListener"in r?r.addEventListener("change",l):r.addListener(l),i.value=r.matches)});return Ns(()=>{a(),s(),r=void 0}),i}const Ei=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},Si="__vueuse_ssr_handlers__",zE=XE();function XE(){return Si in Ei||(Ei[Si]=Ei[Si]||{}),Ei[Si]}function fh(e,t){return zE[e]||t}function qE(e){return e==null?"any":e instanceof Set?"set":e instanceof Map?"map":e instanceof Date?"date":typeof e=="boolean"?"boolean":typeof e=="string"?"string":typeof e=="object"?"object":Number.isNaN(e)?"any":"number"}const JE={boolean:{read:e=>e==="true",write:e=>String(e)},object:{read:e=>JSON.parse(e),write:e=>JSON.stringify(e)},number:{read:e=>Number.parseFloat(e),write:e=>String(e)},any:{read:e=>e,write:e=>String(e)},string:{read:e=>e,write:e=>String(e)},map:{read:e=>new Map(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e.entries()))},set:{read:e=>new Set(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e))},date:{read:e=>new Date(e),write:e=>e.toISOString()}},ju="vueuse-storage";function QE(e,t,n,o={}){var r;const{flush:i="pre",deep:l=!0,listenToStorageChanges:s=!0,writeDefaults:a=!0,mergeDefaults:c=!1,shallow:f,window:u=ir,eventFilter:d,onError:p=L=>{console.error(L)},initOnMounted:h}=o,g=(f?ys:fe)(typeof t=="function"?t():t);if(!n)try{n=fh("getDefaultStorage",()=>{var L;return(L=ir)==null?void 0:L.localStorage})()}catch(L){p(L)}if(!n)return g;const O=rr(t),A=qE(O),V=(r=o.serializer)!=null?r:JE[A],{pause:v,resume:b}=jE(g,()=>S(g.value),{flush:i,deep:l,eventFilter:d});u&&s&&ch(()=>{n instanceof Storage?Uu(u,"storage",M):Uu(u,ju,x),h&&M()}),h||M();function w(L,D){if(u){const I={key:e,oldValue:L,newValue:D,storageArea:n};u.dispatchEvent(n instanceof Storage?new StorageEvent("storage",I):new CustomEvent(ju,{detail:I}))}}function S(L){try{const D=n.getItem(e);if(L==null)w(D,null),n.removeItem(e);else{const I=V.write(L);D!==I&&(n.setItem(e,I),w(D,I))}}catch(D){p(D)}}function N(L){const D=L?L.newValue:n.getItem(e);if(D==null)return a&&O!=null&&n.setItem(e,V.write(O)),O;if(!L&&c){const I=V.read(D);return typeof c=="function"?c(I,O):A==="object"&&!Array.isArray(I)?{...O,...I}:I}else return typeof D!="string"?D:V.read(D)}function M(L){if(!(L&&L.storageArea!==n)){if(L&&L.key==null){g.value=O;return}if(!(L&&L.key!==e)){v();try{(L==null?void 0:L.newValue)!==V.write(g.value)&&(g.value=N(L))}catch(D){p(D)}finally{L?bt(b):b()}}}}function x(L){M(L.detail)}return g}function Ms(e){return YE("(prefers-color-scheme: dark)",e)}const ZE="*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}";function eS(e={}){const{selector:t="html",attribute:n="class",initialValue:o="auto",window:r=ir,storage:i,storageKey:l="vueuse-color-scheme",listenToStorageChanges:s=!0,storageRef:a,emitAuto:c,disableTransition:f=!0}=e,u={auto:"",light:"light",dark:"dark",...e.modes||{}},d=Ms({window:r}),p=he(()=>d.value?"dark":"light"),h=a||(l==null?BE(o):QE(l,o,i,{window:r,listenToStorageChanges:s})),g=he(()=>h.value==="auto"?p.value:h.value),O=fh("updateHTMLAttrs",(b,w,S)=>{const N=typeof b=="string"?r==null?void 0:r.document.querySelector(b):uh(b);if(!N)return;const M=new Set,x=new Set;let L=null;if(w==="class"){const I=S.split(/\s/g);Object.values(u).flatMap(j=>(j||"").split(/\s/g)).filter(Boolean).forEach(j=>{I.includes(j)?M.add(j):x.add(j)})}else L={key:w,value:S};if(M.size===0&&x.size===0&&L===null)return;let D;f&&(D=r.document.createElement("style"),D.appendChild(document.createTextNode(ZE)),r.document.head.appendChild(D));for(const I of M)N.classList.add(I);for(const I of x)N.classList.remove(I);L&&N.setAttribute(L.key,L.value),f&&(r.getComputedStyle(D).opacity,document.head.removeChild(D))});function A(b){var w;O(t,n,(w=u[b])!=null?w:b)}function V(b){e.onChanged?e.onChanged(b,A):A(b)}De(g,V,{flush:"post",immediate:!0}),ch(()=>V(g.value));const v=he({get(){return c?h.value:g.value},set(b){h.value=b}});try{return Object.assign(v,{store:h,system:p,state:g})}catch{return v}}function tS(e={}){const{valueDark:t="dark",valueLight:n="",window:o=ir}=e,r=eS({...e,onChanged:(s,a)=>{var c;e.onChanged?(c=e.onChanged)==null||c.call(e,s==="dark",a,s):a(s)},modes:{dark:t,light:n}}),i=he(()=>r.system?r.system.value:Ms({window:o}).value?"dark":"light");return he({get(){return r.value==="dark"},set(s){const a=s?"dark":"light";i.value===a?r.value="auto":r.value=a}})}const nS={multiple:!0,accept:"*",reset:!1,directory:!1};function dh(e={}){const{document:t=WE}=e,n=fe(null),{on:o,trigger:r}=DE();let i;t&&(i=t.createElement("input"),i.type="file",i.onchange=a=>{const c=a.target;n.value=c.files,r(n.value)});const l=()=>{n.value=null,i&&i.value&&(i.value="",r(null))},s=a=>{if(!i)return;const c={...nS,...e,...a};i.multiple=c.multiple,i.accept=c.accept,i.webkitdirectory=c.directory,ME(c,"capture")&&(i.capture=c.capture),c.reset&&l(),i.click()};return{files:qr(n),open:s,reset:l,onChange:o}}const Al=tS();HE(Al);const oS=Ms(),rS=ue({__name:"App",setup(e){return ih({title:"TV Live",meta:[{name:"description",content:"Opinionated Vite Starter Template"},{name:"theme-color",content:()=>Al.value?"#00aba9":"#ffffff"}],link:[{rel:"icon",type:"image/svg+xml",href:()=>oS.value?"/favicon-dark.svg":"/favicon.svg"}]}),(t,n)=>{const o=El("RouterView");return le(),_e(o)}}});IE(rS,{routes:xE(LE),base:"/remote-configs-en",history:c0()},e=>{Object.values(Object.assign({"./modules/i18n.ts":Cy,"./modules/nprogress.ts":ky,"./modules/pinia.ts":xy,"./modules/pwa.ts":Dy})).forEach(t=>{var n;return(n=t.install)==null?void 0:n.call(t,e)})});const iS={button:{about:{t:0,b:{t:2,i:[{t:3}],s:"حول"}},back:{t:0,b:{t:2,i:[{t:3}],s:"رجوع"}},go:{t:0,b:{t:2,i:[{t:3}],s:"تجربة"}},home:{t:0,b:{t:2,i:[{t:3}],s:"الرئيسية"}},toggle_dark:{t:0,b:{t:2,i:[{t:3}],s:"التغيير إلى الوضع المظلم"}},toggle_langs:{t:0,b:{t:2,i:[{t:3}],s:"تغيير اللغة"}}},intro:{desc:{t:0,b:{t:2,i:[{t:3}],s:"vite مثال لتطبيق"}},"dynamic-route":{t:0,b:{t:2,i:[{t:3}],s:"عرض لتوجيهات ديناميكية"}},hi:{t:0,b:{t:2,i:[{t:3,v:"مرحبا "},{t:4,k:"name"}]}},aka:{t:0,b:{t:2,i:[{t:3}],s:"معروف أيضا تحت مسمى"}},"whats-your-name":{t:0,b:{t:2,i:[{t:3}],s:"ما إسمك؟"}}},"not-found":{t:0,b:{t:2,i:[{t:3}],s:"صفحة غير موجودة"}}},lS=Object.freeze(Object.defineProperty({__proto__:null,default:iS},Symbol.toStringTag,{value:"Module"})),aS={button:{about:{t:0,b:{t:2,i:[{t:3}],s:"Über"}},back:{t:0,b:{t:2,i:[{t:3}],s:"Zurück"}},go:{t:0,b:{t:2,i:[{t:3}],s:"Los"}},home:{t:0,b:{t:2,i:[{t:3}],s:"Startseite"}},toggle_dark:{t:0,b:{t:2,i:[{t:3}],s:"Dunkelmodus umschalten"}},toggle_langs:{t:0,b:{t:2,i:[{t:3}],s:"Sprachen ändern"}}},intro:{desc:{t:0,b:{t:2,i:[{t:3}],s:"Vite Startvorlage mit Vorlieben"}},"dynamic-route":{t:0,b:{t:2,i:[{t:3}],s:"Demo einer dynamischen Route"}},hi:{t:0,b:{t:2,i:[{t:3,v:"Hi, "},{t:4,k:"name"},{t:3,v:"!"}]}},aka:{t:0,b:{t:2,i:[{t:3}],s:"Auch bekannt als"}},"whats-your-name":{t:0,b:{t:2,i:[{t:3}],s:"Wie heißt du?"}}},"not-found":{t:0,b:{t:2,i:[{t:3}],s:"Nicht gefunden"}}},sS=Object.freeze(Object.defineProperty({__proto__:null,default:aS},Symbol.toStringTag,{value:"Module"})),cS={button:{about:{t:0,b:{t:2,i:[{t:3}],s:"About"}},back:{t:0,b:{t:2,i:[{t:3}],s:"Back"}},go:{t:0,b:{t:2,i:[{t:3}],s:"GO"}},home:{t:0,b:{t:2,i:[{t:3}],s:"Home"}},toggle_dark:{t:0,b:{t:2,i:[{t:3}],s:"Toggle dark mode"}},toggle_langs:{t:0,b:{t:2,i:[{t:3}],s:"Change languages"}}},intro:{desc:{t:0,b:{t:2,i:[{t:3}],s:"Opinionated Vite Starter Template"}},"dynamic-route":{t:0,b:{t:2,i:[{t:3}],s:"Demo of dynamic route"}},hi:{t:0,b:{t:2,i:[{t:3,v:"Hi, "},{t:4,k:"name"},{t:3,v:"!"}]}},aka:{t:0,b:{t:2,i:[{t:3}],s:"Also known as"}},"whats-your-name":{t:0,b:{t:2,i:[{t:3}],s:"What's your name?"}}},"not-found":{t:0,b:{t:2,i:[{t:3}],s:"Not found"}}},uS=Object.freeze(Object.defineProperty({__proto__:null,default:cS},Symbol.toStringTag,{value:"Module"})),fS={button:{about:{t:0,b:{t:2,i:[{t:3}],s:"Acerca de"}},back:{t:0,b:{t:2,i:[{t:3}],s:"Atrás"}},go:{t:0,b:{t:2,i:[{t:3}],s:"Ir"}},home:{t:0,b:{t:2,i:[{t:3}],s:"Inicio"}},toggle_dark:{t:0,b:{t:2,i:[{t:3}],s:"Alternar modo oscuro"}},toggle_langs:{t:0,b:{t:2,i:[{t:3}],s:"Cambiar idiomas"}}},intro:{desc:{t:0,b:{t:2,i:[{t:3}],s:"Plantilla de Inicio de Vite Dogmática"}},"dynamic-route":{t:0,b:{t:2,i:[{t:3}],s:"Demo de ruta dinámica"}},hi:{t:0,b:{t:2,i:[{t:3,v:"¡Hola, "},{t:4,k:"name"},{t:3,v:"!"}]}},aka:{t:0,b:{t:2,i:[{t:3}],s:"También conocido como"}},"whats-your-name":{t:0,b:{t:2,i:[{t:3}],s:"¿Cómo te llamas?"}}},"not-found":{t:0,b:{t:2,i:[{t:3}],s:"No se ha encontrado"}}},dS=Object.freeze(Object.defineProperty({__proto__:null,default:fS},Symbol.toStringTag,{value:"Module"})),pS={button:{about:{t:0,b:{t:2,i:[{t:3}],s:"À propos"}},back:{t:0,b:{t:2,i:[{t:3}],s:"Retour"}},go:{t:0,b:{t:2,i:[{t:3}],s:"Essayer"}},home:{t:0,b:{t:2,i:[{t:3}],s:"Accueil"}},toggle_dark:{t:0,b:{t:2,i:[{t:3}],s:"Basculer en mode sombre"}},toggle_langs:{t:0,b:{t:2,i:[{t:3}],s:"Changer de langue"}}},intro:{desc:{t:0,b:{t:2,i:[{t:3}],s:"Exemple d'application Vite"}},"dynamic-route":{t:0,b:{t:2,i:[{t:3}],s:"Démo de route dynamique"}},hi:{t:0,b:{t:2,i:[{t:3,v:"Salut, "},{t:4,k:"name"},{t:3,v:"!"}]}},aka:{t:0,b:{t:2,i:[{t:3}],s:"Aussi connu sous le nom de"}},"whats-your-name":{t:0,b:{t:2,i:[{t:3}],s:"Comment t'appelles-tu ?"}}},"not-found":{t:0,b:{t:2,i:[{t:3}],s:"Page non trouvée"}}},hS=Object.freeze(Object.defineProperty({__proto__:null,default:pS},Symbol.toStringTag,{value:"Module"})),mS={button:{about:{t:0,b:{t:2,i:[{t:3}],s:"Tentang"}},back:{t:0,b:{t:2,i:[{t:3}],s:"Kembali"}},go:{t:0,b:{t:2,i:[{t:3}],s:"Pergi"}},home:{t:0,b:{t:2,i:[{t:3}],s:"Beranda"}},toggle_dark:{t:0,b:{t:2,i:[{t:3}],s:"Ubah ke mode gelap"}},toggle_langs:{t:0,b:{t:2,i:[{t:3}],s:"Ubah bahasa"}}},intro:{desc:{t:0,b:{t:2,i:[{t:3}],s:"Template awal vite"}},"dynamic-route":{t:0,b:{t:2,i:[{t:3}],s:"Contoh rute dinamik"}},hi:{t:0,b:{t:2,i:[{t:3,v:"Halo, "},{t:4,k:"name"},{t:3,v:"!"}]}},aka:{t:0,b:{t:2,i:[{t:3}],s:"Juga diketahui sebagai"}},"whats-your-name":{t:0,b:{t:2,i:[{t:3}],s:"Siapa nama anda?"}}},"not-found":{t:0,b:{t:2,i:[{t:3}],s:"Tidak ditemukan"}}},gS=Object.freeze(Object.defineProperty({__proto__:null,default:mS},Symbol.toStringTag,{value:"Module"})),vS={button:{about:{t:0,b:{t:2,i:[{t:3}],s:"Su di me"}},back:{t:0,b:{t:2,i:[{t:3}],s:"Indietro"}},go:{t:0,b:{t:2,i:[{t:3}],s:"Vai"}},home:{t:0,b:{t:2,i:[{t:3}],s:"Home"}},toggle_dark:{t:0,b:{t:2,i:[{t:3}],s:"Attiva/disattiva modalità scura"}},toggle_langs:{t:0,b:{t:2,i:[{t:3}],s:"Cambia lingua"}}},intro:{desc:{t:0,b:{t:2,i:[{t:3}],s:"Modello per una Applicazione Vite"}},"dynamic-route":{t:0,b:{t:2,i:[{t:3}],s:"Demo di rotta dinamica"}},hi:{t:0,b:{t:2,i:[{t:3,v:"Ciao, "},{t:4,k:"name"},{t:3,v:"!"}]}},"whats-your-name":{t:0,b:{t:2,i:[{t:3}],s:"Come ti chiami?"}}},"not-found":{t:0,b:{t:2,i:[{t:3}],s:"Non trovato"}}},_S=Object.freeze(Object.defineProperty({__proto__:null,default:vS},Symbol.toStringTag,{value:"Module"})),bS={button:{about:{t:0,b:{t:2,i:[{t:3}],s:"これは？"}},back:{t:0,b:{t:2,i:[{t:3}],s:"戻る"}},go:{t:0,b:{t:2,i:[{t:3}],s:"進む"}},home:{t:0,b:{t:2,i:[{t:3}],s:"ホーム"}},toggle_dark:{t:0,b:{t:2,i:[{t:3}],s:"ダークモード切り替え"}},toggle_langs:{t:0,b:{t:2,i:[{t:3}],s:"言語切り替え"}}},intro:{desc:{t:0,b:{t:2,i:[{t:3}],s:"固執された Vite スターターテンプレート"}},"dynamic-route":{t:0,b:{t:2,i:[{t:3}],s:"動的ルートのデモ"}},hi:{t:0,b:{t:2,i:[{t:3,v:"こんにちは、"},{t:4,k:"name"},{t:3,v:"!"}]}},"whats-your-name":{t:0,b:{t:2,i:[{t:3}],s:"君の名は。"}}},"not-found":{t:0,b:{t:2,i:[{t:3}],s:"見つかりませんでした"}}},yS=Object.freeze(Object.defineProperty({__proto__:null,default:bS},Symbol.toStringTag,{value:"Module"})),ES={button:{about:{t:0,b:{t:2,i:[{t:3}],s:"შესახებ"}},back:{t:0,b:{t:2,i:[{t:3}],s:"უკან"}},go:{t:0,b:{t:2,i:[{t:3}],s:"დაწყება"}},home:{t:0,b:{t:2,i:[{t:3}],s:"მთავარი"}},toggle_dark:{t:0,b:{t:2,i:[{t:3}],s:"გადართე მუქი რეჟიმი"}},toggle_langs:{t:0,b:{t:2,i:[{t:3}],s:"ენის შეცვლა"}}},intro:{desc:{t:0,b:{t:2,i:[{t:3}],s:"Opinionated Vite Starter Template"}},"dynamic-route":{t:0,b:{t:2,i:[{t:3}],s:"დინამიური როუტინგის დემო"}},hi:{t:0,b:{t:2,i:[{t:3,v:"გამარჯობა, "},{t:4,k:"name"},{t:3,v:"!"}]}},aka:{t:0,b:{t:2,i:[{t:3}],s:"ასევე ცნობილი როგორც"}},"whats-your-name":{t:0,b:{t:2,i:[{t:3}],s:"რა გქვია?"}}},"not-found":{t:0,b:{t:2,i:[{t:3}],s:"ვერ მოიძებნა"}}},SS=Object.freeze(Object.defineProperty({__proto__:null,default:ES},Symbol.toStringTag,{value:"Module"})),CS={button:{about:{t:0,b:{t:2,i:[{t:3}],s:"소개"}},back:{t:0,b:{t:2,i:[{t:3}],s:"뒤로가기"}},go:{t:0,b:{t:2,i:[{t:3}],s:"이동"}},home:{t:0,b:{t:2,i:[{t:3}],s:"홈"}},toggle_dark:{t:0,b:{t:2,i:[{t:3}],s:"다크모드 토글"}},toggle_langs:{t:0,b:{t:2,i:[{t:3}],s:"언어 변경"}}},intro:{desc:{t:0,b:{t:2,i:[{t:3}],s:"Vite 애플리케이션 템플릿"}},"dynamic-route":{t:0,b:{t:2,i:[{t:3}],s:"다이나믹 라우트 데모"}},hi:{t:0,b:{t:2,i:[{t:3,v:"안녕, "},{t:4,k:"name"},{t:3,v:"!"}]}},"whats-your-name":{t:0,b:{t:2,i:[{t:3}],s:"이름이 뭐예요?"}}},"not-found":{t:0,b:{t:2,i:[{t:3}],s:"찾을 수 없습니다"}}},TS=Object.freeze(Object.defineProperty({__proto__:null,default:CS},Symbol.toStringTag,{value:"Module"})),wS={button:{about:{t:0,b:{t:2,i:[{t:3}],s:"O nas"}},back:{t:0,b:{t:2,i:[{t:3}],s:"Wróć"}},go:{t:0,b:{t:2,i:[{t:3}],s:"WEJDŹ"}},home:{t:0,b:{t:2,i:[{t:3}],s:"Strona główna"}},toggle_dark:{t:0,b:{t:2,i:[{t:3}],s:"Ustaw tryb nocny"}},toggle_langs:{t:0,b:{t:2,i:[{t:3}],s:"Zmień język"}}},intro:{desc:{t:0,b:{t:2,i:[{t:3}],s:"Opiniowany szablon startowy Vite"}},"dynamic-route":{t:0,b:{t:2,i:[{t:3}],s:"Demonstracja dynamicznego route"}},hi:{t:0,b:{t:2,i:[{t:3,v:"Cześć, "},{t:4,k:"name"},{t:3,v:"!"}]}},aka:{t:0,b:{t:2,i:[{t:3}],s:"Znany też jako"}},"whats-your-name":{t:0,b:{t:2,i:[{t:3}],s:"Jak masz na imię?"}}},"not-found":{t:0,b:{t:2,i:[{t:3}],s:"Nie znaleziono"}}},kS=Object.freeze(Object.defineProperty({__proto__:null,default:wS},Symbol.toStringTag,{value:"Module"})),OS={button:{about:{t:0,b:{t:2,i:[{t:3}],s:"Sobre"}},back:{t:0,b:{t:2,i:[{t:3}],s:"Voltar"}},go:{t:0,b:{t:2,i:[{t:3}],s:"Ir"}},home:{t:0,b:{t:2,i:[{t:3}],s:"Início"}},toggle_dark:{t:0,b:{t:2,i:[{t:3}],s:"Alternar modo escuro"}},toggle_langs:{t:0,b:{t:2,i:[{t:3}],s:"Mudar de idioma"}}},intro:{desc:{t:0,b:{t:2,i:[{t:3}],s:"Modelo Opinativo de Partida de Vite"}},"dynamic-route":{t:0,b:{t:2,i:[{t:3}],s:"Demonstração de rota dinâmica"}},hi:{t:0,b:{t:2,i:[{t:3,v:"Olá, "},{t:4,k:"name"},{t:3,v:"!"}]}},aka:{t:0,b:{t:2,i:[{t:3}],s:"Também conhecido como"}},"whats-your-name":{t:0,b:{t:2,i:[{t:3}],s:"Qual é o seu nome?"}}},"not-found":{t:0,b:{t:2,i:[{t:3}],s:"Não encontrado"}}},PS=Object.freeze(Object.defineProperty({__proto__:null,default:OS},Symbol.toStringTag,{value:"Module"})),AS={button:{about:{t:0,b:{t:2,i:[{t:3}],s:"О шаблоне"}},back:{t:0,b:{t:2,i:[{t:3}],s:"Назад"}},go:{t:0,b:{t:2,i:[{t:3}],s:"Перейти"}},home:{t:0,b:{t:2,i:[{t:3}],s:"Главная"}},toggle_dark:{t:0,b:{t:2,i:[{t:3}],s:"Включить темный режим"}},toggle_langs:{t:0,b:{t:2,i:[{t:3}],s:"Сменить язык"}}},intro:{desc:{t:0,b:{t:2,i:[{t:3}],s:"Самостоятельный начальный шаблон Vite"}},"dynamic-route":{t:0,b:{t:2,i:[{t:3}],s:"Демо динамического маршрута"}},hi:{t:0,b:{t:2,i:[{t:3,v:"Привет, "},{t:4,k:"name"},{t:3,v:"!"}]}},"whats-your-name":{t:0,b:{t:2,i:[{t:3}],s:"Как тебя зовут?"}}},"not-found":{t:0,b:{t:2,i:[{t:3}],s:"Не найден"}}},IS=Object.freeze(Object.defineProperty({__proto__:null,default:AS},Symbol.toStringTag,{value:"Module"})),xS={button:{about:{t:0,b:{t:2,i:[{t:3}],s:"Hakkımda"}},back:{t:0,b:{t:2,i:[{t:3}],s:"Geri"}},go:{t:0,b:{t:2,i:[{t:3}],s:"İLERİ"}},home:{t:0,b:{t:2,i:[{t:3}],s:"Anasayfa"}},toggle_dark:{t:0,b:{t:2,i:[{t:3}],s:"Karanlık modu değiştir"}},toggle_langs:{t:0,b:{t:2,i:[{t:3}],s:"Dilleri değiştir"}}},intro:{desc:{t:0,b:{t:2,i:[{t:3}],s:"Görüşlü Vite Başlangıç Şablonu"}},"dynamic-route":{t:0,b:{t:2,i:[{t:3}],s:"Dinamik rota demosu"}},hi:{t:0,b:{t:2,i:[{t:3,v:"Merhaba, "},{t:4,k:"name"},{t:3,v:"!"}]}},aka:{t:0,b:{t:2,i:[{t:3}],s:"Ayrıca şöyle bilinir"}},"whats-your-name":{t:0,b:{t:2,i:[{t:3}],s:"Adınız nedir?"}}},"not-found":{t:0,b:{t:2,i:[{t:3}],s:"Bulunamadı"}}},LS=Object.freeze(Object.defineProperty({__proto__:null,default:xS},Symbol.toStringTag,{value:"Module"})),DS={button:{about:{t:0,b:{t:2,i:[{t:3}],s:"Про шаблон"}},back:{t:0,b:{t:2,i:[{t:3}],s:"Назад"}},go:{t:0,b:{t:2,i:[{t:3}],s:"Перейти"}},home:{t:0,b:{t:2,i:[{t:3}],s:"Головна"}},toggle_dark:{t:0,b:{t:2,i:[{t:3}],s:"Переключити темний режим"}},toggle_langs:{t:0,b:{t:2,i:[{t:3}],s:"Змінити мову"}}},intro:{desc:{t:0,b:{t:2,i:[{t:3}],s:"Самостійний початковий шаблон Vite"}},"dynamic-route":{t:0,b:{t:2,i:[{t:3}],s:"Демо динамічного маршруту"}},hi:{t:0,b:{t:2,i:[{t:3,v:"Привіт, "},{t:4,k:"name"},{t:3,v:"!"}]}},"whats-your-name":{t:0,b:{t:2,i:[{t:3}],s:"Як тебе звати?"}}},"not-found":{t:0,b:{t:2,i:[{t:3}],s:"Не знайдено"}}},RS=Object.freeze(Object.defineProperty({__proto__:null,default:DS},Symbol.toStringTag,{value:"Module"})),NS={button:{about:{t:0,b:{t:2,i:[{t:3}],s:"Haqida"}},back:{t:0,b:{t:2,i:[{t:3}],s:"Orqaga"}},go:{t:0,b:{t:2,i:[{t:3}],s:"Kettik"}},home:{t:0,b:{t:2,i:[{t:3}],s:"Bosh sahifa"}},toggle_dark:{t:0,b:{t:2,i:[{t:3}],s:"Qorong‘i rejimga o‘tish"}},toggle_langs:{t:0,b:{t:2,i:[{t:3}],s:"Tilni o‘zgartirish"}}},intro:{desc:{t:0,b:{t:2,i:[{t:3}],s:"O‘ylangan boshlang‘ich Vite shabloni"}},"dynamic-route":{t:0,b:{t:2,i:[{t:3}],s:"Dynamic route demo'si"}},hi:{t:0,b:{t:2,i:[{t:3,v:"Assalomu alaykum, "},{t:4,k:"name"},{t:3,v:"!"}]}},aka:{t:0,b:{t:2,i:[{t:3}],s:"shuningdek"}},"whats-your-name":{t:0,b:{t:2,i:[{t:3}],s:"Ismingiz nima?"}}},"not-found":{t:0,b:{t:2,i:[{t:3}],s:"Topilmadi"}}},MS=Object.freeze(Object.defineProperty({__proto__:null,default:NS},Symbol.toStringTag,{value:"Module"})),VS={button:{about:{t:0,b:{t:2,i:[{t:3}],s:"Về"}},back:{t:0,b:{t:2,i:[{t:3}],s:"Quay lại"}},go:{t:0,b:{t:2,i:[{t:3}],s:"Đi"}},home:{t:0,b:{t:2,i:[{t:3}],s:"Khởi đầu"}},toggle_dark:{t:0,b:{t:2,i:[{t:3}],s:"Chuyển đổi chế độ tối"}},toggle_langs:{t:0,b:{t:2,i:[{t:3}],s:"Thay đổi ngôn ngữ"}}},intro:{desc:{t:0,b:{t:2,i:[{t:3}],s:"Ý kiến cá nhân Vite Template để bắt đầu"}},"dynamic-route":{t:0,b:{t:2,i:[{t:3}],s:"Bản giới thiệu về dynamic route"}},hi:{t:0,b:{t:2,i:[{t:3,v:"Hi, "},{t:4,k:"name"},{t:3,v:"!"}]}},"whats-your-name":{t:0,b:{t:2,i:[{t:3}],s:"Tên bạn là gì?"}}},"not-found":{t:0,b:{t:2,i:[{t:3}],s:"Không tìm thấy"}}},$S=Object.freeze(Object.defineProperty({__proto__:null,default:VS},Symbol.toStringTag,{value:"Module"})),FS={button:{about:{t:0,b:{t:2,i:[{t:3}],s:"关于"}},back:{t:0,b:{t:2,i:[{t:3}],s:"返回"}},go:{t:0,b:{t:2,i:[{t:3}],s:"确定"}},home:{t:0,b:{t:2,i:[{t:3}],s:"首页"}},toggle_dark:{t:0,b:{t:2,i:[{t:3}],s:"切换深色模式"}},toggle_langs:{t:0,b:{t:2,i:[{t:3}],s:"切换语言"}}},intro:{desc:{t:0,b:{t:2,i:[{t:3}],s:"固执己见的 Vite 项目模板"}},"dynamic-route":{t:0,b:{t:2,i:[{t:3}],s:"动态路由演示"}},hi:{t:0,b:{t:2,i:[{t:3,v:"你好，"},{t:4,k:"name"}]}},aka:{t:0,b:{t:2,i:[{t:3}],s:"也叫"}},"whats-your-name":{t:0,b:{t:2,i:[{t:3}],s:"输入你的名字"}}},"not-found":{t:0,b:{t:2,i:[{t:3}],s:"未找到页面"}}},BS=Object.freeze(Object.defineProperty({__proto__:null,default:FS},Symbol.toStringTag,{value:"Module"}));function US(e={}){const{immediate:t=!1,onNeedRefresh:n,onOfflineReady:o,onRegistered:r,onRegisteredSW:i,onRegisterError:l}=e;let s,a;const c=async(u=!0)=>{await a};async function f(){if("serviceWorker"in navigator){if(s=await Se(async()=>{const{Workbox:u}=await Promise.resolve().then(()=>Z1);return{Workbox:u}},void 0).then(({Workbox:u})=>new u("/remote-configs-en/sw.js",{scope:"/remote-configs-en/",type:"classic"})).catch(u=>{l==null||l(u)}),!s)return;s.addEventListener("activated",u=>{(u.isUpdate||u.isExternal)&&window.location.reload()}),s.addEventListener("installed",u=>{u.isUpdate||o==null||o()}),s.register({immediate:t}).then(u=>{i?i("/remote-configs-en/sw.js",u):r==null||r(u)}).catch(u=>{l==null||l(u)})}}return a=f(),c}const jS=Object.freeze(Object.defineProperty({__proto__:null,registerSW:US},Symbol.toStringTag,{value:"Module"})),HS={p:"x4 y10",text:"center teal-700 dark:gray-200"},WS=Pe("div",{"text-4xl":""},[Pe("div",{"i-carbon-warning":"","inline-block":""})],-1),GS=ue({__name:"404",setup(e){const t=co(),{t:n}=ni();return(o,r)=>{const i=El("RouterView");return le(),kt("main",HS,[WS,m(i),Pe("div",null,[Pe("button",{"text-sm":"",btn:"",m:"3 t8",onClick:r[0]||(r[0]=l=>E(t).back())},xn(E(n)("button.back")),1)])])}}}),KS=Object.freeze(Object.defineProperty({__proto__:null,default:GS},Symbol.toStringTag,{value:"Module"}));function Wa(){}const et=Object.assign,Il=typeof window<"u",oi=e=>e!==null&&typeof e=="object",ut=e=>e!=null,Yr=e=>typeof e=="function",ph=e=>oi(e)&&Yr(e.then)&&Yr(e.catch),hh=e=>typeof e=="number"||/^\d+(\.\d+)?$/.test(e),YS=()=>Il?/ios|iphone|ipad|ipod/.test(navigator.userAgent.toLowerCase()):!1;function Hu(e,t){const n=t.split(".");let o=e;return n.forEach(r=>{var i;o=oi(o)&&(i=o[r])!=null?i:""}),o}function zr(e,t,n){return t.reduce((o,r)=>(o[r]=e[r],o),{})}const Zl=(e,t)=>JSON.stringify(e)===JSON.stringify(t),Wu=e=>Array.isArray(e)?e:[e],Dt=null,Fe=[Number,String],ht={type:Boolean,default:!0},Er=e=>({type:e,required:!0}),ol=()=>({type:Array,default:()=>[]}),zS=e=>({type:Number,default:e}),Lr=e=>({type:Fe,default:e}),je=e=>({type:String,default:e});var Vs=typeof window<"u",XS=e=>e===window,Gu=(e,t)=>({top:0,left:0,right:e,bottom:t,width:e,height:t}),qS=e=>{const t=E(e);if(XS(t)){const n=t.innerWidth,o=t.innerHeight;return Gu(n,o)}return t!=null&&t.getBoundingClientRect?t.getBoundingClientRect():Gu(0,0)};function ri(e){const t=yt(e,null);if(t){const n=Et(),{link:o,unlink:r,internalChildren:i}=t;o(n),wo(()=>r(n));const l=he(()=>i.indexOf(n));return{parent:t,index:l}}return{parent:null,index:fe(-1)}}function JS(e){const t=[],n=o=>{Array.isArray(o)&&o.forEach(r=>{var i;er(r)&&(t.push(r),(i=r.component)!=null&&i.subTree&&(t.push(r.component.subTree),n(r.component.subTree.children)),r.children&&n(r.children))})};return n(e),t}var Ku=(e,t)=>{const n=e.indexOf(t);return n===-1?e.findIndex(o=>t.key!==void 0&&t.key!==null&&o.type===t.type&&o.key===t.key):n};function QS(e,t,n){const o=JS(e.subTree.children);n.sort((i,l)=>Ku(o,i.vnode)-Ku(o,l.vnode));const r=n.map(i=>i.proxy);t.sort((i,l)=>{const s=r.indexOf(i),a=r.indexOf(l);return s-a})}function $s(e){const t=un([]),n=un([]),o=Et();return{children:t,linkChildren:i=>{Qn(e,Object.assign({link:a=>{a.proxy&&(n.push(a),t.push(a.proxy),QS(o,t,n))},unlink:a=>{const c=n.indexOf(a);t.splice(c,1),n.splice(c,1)},children:t,internalChildren:n},i))}}}function mh(e){let t;Pt(()=>{e(),bt(()=>{t=!0})}),Qr(()=>{t&&e()})}function ii(e,t,n={}){if(!Vs)return;const{target:o=window,passive:r=!1,capture:i=!1}=n;let l=!1,s;const a=u=>{if(l)return;const d=E(u);d&&!s&&(d.addEventListener(e,t,{capture:i,passive:r}),s=!0)},c=u=>{if(l)return;const d=E(u);d&&s&&(d.removeEventListener(e,t,i),s=!1)};wo(()=>c(o)),cr(()=>c(o)),mh(()=>a(o));let f;return Ge(o)&&(f=De(o,(u,d)=>{c(d),a(u)})),()=>{f==null||f(),c(o),l=!0}}var Ci,ea;function ZS(){if(!Ci&&(Ci=fe(0),ea=fe(0),Vs)){const e=()=>{Ci.value=window.innerWidth,ea.value=window.innerHeight};e(),window.addEventListener("resize",e,{passive:!0}),window.addEventListener("orientationchange",e,{passive:!0})}return{width:Ci,height:ea}}var eC=/scroll|auto|overlay/i,tC=Vs?window:void 0;function nC(e){return e.tagName!=="HTML"&&e.tagName!=="BODY"&&e.nodeType===1}function oC(e,t=tC){let n=e;for(;n&&n!==t&&nC(n);){const{overflowY:o}=window.getComputedStyle(n);if(eC.test(o))return n;n=n.parentNode}return t}var gh=Symbol("van-field");function vh(e){const t=yt(gh,null);t&&!t.customValue.value&&(t.customValue.value=e,De(e,()=>{t.resetValidation(),t.validateWithTrigger("onChange")}))}function Yu(e,t){"scrollTop"in e?e.scrollTop=t:e.scrollTo(e.scrollX,t)}function _h(){return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0}function bh(e){Yu(window,e),Yu(document.body,e)}const rC=YS();function iC(){rC&&bh(_h())}const lC=e=>e.stopPropagation();function eo(e,t){(typeof e.cancelable!="boolean"||e.cancelable)&&e.preventDefault(),t&&lC(e)}const{width:yh,height:Eh}=ZS();function ft(e){if(ut(e))return hh(e)?`${e}px`:String(e)}function aC(e){if(ut(e)){if(Array.isArray(e))return{width:ft(e[0]),height:ft(e[1])};const t=ft(e);return{width:t,height:t}}}function Sh(e){const t={};return e!==void 0&&(t.zIndex=+e),t}let ta;function sC(){if(!ta){const e=document.documentElement,t=e.style.fontSize||window.getComputedStyle(e).fontSize;ta=parseFloat(t)}return ta}function cC(e){return e=e.replace(/rem/g,""),+e*sC()}function uC(e){return e=e.replace(/vw/g,""),+e*yh.value/100}function fC(e){return e=e.replace(/vh/g,""),+e*Eh.value/100}function dC(e){if(typeof e=="number")return e;if(Il){if(e.includes("rem"))return cC(e);if(e.includes("vw"))return uC(e);if(e.includes("vh"))return fC(e)}return parseFloat(e)}const pC=/-(\w)/g,Ch=e=>e.replace(pC,(t,n)=>n.toUpperCase()),hC=e=>e.replace(/([A-Z])/g,"-$1").toLowerCase().replace(/^-/,""),rl=(e,t,n)=>Math.min(Math.max(e,t),n);function zu(e,t,n){const o=e.indexOf(t);return o===-1?e:t==="-"&&o!==0?e.slice(0,o):e.slice(0,o+1)+e.slice(o).replace(n,"")}function mC(e,t=!0,n=!0){t?e=zu(e,".",/\./g):e=e.split(".")[0],n?e=zu(e,"-",/-/g):e=e.replace(/-/,"");const o=t?/[^-0-9.]/g:/[^-0-9]/g;return e.replace(o,"")}const{hasOwnProperty:gC}=Object.prototype;function vC(e,t,n){const o=t[n];ut(o)&&(!gC.call(e,n)||!oi(o)?e[n]=o:e[n]=Th(Object(e[n]),o))}function Th(e,t){return Object.keys(t).forEach(n=>{vC(e,t,n)}),e}var _C={name:"姓名",tel:"电话",save:"保存",clear:"清空",cancel:"取消",confirm:"确认",delete:"删除",loading:"加载中...",noCoupon:"暂无优惠券",nameEmpty:"请填写姓名",addContact:"添加联系人",telInvalid:"请填写正确的电话",vanCalendar:{end:"结束",start:"开始",title:"日期选择",weekdays:["日","一","二","三","四","五","六"],monthTitle:(e,t)=>`${e}年${t}月`,rangePrompt:e=>`最多选择 ${e} 天`},vanCascader:{select:"请选择"},vanPagination:{prev:"上一页",next:"下一页"},vanPullRefresh:{pulling:"下拉即可刷新...",loosing:"释放即可刷新..."},vanSubmitBar:{label:"合计:"},vanCoupon:{unlimited:"无门槛",discount:e=>`${e}折`,condition:e=>`满${e}元可用`},vanCouponCell:{title:"优惠券",count:e=>`${e}张可用`},vanCouponList:{exchange:"兑换",close:"不使用",enable:"可用",disabled:"不可用",placeholder:"输入优惠码"},vanAddressEdit:{area:"地区",areaEmpty:"请选择地区",addressEmpty:"请填写详细地址",addressDetail:"详细地址",defaultAddress:"设为默认收货地址"},vanAddressList:{add:"新增地址"}};const Xu=fe("zh-CN"),qu=un({"zh-CN":_C}),bC={messages(){return qu[Xu.value]},use(e,t){Xu.value=e,this.add({[e]:t})},add(e={}){Th(qu,e)}};var yC=bC;function EC(e){const t=Ch(e)+".";return(n,...o)=>{const r=yC.messages(),i=Hu(r,t+n)||Hu(r,n);return Yr(i)?i(...o):i}}function Ga(e,t){return t?typeof t=="string"?` ${e}--${t}`:Array.isArray(t)?t.reduce((n,o)=>n+Ga(e,o),""):Object.keys(t).reduce((n,o)=>n+(t[o]?Ga(e,o):""),""):""}function SC(e){return(t,n)=>(t&&typeof t!="string"&&(n=t,t=""),t=t?`${e}__${t}`:e,`${t}${Ga(t,n)}`)}function Je(e){const t=`van-${e}`;return[t,SC(t),EC(t)]}const fr="van-hairline",CC=`${fr}--top`,TC=`${fr}--left`,wC=`${fr}--bottom`,kC=`${fr}--surround`,OC=`${fr}--top-bottom`,PC=`${fr}-unset--top-bottom`,lr="van-haptics-feedback",AC=Symbol("van-form"),Ju=5;function wh(e,{args:t=[],done:n,canceled:o,error:r}){if(e){const i=e.apply(null,t);ph(i)?i.then(l=>{l?n():o&&o()}).catch(r||Wa):i?n():o&&o()}else n()}function st(e){return e.install=t=>{const{name:n}=e;n&&(t.component(n,e),t.component(Ch(`-${n}`),e))},e}const kh=Symbol();function IC(e){const t=yt(kh,null);t&&De(t,n=>{n&&e()})}const xC=(e,t)=>{const n=fe(),o=()=>{n.value=qS(e).height};return Pt(()=>{bt(o);for(let r=1;r<=3;r++)setTimeout(o,100*r)}),IC(()=>bt(o)),De([yh,Eh],o),n};function Oh(e,t){const n=xC(e);return o=>m("div",{class:t("placeholder"),style:{height:n.value?`${n.value}px`:void 0}},[o()])}const[Ph,Qu]=Je("action-bar"),Ah=Symbol(Ph),LC={placeholder:Boolean,safeAreaInsetBottom:ht};var DC=ue({name:Ph,props:LC,setup(e,{slots:t}){const n=fe(),o=Oh(n,Qu),{linkChildren:r}=$s(Ah);r();const i=()=>{var l;return m("div",{ref:n,class:[Qu(),{"van-safe-area-bottom":e.safeAreaInsetBottom}]},[(l=t.default)==null?void 0:l.call(t)])};return()=>e.placeholder?o(i):i()}});const RC=st(DC);function dr(e){const t=Et();t&&et(t.proxy,e)}const Fs={to:[String,Object],url:String,replace:Boolean};function NC({to:e,url:t,replace:n,$router:o}){e&&o?o[n?"replace":"push"](e):t&&(n?location.replace(t):location.href=t)}function Bs(){const e=Et().proxy;return()=>NC(e)}const[MC,Zu]=Je("badge"),VC={dot:Boolean,max:Fe,tag:je("div"),color:String,offset:Array,content:Fe,showZero:ht,position:je("top-right")};var $C=ue({name:MC,props:VC,setup(e,{slots:t}){const n=()=>{if(t.content)return!0;const{content:s,showZero:a}=e;return ut(s)&&s!==""&&(a||s!==0&&s!=="0")},o=()=>{const{dot:s,max:a,content:c}=e;if(!s&&n())return t.content?t.content():ut(a)&&hh(c)&&+c>+a?`${a}+`:c},r=s=>s.startsWith("-")?s.replace("-",""):`-${s}`,i=he(()=>{const s={background:e.color};if(e.offset){const[a,c]=e.offset,{position:f}=e,[u,d]=f.split("-");t.default?(typeof c=="number"?s[u]=ft(u==="top"?c:-c):s[u]=u==="top"?ft(c):r(c),typeof a=="number"?s[d]=ft(d==="left"?a:-a):s[d]=d==="left"?ft(a):r(a)):(s.marginTop=ft(c),s.marginLeft=ft(a))}return s}),l=()=>{if(n()||e.dot)return m("div",{class:Zu([e.position,{dot:e.dot,fixed:!!t.default}]),style:i.value},[o()])};return()=>{if(t.default){const{tag:s}=e;return m(s,{class:Zu("wrapper")},{default:()=>[t.default(),l()]})}return l()}}});const FC=st($C);let Ih=2e3;const BC=()=>++Ih,UC=e=>{Ih=e},[xh,jC]=Je("config-provider"),Lh=Symbol(xh),HC={tag:je("div"),theme:je("light"),zIndex:Number,themeVars:Object,themeVarsDark:Object,themeVarsLight:Object,themeVarsScope:je("local"),iconPrefix:String};function WC(e){return e.replace(/([a-zA-Z])(\d)/g,"$1-$2")}function GC(e){const t={};return Object.keys(e).forEach(n=>{const o=WC(hC(n));t[`--van-${o}`]=e[n]}),t}function Ti(e={},t={}){Object.keys(e).forEach(n=>{e[n]!==t[n]&&document.documentElement.style.setProperty(n,e[n])}),Object.keys(t).forEach(n=>{e[n]||document.documentElement.style.removeProperty(n)})}var KC=ue({name:xh,props:HC,setup(e,{slots:t}){const n=he(()=>GC(et({},e.themeVars,e.theme==="dark"?e.themeVarsDark:e.themeVarsLight)));if(Il){const o=()=>{document.documentElement.classList.add(`van-theme-${e.theme}`)},r=(i=e.theme)=>{document.documentElement.classList.remove(`van-theme-${i}`)};De(()=>e.theme,(i,l)=>{l&&r(l),o()},{immediate:!0}),Qr(o),cr(r),Zr(r),De(n,(i,l)=>{e.themeVarsScope==="global"&&Ti(i,l)}),De(()=>e.themeVarsScope,(i,l)=>{l==="global"&&Ti({},n.value),i==="global"&&Ti(n.value,{})}),e.themeVarsScope==="global"&&Ti(n.value,{})}return Qn(Lh,e),Cl(()=>{e.zIndex!==void 0&&UC(e.zIndex)}),()=>m(e.tag,{class:jC(),style:e.themeVarsScope==="local"?n.value:void 0},{default:()=>{var o;return[(o=t.default)==null?void 0:o.call(t)]}})}});const[YC,ef]=Je("icon"),zC=e=>e==null?void 0:e.includes("/"),XC={dot:Boolean,tag:je("i"),name:String,size:Fe,badge:Fe,color:String,badgeProps:Object,classPrefix:String};var qC=ue({name:YC,props:XC,setup(e,{slots:t}){const n=yt(Lh,null),o=he(()=>e.classPrefix||(n==null?void 0:n.iconPrefix)||ef());return()=>{const{tag:r,dot:i,name:l,size:s,badge:a,color:c}=e,f=zC(l);return m(FC,fn({dot:i,tag:r,class:[o.value,f?"":`${o.value}-${l}`],style:{color:c,fontSize:ft(s)},content:a},e.badgeProps),{default:()=>{var u;return[(u=t.default)==null?void 0:u.call(t),f&&m("img",{class:ef("image"),src:l},null)]}})}}});const Kt=st(qC),[JC,Dr]=Je("loading"),QC=Array(12).fill(null).map((e,t)=>m("i",{class:Dr("line",String(t+1))},null)),ZC=m("svg",{class:Dr("circular"),viewBox:"25 25 50 50"},[m("circle",{cx:"50",cy:"50",r:"20",fill:"none"},null)]),eT={size:Fe,type:je("circular"),color:String,vertical:Boolean,textSize:Fe,textColor:String};var tT=ue({name:JC,props:eT,setup(e,{slots:t}){const n=he(()=>et({color:e.color},aC(e.size))),o=()=>{const i=e.type==="spinner"?QC:ZC;return m("span",{class:Dr("spinner",e.type),style:n.value},[t.icon?t.icon():i])},r=()=>{var i;if(t.default)return m("span",{class:Dr("text"),style:{fontSize:ft(e.textSize),color:(i=e.textColor)!=null?i:e.color}},[t.default()])};return()=>{const{type:i,vertical:l}=e;return m("div",{class:Dr([i,{vertical:l}]),"aria-live":"polite","aria-busy":!0},[o(),r()])}}});const xl=st(tT),[nT,Po]=Je("button"),oT=et({},Fs,{tag:je("button"),text:String,icon:String,type:je("default"),size:je("normal"),color:String,block:Boolean,plain:Boolean,round:Boolean,square:Boolean,loading:Boolean,hairline:Boolean,disabled:Boolean,iconPrefix:String,nativeType:je("button"),loadingSize:Fe,loadingText:String,loadingType:String,iconPosition:je("left")});var rT=ue({name:nT,props:oT,emits:["click"],setup(e,{emit:t,slots:n}){const o=Bs(),r=()=>n.loading?n.loading():m(xl,{size:e.loadingSize,type:e.loadingType,class:Po("loading")},null),i=()=>{if(e.loading)return r();if(n.icon)return m("div",{class:Po("icon")},[n.icon()]);if(e.icon)return m(Kt,{name:e.icon,class:Po("icon"),classPrefix:e.iconPrefix},null)},l=()=>{let c;if(e.loading?c=e.loadingText:c=n.default?n.default():e.text,c)return m("span",{class:Po("text")},[c])},s=()=>{const{color:c,plain:f}=e;if(c){const u={color:f?c:"white"};return f||(u.background=c),c.includes("gradient")?u.border=0:u.borderColor=c,u}},a=c=>{e.loading?eo(c):e.disabled||(t("click",c),o())};return()=>{const{tag:c,type:f,size:u,block:d,round:p,plain:h,square:g,loading:O,disabled:A,hairline:V,nativeType:v,iconPosition:b}=e,w=[Po([f,u,{plain:h,block:d,round:p,square:g,loading:O,disabled:A,hairline:V}]),{[kC]:V}];return m(c,{type:v,class:w,style:s(),disabled:A,onClick:a},{default:()=>[m("div",{class:Po("content")},[b==="left"&&i(),l(),b==="right"&&i()])]})}}});const ot=st(rT),[iT,lT]=Je("action-bar-button"),aT=et({},Fs,{type:String,text:String,icon:String,color:String,loading:Boolean,disabled:Boolean});var sT=ue({name:iT,props:aT,setup(e,{slots:t}){const n=Bs(),{parent:o,index:r}=ri(Ah),i=he(()=>{if(o){const s=o.children[r.value-1];return!(s&&"isButton"in s)}}),l=he(()=>{if(o){const s=o.children[r.value+1];return!(s&&"isButton"in s)}});return dr({isButton:!0}),()=>{const{type:s,icon:a,text:c,color:f,loading:u,disabled:d}=e;return m(ot,{class:lT([s,{last:l.value,first:i.value}]),size:"large",type:s,icon:a,color:f,loading:u,disabled:d,onClick:n},{default:()=>[t.default?t.default():c]})}}});const tf=st(sT),Us={show:Boolean,zIndex:Fe,overlay:ht,duration:Fe,teleport:[String,Object],lockScroll:ht,lazyRender:ht,beforeClose:Function,overlayStyle:Object,overlayClass:Dt,transitionAppear:Boolean,closeOnClickOverlay:ht},cT=Object.keys(Us);function uT(e,t){return e>t?"horizontal":t>e?"vertical":""}function Dh(){const e=fe(0),t=fe(0),n=fe(0),o=fe(0),r=fe(0),i=fe(0),l=fe(""),s=fe(!0),a=()=>l.value==="vertical",c=()=>l.value==="horizontal",f=()=>{n.value=0,o.value=0,r.value=0,i.value=0,l.value="",s.value=!0};return{move:p=>{const h=p.touches[0];n.value=(h.clientX<0?0:h.clientX)-e.value,o.value=h.clientY-t.value,r.value=Math.abs(n.value),i.value=Math.abs(o.value);const g=10;(!l.value||r.value<g&&i.value<g)&&(l.value=uT(r.value,i.value)),s.value&&(r.value>Ju||i.value>Ju)&&(s.value=!1)},start:p=>{f(),e.value=p.touches[0].clientX,t.value=p.touches[0].clientY},reset:f,startX:e,startY:t,deltaX:n,deltaY:o,offsetX:r,offsetY:i,direction:l,isVertical:a,isHorizontal:c,isTap:s}}let _r=0;const nf="van-overflow-hidden";function fT(e,t){const n=Dh(),o="01",r="10",i=f=>{n.move(f);const u=n.deltaY.value>0?r:o,d=oC(f.target,e.value),{scrollHeight:p,offsetHeight:h,scrollTop:g}=d;let O="11";g===0?O=h>=p?"00":"01":g+h>=p&&(O="10"),O!=="11"&&n.isVertical()&&!(parseInt(O,2)&parseInt(u,2))&&eo(f,!0)},l=()=>{document.addEventListener("touchstart",n.start),document.addEventListener("touchmove",i,{passive:!1}),_r||document.body.classList.add(nf),_r++},s=()=>{_r&&(document.removeEventListener("touchstart",n.start),document.removeEventListener("touchmove",i),_r--,_r||document.body.classList.remove(nf))},a=()=>t()&&l(),c=()=>t()&&s();mh(a),cr(c),Zr(c),De(t,f=>{f?l():s()})}function Rh(e){const t=fe(!1);return De(e,n=>{n&&(t.value=n)},{immediate:!0}),n=>()=>t.value?n():null}const Ka=()=>{var e;const{scopeId:t}=((e=Et())==null?void 0:e.vnode)||{};return t?{[t]:""}:null},[dT,pT]=Je("overlay"),hT={show:Boolean,zIndex:Fe,duration:Fe,className:Dt,lockScroll:ht,lazyRender:ht,customStyle:Object,teleport:[String,Object]};var mT=ue({name:dT,props:hT,setup(e,{slots:t}){const n=fe(),o=Rh(()=>e.show||!e.lazyRender),r=l=>{e.lockScroll&&eo(l,!0)},i=o(()=>{var l;const s=et(Sh(e.zIndex),e.customStyle);return ut(e.duration)&&(s.animationDuration=`${e.duration}s`),Md(m("div",{ref:n,style:s,class:[pT(),e.className]},[(l=t.default)==null?void 0:l.call(t)]),[[yp,e.show]])});return ii("touchmove",r,{target:n}),()=>{const l=m(ti,{name:"van-fade",appear:!0},{default:i});return e.teleport?m(np,{to:e.teleport},{default:()=>[l]}):l}}});const gT=st(mT),vT=et({},Us,{round:Boolean,position:je("center"),closeIcon:je("cross"),closeable:Boolean,transition:String,iconPrefix:String,closeOnPopstate:Boolean,closeIconPosition:je("top-right"),safeAreaInsetTop:Boolean,safeAreaInsetBottom:Boolean}),[_T,of]=Je("popup");var bT=ue({name:_T,inheritAttrs:!1,props:vT,emits:["open","close","opened","closed","keydown","update:show","clickOverlay","clickCloseIcon"],setup(e,{emit:t,attrs:n,slots:o}){let r,i;const l=fe(),s=fe(),a=Rh(()=>e.show||!e.lazyRender),c=he(()=>{const S={zIndex:l.value};if(ut(e.duration)){const N=e.position==="center"?"animationDuration":"transitionDuration";S[N]=`${e.duration}s`}return S}),f=()=>{r||(r=!0,l.value=e.zIndex!==void 0?+e.zIndex:BC(),t("open"))},u=()=>{r&&wh(e.beforeClose,{done(){r=!1,t("close"),t("update:show",!1)}})},d=S=>{t("clickOverlay",S),e.closeOnClickOverlay&&u()},p=()=>{if(e.overlay)return m(gT,fn({show:e.show,class:e.overlayClass,zIndex:l.value,duration:e.duration,customStyle:e.overlayStyle,role:e.closeOnClickOverlay?"button":void 0,tabindex:e.closeOnClickOverlay?0:void 0},Ka(),{onClick:d}),{default:o["overlay-content"]})},h=S=>{t("clickCloseIcon",S),u()},g=()=>{if(e.closeable)return m(Kt,{role:"button",tabindex:0,name:e.closeIcon,class:[of("close-icon",e.closeIconPosition),lr],classPrefix:e.iconPrefix,onClick:h},null)};let O;const A=()=>{O&&clearTimeout(O),O=setTimeout(()=>{t("opened")})},V=()=>t("closed"),v=S=>t("keydown",S),b=a(()=>{var S;const{round:N,position:M,safeAreaInsetTop:x,safeAreaInsetBottom:L}=e;return Md(m("div",fn({ref:s,style:c.value,role:"dialog",tabindex:0,class:[of({round:N,[M]:M}),{"van-safe-area-top":x,"van-safe-area-bottom":L}],onKeydown:v},n,Ka()),[(S=o.default)==null?void 0:S.call(o),g()]),[[yp,e.show]])}),w=()=>{const{position:S,transition:N,transitionAppear:M}=e,x=S==="center"?"van-fade":`van-popup-slide-${S}`;return m(ti,{name:N||x,appear:M,onAfterEnter:A,onAfterLeave:V},{default:b})};return De(()=>e.show,S=>{S&&!r&&(f(),n.tabindex===0&&bt(()=>{var N;(N=s.value)==null||N.focus()})),!S&&r&&(r=!1,t("close"))}),dr({popupRef:s}),fT(s,()=>e.show&&e.lockScroll),ii("popstate",()=>{e.closeOnPopstate&&(u(),i=!1)}),Pt(()=>{e.show&&f()}),Qr(()=>{i&&(t("update:show",!0),i=!1)}),cr(()=>{e.show&&e.teleport&&(u(),i=!0)}),Qn(kh,()=>e.show),()=>e.teleport?m(np,{to:e.teleport},{default:()=>[p(),w()]}):m(Be,null,[p(),w()])}});const js=st(bT),[yT,On,rf]=Je("picker"),Nh=e=>e.find(t=>!t.disabled)||e[0];function ET(e,t){const n=e[0];if(n){if(Array.isArray(n))return"multiple";if(t.children in n)return"cascade"}return"default"}function Ri(e,t){t=rl(t,0,e.length);for(let n=t;n<e.length;n++)if(!e[n].disabled)return n;for(let n=t-1;n>=0;n--)if(!e[n].disabled)return n;return 0}const lf=(e,t,n)=>t!==void 0&&!!e.find(o=>o[n.value]===t);function Ya(e,t,n){const o=e.findIndex(i=>i[n.value]===t),r=Ri(e,o);return e[r]}function ST(e,t,n){const o=[];let r={[t.children]:e},i=0;for(;r&&r[t.children];){const l=r[t.children],s=n.value[i];if(r=ut(s)?Ya(l,s,t):void 0,!r&&l.length){const a=Nh(l)[t.value];r=Ya(l,a,t)}i++,o.push(l)}return o}function CT(e){const{transform:t}=window.getComputedStyle(e),n=t.slice(7,t.length-1).split(", ")[5];return Number(n)}function TT(e){return et({text:"text",value:"value",children:"children"},e)}const af=200,sf=300,wT=15,[Mh,na]=Je("picker-column"),Vh=Symbol(Mh);var kT=ue({name:Mh,props:{value:Fe,fields:Er(Object),options:ol(),readonly:Boolean,allowHtml:Boolean,optionHeight:Er(Number),swipeDuration:Er(Fe),visibleOptionNum:Er(Fe)},emits:["change","clickOption","scrollInto"],setup(e,{emit:t,slots:n}){let o,r,i,l,s;const a=fe(),c=fe(),f=fe(0),u=fe(0),d=Dh(),p=()=>e.options.length,h=()=>e.optionHeight*(+e.visibleOptionNum-1)/2,g=L=>{let D=Ri(e.options,L);const I=-D*e.optionHeight,j=()=>{D>p()-1&&(D=Ri(e.options,L));const Q=e.options[D][e.fields.value];Q!==e.value&&t("change",Q)};o&&I!==f.value?s=j:j(),f.value=I},O=()=>e.readonly||!e.options.length,A=L=>{o||O()||(s=null,u.value=af,g(L),t("clickOption",e.options[L]))},V=L=>rl(Math.round(-L/e.optionHeight),0,p()-1),v=he(()=>V(f.value)),b=(L,D)=>{const I=Math.abs(L/D);L=f.value+I/.003*(L<0?-1:1);const j=V(L);u.value=+e.swipeDuration,g(j)},w=()=>{o=!1,u.value=0,s&&(s(),s=null)},S=L=>{if(!O()){if(d.start(L),o){const D=CT(c.value);f.value=Math.min(0,D-h())}u.value=0,r=f.value,i=Date.now(),l=r,s=null}},N=L=>{if(O())return;d.move(L),d.isVertical()&&(o=!0,eo(L,!0));const D=rl(r+d.deltaY.value,-(p()*e.optionHeight),e.optionHeight),I=V(D);I!==v.value&&t("scrollInto",e.options[I]),f.value=D;const j=Date.now();j-i>sf&&(i=j,l=D)},M=()=>{if(O())return;const L=f.value-l,D=Date.now()-i;if(D<sf&&Math.abs(L)>wT){b(L,D);return}const j=V(f.value);u.value=af,g(j),setTimeout(()=>{o=!1},0)},x=()=>{const L={height:`${e.optionHeight}px`};return e.options.map((D,I)=>{const j=D[e.fields.text],{disabled:Q}=D,G=D[e.fields.value],U={role:"button",style:L,tabindex:Q?-1:0,class:[na("item",{disabled:Q,selected:G===e.value}),D.className],onClick:()=>A(I)},X={class:"van-ellipsis",[e.allowHtml?"innerHTML":"textContent"]:j};return m("li",U,[n.option?n.option(D,I):m("div",X,null)])})};return ri(Vh),dr({stopMomentum:w}),Cl(()=>{const L=o?Math.floor(-f.value/e.optionHeight):e.options.findIndex(j=>j[e.fields.value]===e.value),D=Ri(e.options,L),I=-D*e.optionHeight;o&&D<L&&w(),f.value=I}),ii("touchmove",N,{target:a}),()=>m("div",{ref:a,class:na(),onTouchstartPassive:S,onTouchend:M,onTouchcancel:M},[m("ul",{ref:c,style:{transform:`translate3d(0, ${f.value+h()}px, 0)`,transitionDuration:`${u.value}ms`,transitionProperty:u.value?"all":"none"},class:na("wrapper"),onTransitionend:w},[x()])])}});const[OT]=Je("picker-toolbar"),Ll={title:String,cancelButtonText:String,confirmButtonText:String},PT=["cancel","confirm","title","toolbar"],AT=Object.keys(Ll);var IT=ue({name:OT,props:Ll,emits:["confirm","cancel"],setup(e,{emit:t,slots:n}){const o=()=>{if(n.title)return n.title();if(e.title)return m("div",{class:[On("title"),"van-ellipsis"]},[e.title])},r=()=>t("cancel"),i=()=>t("confirm"),l=()=>{var a;const c=(a=e.cancelButtonText)!=null?a:rf("cancel");if(!(!n.cancel&&!c))return m("button",{type:"button",class:[On("cancel"),lr],onClick:r},[n.cancel?n.cancel():c])},s=()=>{var a;const c=(a=e.confirmButtonText)!=null?a:rf("confirm");if(!(!n.confirm&&!c))return m("button",{type:"button",class:[On("confirm"),lr],onClick:i},[n.confirm?n.confirm():c])};return()=>m("div",{class:On("toolbar")},[n.toolbar?n.toolbar():[l(),o(),s()]])}});let xT=0;function LT(){const e=Et(),{name:t="unknown"}=(e==null?void 0:e.type)||{};return`${t}-${++xT}`}const[DT,eO]=Je("picker-group"),RT=Symbol(DT);et({tabs:ol(),activeTab:Lr(0),nextStepText:String,showToolbar:ht},Ll);const NT=et({loading:Boolean,readonly:Boolean,allowHtml:Boolean,optionHeight:Lr(44),showToolbar:ht,swipeDuration:Lr(1e3),visibleOptionNum:Lr(6)},Ll),MT=et({},NT,{columns:ol(),modelValue:ol(),toolbarPosition:je("top"),columnsFieldNames:Object});var VT=ue({name:yT,props:MT,emits:["confirm","cancel","change","scrollInto","clickOption","update:modelValue"],setup(e,{emit:t,slots:n}){const o=fe(),r=fe(e.modelValue.slice(0)),{parent:i}=ri(RT),{children:l,linkChildren:s}=$s(Vh);s();const a=he(()=>TT(e.columnsFieldNames)),c=he(()=>dC(e.optionHeight)),f=he(()=>ET(e.columns,a.value)),u=he(()=>{const{columns:D}=e;switch(f.value){case"multiple":return D;case"cascade":return ST(D,a.value,r);default:return[D]}}),d=he(()=>u.value.some(D=>D.length)),p=he(()=>u.value.map((D,I)=>Ya(D,r.value[I],a.value))),h=he(()=>u.value.map((D,I)=>D.findIndex(j=>j[a.value.value]===r.value[I]))),g=(D,I)=>{if(r.value[D]!==I){const j=r.value.slice(0);j[D]=I,r.value=j}},O=()=>({selectedValues:r.value.slice(0),selectedOptions:p.value,selectedIndexes:h.value}),A=(D,I)=>{g(I,D),f.value==="cascade"&&r.value.forEach((j,Q)=>{const G=u.value[Q];lf(G,j,a.value)||g(Q,G.length?G[0][a.value.value]:void 0)}),bt(()=>{t("change",et({columnIndex:I},O()))})},V=(D,I)=>{const j={columnIndex:I,currentOption:D};t("clickOption",et(O(),j)),t("scrollInto",j)},v=()=>{l.forEach(I=>I.stopMomentum());const D=O();return bt(()=>{t("confirm",D)}),D},b=()=>t("cancel",O()),w=()=>u.value.map((D,I)=>m(kT,{value:r.value[I],fields:a.value,options:D,readonly:e.readonly,allowHtml:e.allowHtml,optionHeight:c.value,swipeDuration:e.swipeDuration,visibleOptionNum:e.visibleOptionNum,onChange:j=>A(j,I),onClickOption:j=>V(j,I),onScrollInto:j=>{t("scrollInto",{currentOption:j,columnIndex:I})}},{option:n.option})),S=D=>{if(d.value){const I={height:`${c.value}px`},j={backgroundSize:`100% ${(D-c.value)/2}px`};return[m("div",{class:On("mask"),style:j},null),m("div",{class:[PC,On("frame")],style:I},null)]}},N=()=>{const D=c.value*+e.visibleOptionNum,I={height:`${D}px`};return m("div",{ref:o,class:On("columns"),style:I},[w(),S(D)])},M=()=>{if(e.showToolbar&&!i)return m(IT,fn(zr(e,AT),{onConfirm:v,onCancel:b}),zr(n,PT))};De(u,D=>{D.forEach((I,j)=>{I.length&&!lf(I,r.value[j],a.value)&&g(j,Nh(I)[a.value.value])})},{immediate:!0});let x;return De(()=>e.modelValue,D=>{!Zl(D,r.value)&&!Zl(D,x)&&(r.value=D.slice(0),x=D.slice(0))},{deep:!0}),De(r,D=>{Zl(D,e.modelValue)||(x=D.slice(0),t("update:modelValue",x))},{immediate:!0}),ii("touchmove",eo,{target:o}),dr({confirm:v,getSelectedOptions:()=>p.value}),()=>{var D,I;return m("div",{class:On()},[e.toolbarPosition==="top"?M():null,e.loading?m(xl,{class:On("loading")},null):null,(D=n["columns-top"])==null?void 0:D.call(n),N(),(I=n["columns-bottom"])==null?void 0:I.call(n),e.toolbarPosition==="bottom"?M():null])}}});const $T=st(VT),[FT,Ao]=Je("cell"),$h={tag:je("div"),icon:String,size:String,title:Fe,value:Fe,label:Fe,center:Boolean,isLink:Boolean,border:ht,iconPrefix:String,valueClass:Dt,labelClass:Dt,titleClass:Dt,titleStyle:null,arrowDirection:String,required:{type:[Boolean,String],default:null},clickable:{type:Boolean,default:null}},BT=et({},$h,Fs);var UT=ue({name:FT,props:BT,setup(e,{slots:t}){const n=Bs(),o=()=>{if(t.label||ut(e.label))return m("div",{class:[Ao("label"),e.labelClass]},[t.label?t.label():e.label])},r=()=>{var a;if(t.title||ut(e.title)){const c=(a=t.title)==null?void 0:a.call(t);return Array.isArray(c)&&c.length===0?void 0:m("div",{class:[Ao("title"),e.titleClass],style:e.titleStyle},[c||m("span",null,[e.title]),o()])}},i=()=>{const a=t.value||t.default;if(a||ut(e.value))return m("div",{class:[Ao("value"),e.valueClass]},[a?a():m("span",null,[e.value])])},l=()=>{if(t.icon)return t.icon();if(e.icon)return m(Kt,{name:e.icon,class:Ao("left-icon"),classPrefix:e.iconPrefix},null)},s=()=>{if(t["right-icon"])return t["right-icon"]();if(e.isLink){const a=e.arrowDirection&&e.arrowDirection!=="right"?`arrow-${e.arrowDirection}`:"arrow";return m(Kt,{name:a,class:Ao("right-icon")},null)}};return()=>{var a;const{tag:c,size:f,center:u,border:d,isLink:p,required:h}=e,g=(a=e.clickable)!=null?a:p,O={center:u,required:!!h,clickable:g,borderless:!d};return f&&(O[f]=!!f),m(c,{class:Ao(O),role:g?"button":void 0,tabindex:g?0:void 0,onClick:n},{default:()=>{var A;return[l(),r(),i(),s(),(A=t.extra)==null?void 0:A.call(t)]}})}}});const tt=st(UT);function Fh(e){return Array.isArray(e)?!e.length:e===0?!1:!e}function jT(e,t){if(Fh(e)){if(t.required)return!1;if(t.validateEmpty===!1)return!0}return!(t.pattern&&!t.pattern.test(String(e)))}function HT(e,t){return new Promise(n=>{const o=t.validator(e,t);if(ph(o)){o.then(n);return}n(o)})}function cf(e,t){const{message:n}=t;return Yr(n)?n(e,t):n||""}function WT({target:e}){e.composing=!0}function uf({target:e}){e.composing&&(e.composing=!1,e.dispatchEvent(new Event("input")))}function GT(e,t){const n=_h();e.style.height="auto";let o=e.scrollHeight;if(oi(t)){const{maxHeight:r,minHeight:i}=t;r!==void 0&&(o=Math.min(o,r)),i!==void 0&&(o=Math.max(o,i))}o&&(e.style.height=`${o}px`,bh(n))}function KT(e){return e==="number"?{type:"text",inputmode:"decimal"}:e==="digit"?{type:"tel",inputmode:"numeric"}:{type:e}}function Tn(e){return[...e].length}function oa(e,t){return[...e].slice(0,t).join("")}const[YT,zt]=Je("field"),zT={id:String,name:String,leftIcon:String,rightIcon:String,autofocus:Boolean,clearable:Boolean,maxlength:Fe,max:Number,min:Number,formatter:Function,clearIcon:je("clear"),modelValue:Lr(""),inputAlign:String,placeholder:String,autocomplete:String,autocapitalize:String,autocorrect:String,errorMessage:String,enterkeyhint:String,clearTrigger:je("focus"),formatTrigger:je("onChange"),spellcheck:{type:Boolean,default:null},error:{type:Boolean,default:null},disabled:{type:Boolean,default:null},readonly:{type:Boolean,default:null}},XT=et({},$h,zT,{rows:Fe,type:je("text"),rules:Array,autosize:[Boolean,Object],labelWidth:Fe,labelClass:Dt,labelAlign:String,showWordLimit:Boolean,errorMessageAlign:String,colon:{type:Boolean,default:null}});var qT=ue({name:YT,props:XT,emits:["blur","focus","clear","keypress","clickInput","endValidate","startValidate","clickLeftIcon","clickRightIcon","update:modelValue"],setup(e,{emit:t,slots:n}){const o=LT(),r=un({status:"unvalidated",focused:!1,validateMessage:""}),i=fe(),l=fe(),s=fe(),{parent:a}=ri(AC),c=()=>{var P;return String((P=e.modelValue)!=null?P:"")},f=P=>{if(ut(e[P]))return e[P];if(a&&ut(a.props[P]))return a.props[P]},u=he(()=>{const P=f("readonly");if(e.clearable&&!P){const q=c()!=="",z=e.clearTrigger==="always"||e.clearTrigger==="focus"&&r.focused;return q&&z}return!1}),d=he(()=>s.value&&n.input?s.value():e.modelValue),p=he(()=>{var P;const q=f("required");return q==="auto"?(P=e.rules)==null?void 0:P.some(z=>z.required):q}),h=P=>P.reduce((q,z)=>q.then(()=>{if(r.status==="failed")return;let{value:ee}=d;if(z.formatter&&(ee=z.formatter(ee,z)),!jT(ee,z)){r.status="failed",r.validateMessage=cf(ee,z);return}if(z.validator)return Fh(ee)&&z.validateEmpty===!1?void 0:HT(ee,z).then(se=>{se&&typeof se=="string"?(r.status="failed",r.validateMessage=se):se===!1&&(r.status="failed",r.validateMessage=cf(ee,z))})}),Promise.resolve()),g=()=>{r.status="unvalidated",r.validateMessage=""},O=()=>t("endValidate",{status:r.status,message:r.validateMessage}),A=(P=e.rules)=>new Promise(q=>{g(),P?(t("startValidate"),h(P).then(()=>{r.status==="failed"?(q({name:e.name,message:r.validateMessage}),O()):(r.status="passed",q(),O())})):q()}),V=P=>{if(a&&e.rules){const{validateTrigger:q}=a.props,z=Wu(q).includes(P),ee=e.rules.filter(se=>se.trigger?Wu(se.trigger).includes(P):z);ee.length&&A(ee)}},v=P=>{var q;const{maxlength:z}=e;if(ut(z)&&Tn(P)>+z){const ee=c();if(ee&&Tn(ee)===+z)return ee;const se=(q=i.value)==null?void 0:q.selectionEnd;if(r.focused&&se){const ke=[...P],T=ke.length-+z;return ke.splice(se-T,T),ke.join("")}return oa(P,+z)}return P},b=(P,q="onChange")=>{var z,ee;const se=P;P=v(P);const ke=Tn(se)-Tn(P);if(e.type==="number"||e.type==="digit"){const k=e.type==="number";P=mC(P,k,k),q==="onBlur"&&P!==""&&(e.min!==void 0||e.max!==void 0)&&(P=rl(+P,(z=e.min)!=null?z:-1/0,(ee=e.max)!=null?ee:1/0).toString())}let T=0;if(e.formatter&&q===e.formatTrigger){const{formatter:k,maxlength:B}=e;if(P=k(P),ut(B)&&Tn(P)>+B&&(P=oa(P,+B)),i.value&&r.focused){const{selectionEnd:J}=i.value,K=oa(se,J);T=Tn(k(K))-Tn(K)}}if(i.value&&i.value.value!==P)if(r.focused){let{selectionStart:k,selectionEnd:B}=i.value;if(i.value.value=P,ut(k)&&ut(B)){const J=Tn(P);ke?(k-=ke,B-=ke):T&&(k+=T,B+=T),i.value.setSelectionRange(Math.min(k,J),Math.min(B,J))}}else i.value.value=P;P!==e.modelValue&&t("update:modelValue",P)},w=P=>{P.target.composing||b(P.target.value)},S=()=>{var P;return(P=i.value)==null?void 0:P.blur()},N=()=>{var P;return(P=i.value)==null?void 0:P.focus()},M=()=>{const P=i.value;e.type==="textarea"&&e.autosize&&P&&GT(P,e.autosize)},x=P=>{r.focused=!0,t("focus",P),bt(M),f("readonly")&&S()},L=P=>{r.focused=!1,b(c(),"onBlur"),t("blur",P),!f("readonly")&&(V("onBlur"),bt(M),iC())},D=P=>t("clickInput",P),I=P=>t("clickLeftIcon",P),j=P=>t("clickRightIcon",P),Q=P=>{eo(P),t("update:modelValue",""),t("clear",P)},G=he(()=>{if(typeof e.error=="boolean")return e.error;if(a&&a.props.showError&&r.status==="failed")return!0}),U=he(()=>{const P=f("labelWidth"),q=f("labelAlign");if(P&&q!=="top")return{width:ft(P)}}),X=P=>{P.keyCode===13&&(!(a&&a.props.submitOnEnter)&&e.type!=="textarea"&&eo(P),e.type==="search"&&S()),t("keypress",P)},W=()=>e.id||`${o}-input`,ie=()=>r.status,ce=()=>{const P=zt("control",[f("inputAlign"),{error:G.value,custom:!!n.input,"min-height":e.type==="textarea"&&!e.autosize}]);if(n.input)return m("div",{class:P,onClick:D},[n.input()]);const q={id:W(),ref:i,name:e.name,rows:e.rows!==void 0?+e.rows:void 0,class:P,disabled:f("disabled"),readonly:f("readonly"),autofocus:e.autofocus,placeholder:e.placeholder,autocomplete:e.autocomplete,autocapitalize:e.autocapitalize,autocorrect:e.autocorrect,enterkeyhint:e.enterkeyhint,spellcheck:e.spellcheck,"aria-labelledby":e.label?`${o}-label`:void 0,"data-allow-mismatch":"attribute",onBlur:L,onFocus:x,onInput:w,onClick:D,onChange:uf,onKeypress:X,onCompositionend:uf,onCompositionstart:WT};return e.type==="textarea"?m("textarea",q,null):m("input",fn(KT(e.type),q),null)},ge=()=>{const P=n["left-icon"];if(e.leftIcon||P)return m("div",{class:zt("left-icon"),onClick:I},[P?P():m(Kt,{name:e.leftIcon,classPrefix:e.iconPrefix},null)])},Ce=()=>{const P=n["right-icon"];if(e.rightIcon||P)return m("div",{class:zt("right-icon"),onClick:j},[P?P():m(Kt,{name:e.rightIcon,classPrefix:e.iconPrefix},null)])},Re=()=>{if(e.showWordLimit&&e.maxlength){const P=Tn(c());return m("div",{class:zt("word-limit")},[m("span",{class:zt("word-num")},[P]),Oe("/"),e.maxlength])}},Me=()=>{if(a&&a.props.showErrorMessage===!1)return;const P=e.errorMessage||r.validateMessage;if(P){const q=n["error-message"],z=f("errorMessageAlign");return m("div",{class:zt("error-message",z)},[q?q({message:P}):P])}},ze=()=>{const P=f("labelWidth"),q=f("labelAlign"),z=f("colon")?":":"";if(n.label)return[n.label(),z];if(e.label)return m("label",{id:`${o}-label`,for:n.input?void 0:W(),"data-allow-mismatch":"attribute",onClick:ee=>{eo(ee),N()},style:q==="top"&&P?{width:ft(P)}:void 0},[e.label+z])},We=()=>[m("div",{class:zt("body")},[ce(),u.value&&m(Kt,{ref:l,name:e.clearIcon,class:zt("clear")},null),Ce(),n.button&&m("div",{class:zt("button")},[n.button()])]),Re(),Me()];return dr({blur:S,focus:N,validate:A,formValue:d,resetValidation:g,getValidationStatus:ie}),Qn(gh,{customValue:s,resetValidation:g,validateWithTrigger:V}),De(()=>e.modelValue,()=>{b(c()),g(),V("onChange"),bt(M)}),Pt(()=>{b(c(),e.formatTrigger),bt(M)}),ii("touchstart",Q,{target:he(()=>{var P;return(P=l.value)==null?void 0:P.$el})}),()=>{const P=f("disabled"),q=f("labelAlign"),z=ge(),ee=()=>{const se=ze();return q==="top"?[z,se].filter(Boolean):se||[]};return m(tt,{size:e.size,class:zt({error:G.value,disabled:P,[`label-${q}`]:q}),center:e.center,border:e.border,isLink:e.isLink,clickable:e.clickable,titleStyle:U.value,valueClass:zt("value"),titleClass:[zt("label",[q,{required:p.value}]),e.labelClass],arrowDirection:e.arrowDirection},{icon:z&&q!=="top"?()=>z:null,title:ee,value:We,extra:n.extra})}}});const on=st(qT);let br=0;function JT(e){e?(br||document.body.classList.add("van-toast--unclickable"),br++):br&&(br--,br||document.body.classList.remove("van-toast--unclickable"))}const[QT,Io]=Je("toast"),ZT=["show","overlay","teleport","transition","overlayClass","overlayStyle","closeOnClickOverlay","zIndex"],ew={icon:String,show:Boolean,type:je("text"),overlay:Boolean,message:Fe,iconSize:Fe,duration:zS(2e3),position:je("middle"),teleport:[String,Object],wordBreak:String,className:Dt,iconPrefix:String,transition:je("van-fade"),loadingType:String,forbidClick:Boolean,overlayClass:Dt,overlayStyle:Object,closeOnClick:Boolean,closeOnClickOverlay:Boolean,zIndex:Fe};var Bh=ue({name:QT,props:ew,emits:["update:show"],setup(e,{emit:t,slots:n}){let o,r=!1;const i=()=>{const u=e.show&&e.forbidClick;r!==u&&(r=u,JT(r))},l=u=>t("update:show",u),s=()=>{e.closeOnClick&&l(!1)},a=()=>clearTimeout(o),c=()=>{const{icon:u,type:d,iconSize:p,iconPrefix:h,loadingType:g}=e;if(u||d==="success"||d==="fail")return m(Kt,{name:u||d,size:p,class:Io("icon"),classPrefix:h},null);if(d==="loading")return m(xl,{class:Io("loading"),size:p,type:g},null)},f=()=>{const{type:u,message:d}=e;if(n.message)return m("div",{class:Io("text")},[n.message()]);if(ut(d)&&d!=="")return u==="html"?m("div",{key:0,class:Io("text"),innerHTML:String(d)},null):m("div",{class:Io("text")},[d])};return De(()=>[e.show,e.forbidClick],i),De(()=>[e.show,e.type,e.message,e.duration],()=>{a(),e.show&&e.duration>0&&(o=setTimeout(()=>{l(!1)},e.duration))}),Pt(i),wo(i),()=>m(js,fn({class:[Io([e.position,e.wordBreak==="normal"?"break-normal":e.wordBreak,{[e.type]:!e.icon}]),e.className],lockScroll:!1,onClick:s,onClosed:a,"onUpdate:show":l},zr(e,ZT)),{default:()=>[c(),f()]})}});function tw(){const e=un({show:!1}),t=r=>{e.show=r},n=r=>{et(e,r,{transitionAppear:!0}),t(!0)},o=()=>t(!1);return dr({open:n,close:o,toggle:t}),{open:n,close:o,state:e,toggle:t}}function nw(e){const t=Sp(e),n=document.createElement("div");return document.body.appendChild(n),{instance:t.mount(n),unmount(){t.unmount(),document.body.removeChild(n)}}}const ow={icon:"",type:"text",message:"",className:"",overlay:!1,onClose:void 0,onOpened:void 0,duration:2e3,teleport:"body",iconSize:void 0,iconPrefix:void 0,position:"middle",transition:"van-fade",forbidClick:!1,loadingType:void 0,overlayClass:"",overlayStyle:void 0,closeOnClick:!1,closeOnClickOverlay:!1};let $o=[],rw=!1,ff=et({},ow);const iw=new Map;function Uh(e){return oi(e)?e:{message:e}}function lw(){const{instance:e,unmount:t}=nw({setup(){const n=fe(""),{open:o,state:r,close:i,toggle:l}=tw(),s=()=>{},a=()=>m(Bh,fn(r,{onClosed:s,"onUpdate:show":l}),null);return De(n,c=>{r.message=c}),Et().render=a,{open:o,close:i,message:n}}});return e}function aw(){if(!$o.length||rw){const e=lw();$o.push(e)}return $o[$o.length-1]}function jh(e={}){if(!Il)return{};const t=aw(),n=Uh(e);return t.open(et({},ff,iw.get(n.type||ff.type),n)),t}const Hs=e=>t=>jh(et({type:e},Uh(t))),sw=Hs("loading"),cw=Hs("success"),Hh=Hs("fail"),uw=e=>{$o.length&&$o[0].close()};st(Bh);const[fw,ra]=Je("switch"),dw={size:Fe,loading:Boolean,disabled:Boolean,modelValue:Dt,activeColor:String,inactiveColor:String,activeValue:{type:Dt,default:!0},inactiveValue:{type:Dt,default:!1}};var pw=ue({name:fw,props:dw,emits:["change","update:modelValue"],setup(e,{emit:t,slots:n}){const o=()=>e.modelValue===e.activeValue,r=()=>{if(!e.disabled&&!e.loading){const l=o()?e.inactiveValue:e.activeValue;t("update:modelValue",l),t("change",l)}},i=()=>{if(e.loading){const l=o()?e.activeColor:e.inactiveColor;return m(xl,{class:ra("loading"),color:l},null)}if(n.node)return n.node()};return vh(()=>e.modelValue),()=>{var l;const{size:s,loading:a,disabled:c,activeColor:f,inactiveColor:u}=e,d=o(),p={fontSize:ft(s),backgroundColor:d?f:u};return m("div",{role:"switch",class:ra({on:d,loading:a,disabled:c}),style:p,tabindex:c?void 0:0,"aria-checked":d,onClick:r},[m("div",{class:ra("node")},[i()]),(l=n.background)==null?void 0:l.call(n)])}}});const uo=st(pw),[Wh,hw]=Je("radio-group"),mw={shape:String,disabled:Boolean,iconSize:Fe,direction:String,modelValue:Dt,checkedColor:String},Gh=Symbol(Wh);var gw=ue({name:Wh,props:mw,emits:["change","update:modelValue"],setup(e,{emit:t,slots:n}){const{linkChildren:o}=$s(Gh),r=i=>t("update:modelValue",i);return De(()=>e.modelValue,i=>t("change",i)),o({props:e,updateValue:r}),vh(()=>e.modelValue),()=>{var i;return m("div",{class:hw([e.direction]),role:"radiogroup"},[(i=n.default)==null?void 0:i.call(n)])}}});const vw=st(gw),[_w,df]=Je("tag"),bw={size:String,mark:Boolean,show:ht,type:je("default"),color:String,plain:Boolean,round:Boolean,textColor:String,closeable:Boolean};var yw=ue({name:_w,props:bw,emits:["close"],setup(e,{slots:t,emit:n}){const o=l=>{l.stopPropagation(),n("close",l)},r=()=>e.plain?{color:e.textColor||e.color,borderColor:e.color}:{color:e.textColor,background:e.color},i=()=>{var l;const{type:s,mark:a,plain:c,round:f,size:u,closeable:d}=e,p={mark:a,plain:c,round:f};u&&(p[u]=u);const h=d&&m(Kt,{name:"cross",class:[df("close"),lr],onClick:o},null);return m("span",{style:r(),class:df([p,s])},[(l=t.default)==null?void 0:l.call(t),h])};return()=>m(ti,{name:e.closeable?"van-fade":void 0},{default:()=>[e.show?i():null]})}});const Kh=st(yw),Yh={name:Dt,disabled:Boolean,iconSize:Fe,modelValue:Dt,checkedColor:String,labelPosition:String,labelDisabled:Boolean};var Ew=ue({props:et({},Yh,{bem:Er(Function),role:String,shape:String,parent:Object,checked:Boolean,bindGroup:ht,indeterminate:{type:Boolean,default:null}}),emits:["click","toggle"],setup(e,{emit:t,slots:n}){const o=fe(),r=d=>{if(e.parent&&e.bindGroup)return e.parent.props[d]},i=he(()=>{if(e.parent&&e.bindGroup){const d=r("disabled")||e.disabled;if(e.role==="checkbox"){const p=r("modelValue").length,h=r("max"),g=h&&p>=+h;return d||g&&!e.checked}return d}return e.disabled}),l=he(()=>r("direction")),s=he(()=>{const d=e.checkedColor||r("checkedColor");if(d&&e.checked&&!i.value)return{borderColor:d,backgroundColor:d}}),a=he(()=>e.shape||r("shape")||"round"),c=d=>{const{target:p}=d,h=o.value,g=h===p||(h==null?void 0:h.contains(p));!i.value&&(g||!e.labelDisabled)&&t("toggle"),t("click",d)},f=()=>{var d,p;const{bem:h,checked:g,indeterminate:O}=e,A=e.iconSize||r("iconSize");return m("div",{ref:o,class:h("icon",[a.value,{disabled:i.value,checked:g,indeterminate:O}]),style:a.value!=="dot"?{fontSize:ft(A)}:{width:ft(A),height:ft(A),borderColor:(d=s.value)==null?void 0:d.borderColor}},[n.icon?n.icon({checked:g,disabled:i.value}):a.value!=="dot"?m(Kt,{name:O?"minus":"success",style:s.value},null):m("div",{class:h("icon--dot__icon"),style:{backgroundColor:(p=s.value)==null?void 0:p.backgroundColor}},null)])},u=()=>{const{checked:d}=e;if(n.default)return m("span",{class:e.bem("label",[e.labelPosition,{disabled:i.value}])},[n.default({checked:d,disabled:i.value})])};return()=>{const d=e.labelPosition==="left"?[u(),f()]:[f(),u()];return m("div",{role:e.role,class:e.bem([{disabled:i.value,"label-disabled":e.labelDisabled},l.value]),tabindex:i.value?void 0:0,"aria-checked":e.checked,onClick:c},[d])}}});const Sw=et({},Yh,{shape:String}),[Cw,Tw]=Je("radio");var ww=ue({name:Cw,props:Sw,emits:["update:modelValue"],setup(e,{emit:t,slots:n}){const{parent:o}=ri(Gh),r=()=>(o?o.props.modelValue:e.modelValue)===e.name,i=()=>{o?o.updateValue(e.name):t("update:modelValue",e.name)};return()=>m(Ew,fn({bem:Tw,role:"radio",parent:o,checked:r(),onToggle:i},e),zr(n,["default","icon"]))}});const kw=st(ww),[Ow,pf]=Je("cell-group"),Pw={title:String,inset:Boolean,border:ht};var Aw=ue({name:Ow,inheritAttrs:!1,props:Pw,setup(e,{slots:t,attrs:n}){const o=()=>{var i;return m("div",fn({class:[pf({inset:e.inset}),{[OC]:e.border&&!e.inset}]},n,Ka()),[(i=t.default)==null?void 0:i.call(t)])},r=()=>m("div",{class:pf("title",{inset:e.inset})},[t.title?t.title():e.title]);return()=>e.title||t.title?m(Be,null,[r(),o()]):o()}});const rt=st(Aw),zh=st(KC),[Iw,ln,wi]=Je("dialog"),xw=et({},Us,{title:String,theme:String,width:Fe,message:[String,Function],callback:Function,allowHtml:Boolean,className:Dt,transition:je("van-dialog-bounce"),messageAlign:String,closeOnPopstate:ht,showCancelButton:Boolean,cancelButtonText:String,cancelButtonColor:String,cancelButtonDisabled:Boolean,confirmButtonText:String,confirmButtonColor:String,confirmButtonDisabled:Boolean,showConfirmButton:ht,closeOnClickOverlay:Boolean}),Lw=[...cT,"transition","closeOnPopstate"];var Dw=ue({name:Iw,props:xw,emits:["confirm","cancel","keydown","update:show"],setup(e,{emit:t,slots:n}){const o=fe(),r=un({confirm:!1,cancel:!1}),i=A=>t("update:show",A),l=A=>{var V;i(!1),(V=e.callback)==null||V.call(e,A)},s=A=>()=>{e.show&&(t(A),e.beforeClose?(r[A]=!0,wh(e.beforeClose,{args:[A],done(){l(A),r[A]=!1},canceled(){r[A]=!1}})):l(A))},a=s("cancel"),c=s("confirm"),f=Gb(A=>{var V,v;if(A.target!==((v=(V=o.value)==null?void 0:V.popupRef)==null?void 0:v.value))return;({Enter:e.showConfirmButton?c:Wa,Escape:e.showCancelButton?a:Wa})[A.key](),t("keydown",A)},["enter","esc"]),u=()=>{const A=n.title?n.title():e.title;if(A)return m("div",{class:ln("header",{isolated:!e.message&&!n.default})},[A])},d=A=>{const{message:V,allowHtml:v,messageAlign:b}=e,w=ln("message",{"has-title":A,[b]:b}),S=Yr(V)?V():V;return v&&typeof S=="string"?m("div",{class:w,innerHTML:S},null):m("div",{class:w},[S])},p=()=>{if(n.default)return m("div",{class:ln("content")},[n.default()]);const{title:A,message:V,allowHtml:v}=e;if(V){const b=!!(A||n.title);return m("div",{key:v?1:0,class:ln("content",{isolated:!b})},[d(b)])}},h=()=>m("div",{class:[CC,ln("footer")]},[e.showCancelButton&&m(ot,{size:"large",text:e.cancelButtonText||wi("cancel"),class:ln("cancel"),style:{color:e.cancelButtonColor},loading:r.cancel,disabled:e.cancelButtonDisabled,onClick:a},null),e.showConfirmButton&&m(ot,{size:"large",text:e.confirmButtonText||wi("confirm"),class:[ln("confirm"),{[TC]:e.showCancelButton}],style:{color:e.confirmButtonColor},loading:r.confirm,disabled:e.confirmButtonDisabled,onClick:c},null)]),g=()=>m(RC,{class:ln("footer")},{default:()=>[e.showCancelButton&&m(tf,{type:"warning",text:e.cancelButtonText||wi("cancel"),class:ln("cancel"),color:e.cancelButtonColor,loading:r.cancel,disabled:e.cancelButtonDisabled,onClick:a},null),e.showConfirmButton&&m(tf,{type:"danger",text:e.confirmButtonText||wi("confirm"),class:ln("confirm"),color:e.confirmButtonColor,loading:r.confirm,disabled:e.confirmButtonDisabled,onClick:c},null)]}),O=()=>n.footer?n.footer():e.theme==="round-button"?g():h();return()=>{const{width:A,title:V,theme:v,message:b,className:w}=e;return m(js,fn({ref:o,role:"dialog",class:[ln([v]),w],style:{width:ft(A)},tabindex:0,"aria-labelledby":V||b,onKeydown:f,"onUpdate:show":i},zr(e,Lw)),{default:()=>[u(),p(),O()]})}}});const Rw=st(Dw),[Nw,wn]=Je("nav-bar"),Mw={title:String,fixed:Boolean,zIndex:Fe,border:ht,leftText:String,rightText:String,leftDisabled:Boolean,rightDisabled:Boolean,leftArrow:Boolean,placeholder:Boolean,safeAreaInsetTop:Boolean,clickable:ht};var Vw=ue({name:Nw,props:Mw,emits:["clickLeft","clickRight"],setup(e,{emit:t,slots:n}){const o=fe(),r=Oh(o,wn),i=f=>{e.leftDisabled||t("clickLeft",f)},l=f=>{e.rightDisabled||t("clickRight",f)},s=()=>n.left?n.left():[e.leftArrow&&m(Kt,{class:wn("arrow"),name:"arrow-left"},null),e.leftText&&m("span",{class:wn("text")},[e.leftText])],a=()=>n.right?n.right():m("span",{class:wn("text")},[e.rightText]),c=()=>{const{title:f,fixed:u,border:d,zIndex:p}=e,h=Sh(p),g=e.leftArrow||e.leftText||n.left,O=e.rightText||n.right;return m("div",{ref:o,style:h,class:[wn({fixed:u}),{[wC]:d,"van-safe-area-top":e.safeAreaInsetTop}]},[m("div",{class:wn("content")},[g&&m("div",{class:[wn("left",{disabled:e.leftDisabled}),e.clickable&&!e.leftDisabled?lr:""],onClick:i},[s()]),m("div",{class:[wn("title"),"van-ellipsis"]},[n.title?n.title():f]),O&&m("div",{class:[wn("right",{disabled:e.rightDisabled}),e.clickable&&!e.rightDisabled?lr:""],onClick:l},[a()])])])};return()=>e.fixed&&e.placeholder?r(c):c()}});const $w=st(Vw),Fw={class:"py-4"},Bw=ue({__name:"default",setup(e){const t=co(),n=Rs();function o(){t.back()}return(r,i)=>{const l=$w,s=El("RouterView"),a=zh;return le(),_e(a,{theme:("isDark"in r?r.isDark:E(Al))?"dark":void 0},{default:$(()=>[m(l,{title:E(n).meta.title,"left-text":"Return","left-arrow":"",placeholder:"",fixed:"",onClickLeft:o},null,8,["title"]),Pe("div",Fw,[(le(),_e(nb,null,{default:$(()=>[m(s)]),_:1}))])]),_:1},8,["theme"])}}}),Uw=Object.freeze(Object.defineProperty({__proto__:null,default:Bw},Symbol.toStringTag,{value:"Module"})),At=(e,t)=>{const n=e.__vccOpts||e;for(const[o,r]of t)n[o]=r;return n},jw={};function Hw(e,t){const n=El("RouterView"),o=zh;return le(),_e(o,{theme:("isDark"in e?e.isDark:E(Al))?"dark":void 0,"py-10":""},{default:$(()=>[m(n)]),_:1},8,["theme"])}const Ww=At(jw,[["render",Hw]]),Gw=Object.freeze(Object.defineProperty({__proto__:null,default:Ww},Symbol.toStringTag,{value:"Module"})),we={},Xh={},Kw=Pe("div",{class:"ml-16px p-20px pt-0 text-32px"}," Live TV ",-1);function Yw(e,t){const n=tt,o=rt;return le(),kt("div",null,[Kw,m(o,{inset:""},{default:$(()=>[m(n,{title:"General","is-link":"",to:"app"}),m(n,{title:"Subscriptions","is-link":"",to:"iptv"}),m(n,{title:"EPG","is-link":"",to:"epg"}),m(n,{title:"UI","is-link":"",to:"ui"}),m(n,{title:"Control","is-link":"",to:"control"}),m(n,{title:"Player","is-link":"",to:"player"}),m(n,{title:"Update","is-link":"",to:"update"}),m(n,{title:"Theme","is-link":"",to:"theme"}),m(n,{title:"Cloud Sync","is-link":"",to:"cloudsync"}),m(n,{title:"Debug","is-link":"",to:"debug"}),m(n,{title:"Log","is-link":"",to:"log"}),m(n,{title:"About","is-link":"",to:"about"})]),_:1})])}typeof we=="function"&&we(Xh);const zw=At(Xh,[["render",Yw]]),Xw=Object.freeze(Object.defineProperty({__proto__:null,default:zw},Symbol.toStringTag,{value:"Module"})),qh=ue({__name:"[...all]",setup(e){const{t}=ni();return(n,o)=>(le(),kt("div",null,xn(E(t)("not-found")),1))}});typeof we=="function"&&we(qh);const qw=Object.freeze(Object.defineProperty({__proto__:null,default:qh},Symbol.toStringTag,{value:"Module"}));function Jw(e){const t=[];return Object.entries(e).forEach(([n,o],r)=>{const i=encodeURIComponent(n),l=encodeURIComponent(String(o));t.push(`${i}=${l}`)}),t.join("&")}const Xt={async request(e,t,n,o,r,i){sw({message:"Loading...",forbidClick:!0,duration:0});try{const l=n?`?${Jw(n)}`:"",s=await fetch(`${t}${l}`,{method:e,headers:r?{"Content-Type":"application/json"}:{},body:r?JSON.stringify(o):o,...i});uw();const a=s.headers.get("Content-Type");return a!=null&&a.includes("text/html")?s.text():a!=null&&a.includes("application/json")?s.json():s.text()}catch(l){throw jh("Request failed"),l}},get(e,t,n){return this.request("GET",e,t,n)},post(e,t,n,o=!0,r){return this.request("POST",e,n,t,o,r)}},nn={getAbout(){return Xt.get("/api/about")},getLogs(){return Xt.get("/api/logs")},getConfigs(){return Xt.get("/api/configs")},changeConfig(e){return Xt.post("/api/configs",e)},getFileContent(e){return Xt.get("/api/file/content",{path:e})},writeFileContent(e,t){return Xt.post("/api/file/content",{path:e,content:t})},writeFileContentWithDir(e,t,n){return Xt.post("/api/file/content-with-dir",{dir:e,filename:t,content:n})},uploadApk(e){const t=new FormData;return t.append("filename",e),Xt.post("/api/upload/apk",t,void 0,!1)},getChannelAlias(){return Xt.get("/api/channel-alias")},changeChannelAlias(e){return Xt.post("/api/channel-alias",e,void 0,!1)},getCloudSyncData(){return Xt.get("/api/cloud-sync/data")},pushCloudSyncData(e){return Xt.post("/api/cloud-sync/data",e)}},hf={DISABLE:"Disable",IPTV_FIRST:"IPTV First",HYBRID_FIRST:"Web Source First"},mf={HIDDEN:"Hidden",ALWAYS:"Always",EVERY_HOUR:"Every Hour",HALF_HOUR:"Half Hour"},gf={MEDIA3:"Media3",IJK:"IjkPlayer"},vf={SURFACE_VIEW:"SurfaceView",TEXTURE_VIEW:"TextureView"},_f={ORIGINAL:"Original",FILL:"Fill",CROP:"Crop",FOUR_THREE:"4:3",SIXTEEN_NINE:"16:9",WIDE:"2.35:1"};var No=(e=>(e.GITHUB_GIST="GITHUB_GIST",e.GITEE_GIST="GITEE_GIST",e.NETWORK_URL="NETWORK_URL",e.LOCAL_FILE="LOCAL_FILE",e.WEBDAV="WEBDAV",e))(No||{});const bf={GITHUB_GIST:"GitHub Gist",GITEE_GIST:"Gitee Gist",NETWORK_URL:"Network URL",LOCAL_FILE:"Local File",WEBDAV:"WebDAV"},Jh=ue({__name:"index",setup(e){const t=fe();return Pt(async()=>{t.value=await nn.getAbout()}),(n,o)=>{const r=tt,i=rt;return E(t)?(le(),_e(i,{key:0,inset:""},{default:$(()=>[m(r,{title:"App ID",value:`${E(t).applicationId}_${E(t).flavor}_${E(t).buildType}`},null,8,["value"]),m(r,{title:"App Version",value:`${E(t).versionName}(${E(t).versionCode})`},null,8,["value"]),m(r,{title:"Device Name",value:E(t).deviceName},null,8,["value"]),m(r,{title:"Device ID",value:E(t).deviceId},null,8,["value"])]),_:1})):at("",!0)}}});typeof we=="function"&&we(Jh);const Qw=At(Jh,[["__scopeId","data-v-ef3812e9"]]),Zw=Object.freeze(Object.defineProperty({__proto__:null,default:Qw},Symbol.toStringTag,{value:"Module"}));function St(){const e=fe({});async function t(){e.value=await nn.getConfigs()}async function n(){await nn.changeConfig(e.value),await t()}return Pt(()=>{t()}),{data:e,refresh:t,update:n}}const Qh=ue({__name:"index",setup(e){const t=St();return(n,o)=>{const r=uo,i=tt,l=ot,s=rt;return le(),_e(s,{inset:""},{default:$(()=>[m(i,{title:"Boot Launch",center:""},{value:$(()=>[m(r,{modelValue:E(t).data.value.appBootLaunch,"onUpdate:modelValue":o[0]||(o[0]=a=>E(t).data.value.appBootLaunch=a)},null,8,["modelValue"])]),_:1}),m(i,{title:"Start Directly in Live",center:""},{value:$(()=>[m(r,{"model-value":E(t).data.value.appStartupScreen==="Live","onUpdate:modelValue":o[1]||(o[1]=a=>E(t).data.value.appStartupScreen=a?"Live":"Dashboard")},null,8,["model-value"])]),_:1}),m(i,{title:"Picture-in-Picture",center:""},{value:$(()=>[m(r,{modelValue:E(t).data.value.appPipEnable,"onUpdate:modelValue":o[2]||(o[2]=a=>E(t).data.value.appPipEnable=a)},null,8,["modelValue"])]),_:1}),m(i,null,{default:$(()=>[m(l,{type:"primary",block:"",onClick:E(t).update},{default:$(()=>[Oe(" Update ")]),_:1},8,["onClick"])]),_:1})]),_:1})}}});typeof we=="function"&&we(Qh);const ek=Object.freeze(Object.defineProperty({__proto__:null,default:Qh},Symbol.toStringTag,{value:"Module"})),pr=ue({__name:"SimplePicker",props:{value:null,columns:null},emits:["update:value"],setup(e,{emit:t}){const n=t,o=fe(!1);function r({selectedValues:i}){n("update:value",i[0]),o.value=!1}return(i,l)=>{const s=$T,a=js;return le(),kt(Be,null,[Pe("div",{onClick:l[0]||(l[0]=c=>o.value=!0)},[jd(i.$slots,"default",{value:e.value})]),m(a,{show:E(o),"onUpdate:show":l[2]||(l[2]=c=>Ge(o)?o.value=c:null),round:"",position:"bottom"},{default:$(()=>[m(s,{columns:e.columns,onCancel:l[1]||(l[1]=c=>o.value=!1),onConfirm:r},null,8,["columns"])]),_:1},8,["show"])],64)}}}),tk={class:"flex flex-col gap-4"},Zh=ue({__name:"index",setup(e){const t=St(),n=Object.entries(bf).map(i=>({text:i[1],value:i[0]}));async function o(){const i=await nn.getCloudSyncData(),l=JSON.stringify(i,null,2),s=new Blob([l],{type:"application/json"}),a=document.createElement("a");a.href=URL.createObjectURL(s),a.download=`${i.syncFrom}-v${i.version}-${i.syncAt}.json`,document.body.appendChild(a),a.click(),document.body.removeChild(a)}async function r(){const{open:i,onChange:l}=dh({accept:".json",multiple:!1});i(),l(s=>{if(!s||!s.length)return;const a=new FileReader;a.onload=async c=>{var f;(f=c.target)!=null&&f.result&&(await nn.pushCloudSyncData(JSON.parse(c.target.result)),cw("App data imported successfully"))},a.onerror=()=>{Hh("Failed to read app data")},a.readAsText(s[0])})}return(i,l)=>{const s=uo,a=tt,c=on,f=pr,u=ot,d=rt;return le(),kt("div",tk,[m(d,{inset:""},{default:$(()=>[m(a,{title:"Auto Pull",center:""},{value:$(()=>[m(s,{modelValue:E(t).data.value.cloudSyncAutoPull,"onUpdate:modelValue":l[0]||(l[0]=p=>E(t).data.value.cloudSyncAutoPull=p)},null,8,["modelValue"])]),_:1}),m(a,{title:"Cloud Sync Provider",center:"","is-link":""},{value:$(()=>[m(f,{value:E(t).data.value.cloudSyncProvider,"onUpdate:value":l[1]||(l[1]=p=>E(t).data.value.cloudSyncProvider=p),columns:E(n)},{default:$(()=>[m(c,{"model-value":E(bf)[E(t).data.value.cloudSyncProvider],"input-align":"right",readonly:""},null,8,["model-value"])]),_:1},8,["value","columns"])]),_:1}),E(t).data.value.cloudSyncProvider===E(No).GITHUB_GIST?(le(),kt(Be,{key:0},[m(a,{title:"Github Gist Id",center:""},{value:$(()=>[m(c,{modelValue:E(t).data.value.cloudSyncGithubGistId,"onUpdate:modelValue":l[2]||(l[2]=p=>E(t).data.value.cloudSyncGithubGistId=p),"input-align":"right"},null,8,["modelValue"])]),_:1}),m(a,{title:"Github Gist Token",center:""},{value:$(()=>[m(c,{modelValue:E(t).data.value.cloudSyncGithubGistToken,"onUpdate:modelValue":l[3]||(l[3]=p=>E(t).data.value.cloudSyncGithubGistToken=p),"input-align":"right"},null,8,["modelValue"])]),_:1})],64)):at("",!0),E(t).data.value.cloudSyncProvider===E(No).GITEE_GIST?(le(),kt(Be,{key:1},[m(a,{title:"Gitee Gist Id",center:""},{value:$(()=>[m(c,{modelValue:E(t).data.value.cloudSyncGiteeGistId,"onUpdate:modelValue":l[4]||(l[4]=p=>E(t).data.value.cloudSyncGiteeGistId=p),"input-align":"right"},null,8,["modelValue"])]),_:1}),m(a,{title:"Gitee Gist Token",center:""},{value:$(()=>[m(c,{modelValue:E(t).data.value.cloudSyncGiteeGistToken,"onUpdate:modelValue":l[5]||(l[5]=p=>E(t).data.value.cloudSyncGiteeGistToken=p),"input-align":"right"},null,8,["modelValue"])]),_:1})],64)):at("",!0),E(t).data.value.cloudSyncProvider===E(No).NETWORK_URL?(le(),_e(a,{key:2,title:"Network URL",center:""},{value:$(()=>[m(c,{modelValue:E(t).data.value.cloudSyncNetworkUrl,"onUpdate:modelValue":l[6]||(l[6]=p=>E(t).data.value.cloudSyncNetworkUrl=p),"input-align":"right"},null,8,["modelValue"])]),_:1})):E(t).data.value.cloudSyncProvider===E(No).LOCAL_FILE?(le(),_e(a,{key:3,title:"Local File Path",center:""},{value:$(()=>[m(c,{modelValue:E(t).data.value.cloudSyncLocalFilePath,"onUpdate:modelValue":l[7]||(l[7]=p=>E(t).data.value.cloudSyncLocalFilePath=p),"input-align":"right"},null,8,["modelValue"])]),_:1})):E(t).data.value.cloudSyncProvider===E(No).WEBDAV?(le(),kt(Be,{key:4},[m(a,{title:"WebDAV URL",center:""},{value:$(()=>[m(c,{modelValue:E(t).data.value.cloudSyncWebDavUrl,"onUpdate:modelValue":l[8]||(l[8]=p=>E(t).data.value.cloudSyncWebDavUrl=p),"input-align":"right"},null,8,["modelValue"])]),_:1}),m(a,{title:"WebDAV Username",center:""},{value:$(()=>[m(c,{modelValue:E(t).data.value.cloudSyncWebDavUsername,"onUpdate:modelValue":l[9]||(l[9]=p=>E(t).data.value.cloudSyncWebDavUsername=p),"input-align":"right"},null,8,["modelValue"])]),_:1}),m(a,{title:"WebDAV Password",center:""},{value:$(()=>[m(c,{modelValue:E(t).data.value.cloudSyncWebDavPassword,"onUpdate:modelValue":l[10]||(l[10]=p=>E(t).data.value.cloudSyncWebDavPassword=p),"input-align":"right"},null,8,["modelValue"])]),_:1})],64)):at("",!0),m(a,null,{default:$(()=>[m(u,{type:"primary",block:"",onClick:E(t).update},{default:$(()=>[Oe(" Update ")]),_:1},8,["onClick"])]),_:1})]),_:1}),m(d,{inset:""},{default:$(()=>[m(a,{title:"Import App Data",value:"",center:"","is-link":"",onClick:r}),m(a,{title:"Export App Data",value:"",center:"","is-link":"",onClick:o})]),_:1})])}}});typeof we=="function"&&we(Zh);const nk=At(Zh,[["__scopeId","data-v-d9c8fd8d"]]),ok=Object.freeze(Object.defineProperty({__proto__:null,default:nk},Symbol.toStringTag,{value:"Module"})),em=ue({__name:"index",setup(e){const t=St();return(n,o)=>{const r=uo,i=tt,l=ot,s=rt;return le(),_e(s,{inset:""},{default:$(()=>[m(i,{title:"Digital Channel Selection",center:""},{value:$(()=>[m(r,{modelValue:E(t).data.value.iptvChannelNoSelectEnable,"onUpdate:modelValue":o[0]||(o[0]=a=>E(t).data.value.iptvChannelNoSelectEnable=a)},null,8,["modelValue"])]),_:1}),m(i,{title:"Channel List Loop",center:""},{value:$(()=>[m(r,{modelValue:E(t).data.value.iptvChannelChangeListLoop,"onUpdate:modelValue":o[1]||(o[1]=a=>E(t).data.value.iptvChannelChangeListLoop=a)},null,8,["modelValue"])]),_:1}),m(i,{title:"Cross-group Channel Switching",center:""},{value:$(()=>[m(r,{modelValue:E(t).data.value.iptvChannelChangeCrossGroup,"onUpdate:modelValue":o[2]||(o[2]=a=>E(t).data.value.iptvChannelChangeCrossGroup=a)},null,8,["modelValue"])]),_:1}),m(i,null,{default:$(()=>[m(l,{type:"primary",block:"",onClick:E(t).update},{default:$(()=>[Oe(" Update ")]),_:1},8,["onClick"])]),_:1})]),_:1})}}});typeof we=="function"&&we(em);const rk=Object.freeze(Object.defineProperty({__proto__:null,default:em},Symbol.toStringTag,{value:"Module"})),ik={class:"flex flex-col gap-4"},tm=ue({__name:"index",setup(e){const t=St();async function n(){const{open:o,onChange:r}=dh({accept:".apk",multiple:!1});o(),r(async i=>{!i||!i.length||await nn.uploadApk(i[0])})}return(o,r)=>{const i=uo,l=tt,s=ot,a=rt;return le(),kt("div",ik,[m(a,{inset:""},{default:$(()=>[m(l,{title:"Developer Mode",center:""},{value:$(()=>[m(i,{modelValue:E(t).data.value.debugDeveloperMode,"onUpdate:modelValue":r[0]||(r[0]=c=>E(t).data.value.debugDeveloperMode=c)},null,8,["modelValue"])]),_:1}),m(l,{title:"Show FPS",center:""},{value:$(()=>[m(i,{modelValue:E(t).data.value.debugShowFps,"onUpdate:modelValue":r[1]||(r[1]=c=>E(t).data.value.debugShowFps=c)},null,8,["modelValue"])]),_:1}),m(l,{title:"Show Player Info",center:""},{value:$(()=>[m(i,{modelValue:E(t).data.value.debugShowVideoPlayerMetadata,"onUpdate:modelValue":r[2]||(r[2]=c=>E(t).data.value.debugShowVideoPlayerMetadata=c)},null,8,["modelValue"])]),_:1}),m(l,{title:"Show Layout Grids",center:""},{value:$(()=>[m(i,{modelValue:E(t).data.value.debugShowLayoutGrids,"onUpdate:modelValue":r[3]||(r[3]=c=>E(t).data.value.debugShowLayoutGrids=c)},null,8,["modelValue"])]),_:1}),m(l,null,{default:$(()=>[m(s,{type:"primary",block:"",onClick:E(t).update},{default:$(()=>[Oe(" Update ")]),_:1},8,["onClick"])]),_:1})]),_:1}),m(a,{inset:""},{default:$(()=>[m(l,{title:"Upload APK",value:"",center:"","is-link":"",onClick:n})]),_:1})])}}});typeof we=="function"&&we(tm);const lk=Object.freeze(Object.defineProperty({__proto__:null,default:tm},Symbol.toStringTag,{value:"Module"})),nm=ue({__name:"index",setup(e){const t=St(),n=[...Array.from({length:13},(o,r)=>r).map(o=>({text:`${o.toString().padStart(2,"0")}:00`,value:o}))];return(o,r)=>{const i=uo,l=tt,s=on,a=pr,c=ot,f=rt;return le(),_e(f,{inset:""},{default:$(()=>{var u;return[m(l,{title:"EPG Enable",center:""},{value:$(()=>[m(i,{modelValue:E(t).data.value.epgEnable,"onUpdate:modelValue":r[0]||(r[0]=d=>E(t).data.value.epgEnable=d)},null,8,["modelValue"])]),_:1}),m(l,{title:"Follow Source",center:""},{value:$(()=>[m(i,{modelValue:E(t).data.value.epgSourceFollowIptv,"onUpdate:modelValue":r[1]||(r[1]=d=>E(t).data.value.epgSourceFollowIptv=d)},null,8,["modelValue"])]),_:1}),m(l,{title:"Custom EPG",value:(u=E(t).data.value.epgSourceCurrent)==null?void 0:u.name,"is-link":"",center:"",to:"epg/epg-source-list"},null,8,["value"]),m(l,{title:"Refresh Time Threshold",center:"","is-link":""},{value:$(()=>[m(a,{value:E(t).data.value.epgRefreshTimeThreshold,"onUpdate:value":r[2]||(r[2]=d=>E(t).data.value.epgRefreshTimeThreshold=d),columns:n},{default:$(()=>{var d;return[m(s,{"model-value":((d=n.find(p=>p.value===E(t).data.value.epgRefreshTimeThreshold))==null?void 0:d.text)??`${E(t).data.value.epgRefreshTimeThreshold}:00`,"input-align":"right",readonly:""},null,8,["model-value"])]}),_:1},8,["value"])]),_:1}),m(l,null,{default:$(()=>[m(c,{type:"primary",block:"",onClick:E(t).update},{default:$(()=>[Oe(" Update ")]),_:1},8,["onClick"])]),_:1})]}),_:1})}}});typeof we=="function"&&we(nm);const ak=At(nm,[["__scopeId","data-v-1b434384"]]),sk=Object.freeze(Object.defineProperty({__proto__:null,default:ak},Symbol.toStringTag,{value:"Module"})),om=ue({__name:"index",setup(e){const t=co(),n=St(),o=fe({name:"",url:""});async function r(){var i;n.data.value.epgSourceList={value:[...((i=n.data.value.epgSourceList)==null?void 0:i.value)??[],o.value]},await n.update(),t.back()}return(i,l)=>{const s=on,a=tt,c=ot,f=rt;return E(o)?(le(),_e(f,{key:0,inset:""},{default:$(()=>[m(a,{title:"Name",center:""},{value:$(()=>[m(s,{modelValue:E(o).name,"onUpdate:modelValue":l[0]||(l[0]=u=>E(o).name=u),"input-align":"right"},null,8,["modelValue"])]),_:1}),m(a,{title:"URL",center:""},{value:$(()=>[m(s,{modelValue:E(o).url,"onUpdate:modelValue":l[1]||(l[1]=u=>E(o).url=u),"input-align":"right"},null,8,["modelValue"])]),_:1}),m(a,null,{default:$(()=>[m(c,{type:"primary",block:"",onClick:r},{default:$(()=>[Oe(" Confirm ")]),_:1})]),_:1})]),_:1})):at("",!0)}}});typeof we=="function"&&we(om);const ck=At(om,[["__scopeId","data-v-f39be8f1"]]),uk=Object.freeze(Object.defineProperty({__proto__:null,default:ck},Symbol.toStringTag,{value:"Module"})),fk={class:"flex gap-4"},rm=ue({__name:"[idx]",setup(e){const t=co(),n=Rs(),o=St(),{idx:r}=n.params,i=he(()=>{var c;return(c=o.data.value.epgSourceList)==null?void 0:c.value[r]});async function l(){await o.update(),t.back()}async function s(){var c;(c=o.data.value.epgSourceList)==null||c.value.splice(r,1),await o.update(),t.back()}async function a(){o.data.value.epgSourceCurrent=i.value,await o.update(),t.back()}return(c,f)=>{const u=on,d=tt,p=ot,h=rt;return E(i)?(le(),_e(h,{key:0,inset:""},{default:$(()=>[m(d,{title:"Name",center:""},{value:$(()=>[m(u,{modelValue:E(i).name,"onUpdate:modelValue":f[0]||(f[0]=g=>E(i).name=g),"input-align":"right"},null,8,["modelValue"])]),_:1}),m(d,{title:"URL",center:""},{value:$(()=>[m(u,{modelValue:E(i).url,"onUpdate:modelValue":f[1]||(f[1]=g=>E(i).url=g),"input-align":"right"},null,8,["modelValue"])]),_:1}),m(d,null,{default:$(()=>[Pe("div",fk,[m(p,{type:"danger",block:"",onClick:s},{default:$(()=>[Oe(" Delete ")]),_:1}),m(p,{type:"primary",block:"",onClick:l},{default:$(()=>[Oe(" Update ")]),_:1}),m(p,{type:"warning",block:"",onClick:a},{default:$(()=>[Oe(" Switch ")]),_:1})])]),_:1})]),_:1})):at("",!0)}}});typeof we=="function"&&we(rm);const dk=At(rm,[["__scopeId","data-v-98071427"]]),pk=Object.freeze(Object.defineProperty({__proto__:null,default:dk},Symbol.toStringTag,{value:"Module"})),im=ue({__name:"index",setup(e){const t=St(),n=co();function o(){n.push("epg-source-add")}return(r,i)=>{const l=tt,s=ot,a=rt;return le(),_e(a,{inset:""},{default:$(()=>{var c;return[(le(!0),kt(Be,null,Sl((c=E(t).data.value.epgSourceList)==null?void 0:c.value,(f,u)=>(le(),_e(l,{key:u,title:f.name,label:f.url,"is-link":"",center:"",to:`epg-source-detail/${u}`},null,8,["title","label","to"]))),128)),m(l,null,{default:$(()=>[m(s,{type:"primary",block:"",onClick:o},{default:$(()=>[Oe(" Add ")]),_:1})]),_:1})]}),_:1})}}});typeof we=="function"&&we(im);const hk=At(im,[["__scopeId","data-v-dfdf67cb"]]),mk=Object.freeze(Object.defineProperty({__proto__:null,default:hk},Symbol.toStringTag,{value:"Module"})),gk={class:"overflow-x-auto px-8 py-4"},Ws=ue({__name:"CellTitle",props:{title:null,help:null},setup(e){const t=fe(!1);return(n,o)=>{const r=Kt,i=Rw;return le(),kt(Be,null,[Pe("div",{class:"flex items-center gap-1",onClick:o[0]||(o[0]=l=>t.value=!0)},[Oe(xn(e.title)+" ",1),m(r,{name:"info-o",class:"op-60"})]),m(i,{show:E(t),"onUpdate:show":o[1]||(o[1]=l=>Ge(t)?t.value=l:null),title:"Help"},{default:$(()=>[Pe("div",gk,[jd(n.$slots,"help",{},()=>[Oe(xn(e.help),1)])])]),_:3},8,["show"])],64)}}}),vk=e=>(Cs("data-v-7279b46f"),e=e(),Ts(),e),_k={class:"flex items-center justify-end gap-2"},bk=vk(()=>Pe("div",{class:"flex flex-col gap-1"},[Pe("div",null,"Format:"),Pe("div",null,"{name} - Keep unchanged"),Pe("div",null,"{name|lowercase} - Lowercase"),Pe("div",null,"{name|uppercase} - Uppercase")],-1)),lm=ue({__name:"index",setup(e){const t=St(),n=[{text:"No Cache",value:0},...Array.from({length:23},(r,i)=>i+1).map(r=>({text:`${r} hour`,value:r*1e3*60*60})),...Array.from({length:15},(r,i)=>i+1).map(r=>({text:`${r} day`,value:r*24*1e3*60*60})),{text:"Permanent",value:9223372036854776e3}],o=Object.entries(hf).map(r=>({text:r[1],value:r[0]}));return(r,i)=>{const l=Kh,s=tt,a=on,c=pr,f=uo,u=Ws,d=ot,p=rt;return le(),_e(p,{inset:""},{default:$(()=>{var h;return[m(s,{title:"Custom Subscription Source","is-link":"",center:"",to:"iptv/iptv-source-list"},{value:$(()=>{var g,O,A;return[Pe("div",_k,[((g=E(t).data.value.iptvSourceCurrent)==null?void 0:g.sourceType)===1?(le(),_e(l,{key:0,plain:"",type:"warning",class:"flex-none"},{default:$(()=>[Oe(" Local ")]),_:1})):((O=E(t).data.value.iptvSourceCurrent)==null?void 0:O.sourceType)===2?(le(),_e(l,{key:1,plain:"",type:"danger",class:"flex-none"},{default:$(()=>[Oe(" XTREAM ")]),_:1})):(le(),_e(l,{key:2,plain:"",type:"primary",class:"flex-none"},{default:$(()=>[Oe(" Remote ")]),_:1})),Oe(" "+xn((A=E(t).data.value.iptvSourceCurrent)==null?void 0:A.name),1)])]}),_:1}),m(s,{title:"Source Cache Time",center:"","is-link":""},{value:$(()=>[m(c,{value:E(t).data.value.iptvSourceCacheTime,"onUpdate:value":i[0]||(i[0]=g=>E(t).data.value.iptvSourceCacheTime=g),columns:n},{default:$(()=>{var g;return[m(a,{"model-value":((g=n.find(O=>O.value===E(t).data.value.iptvSourceCacheTime))==null?void 0:g.text)??`${E(t).data.value.iptvSourceCacheTime}ms`,"input-align":"right",readonly:""},null,8,["model-value"])]}),_:1},8,["value"])]),_:1}),m(s,{title:"Channel Alias",value:"",center:"","is-link":"",to:"iptv/channel-alias"}),m(s,{title:"Merge Similar Channels",center:""},{value:$(()=>[m(f,{modelValue:E(t).data.value.iptvSimilarChannelMerge,"onUpdate:modelValue":i[1]||(i[1]=g=>E(t).data.value.iptvSimilarChannelMerge=g)},null,8,["modelValue"])]),_:1}),m(s,{center:""},{title:$(()=>[m(u,{title:"Channel Logo Provider"},{help:$(()=>[bk]),_:1})]),value:$(()=>[m(a,{modelValue:E(t).data.value.iptvChannelLogoProvider,"onUpdate:modelValue":i[2]||(i[2]=g=>E(t).data.value.iptvChannelLogoProvider=g),"input-align":"right"},null,8,["modelValue"])]),_:1}),m(s,{center:""},{title:$(()=>[m(u,{title:"Web Source Yangshipin Cookie"})]),value:$(()=>[m(a,{modelValue:E(t).data.value.iptvHybridYangshipinCookie,"onUpdate:modelValue":i[3]||(i[3]=g=>E(t).data.value.iptvHybridYangshipinCookie=g),"input-align":"right"},null,8,["modelValue"])]),_:1}),m(s,{title:"Auto Add Web Source",center:"","is-link":""},{value:$(()=>[m(c,{value:E(t).data.value.iptvHybridMode,"onUpdate:value":i[4]||(i[4]=g=>E(t).data.value.iptvHybridMode=g),columns:E(o)},{default:$(()=>[m(a,{"model-value":E(hf)[E(t).data.value.iptvHybridMode],"input-align":"right",readonly:""},null,8,["model-value"])]),_:1},8,["value","columns"])]),_:1}),m(s,{title:"Favorite Channels",value:`Total ${(h=E(t).data.value.iptvChannelFavoriteList)==null?void 0:h.value.length}`,"is-link":"",to:"iptv/favorites"},null,8,["value"]),m(s,null,{default:$(()=>[m(d,{type:"primary",block:"",onClick:E(t).update},{default:$(()=>[Oe(" Update ")]),_:1},8,["onClick"])]),_:1})]}),_:1})}}});typeof we=="function"&&we(lm);const yk=At(lm,[["__scopeId","data-v-7279b46f"]]),Ek=Object.freeze(Object.defineProperty({__proto__:null,default:yk},Symbol.toStringTag,{value:"Module"})),am=ue({__name:"index",setup(e){const t=fe(),n=JSON.stringify({__suffix:["-HighBitrate","-HD"],CCTV1:["CCTV1HD","CCTV1 HD"]},null,2);Pt(async()=>{t.value=await nn.getChannelAlias()});async function o(){try{t.value=JSON.stringify(JSON.parse(t.value),null,2),await nn.changeChannelAlias(t.value)}catch(r){console.error(r),Hh({message:"Channel alias format error"})}}return(r,i)=>{const l=on,s=tt,a=ot,c=rt;return le(),_e(c,{inset:""},{default:$(()=>[m(s,null,{default:$(()=>[m(l,{modelValue:E(t),"onUpdate:modelValue":i[0]||(i[0]=f=>Ge(t)?t.value=f:null),placeholder:E(n),type:"textarea",autosize:""},null,8,["modelValue","placeholder"])]),_:1}),m(s,null,{default:$(()=>[m(a,{type:"primary",block:"",onClick:o},{default:$(()=>[Oe(" Confirm ")]),_:1})]),_:1})]),_:1})}}});typeof we=="function"&&we(am);const Sk=At(am,[["__scopeId","data-v-08899257"]]),Ck=Object.freeze(Object.defineProperty({__proto__:null,default:Sk},Symbol.toStringTag,{value:"Module"}));var Tk=Object.defineProperty,il=Object.getOwnPropertySymbols,sm=Object.prototype.hasOwnProperty,cm=Object.prototype.propertyIsEnumerable,yf=(e,t,n)=>t in e?Tk(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Fo=(e,t)=>{for(var n in t||(t={}))sm.call(t,n)&&yf(e,n,t[n]);if(il)for(var n of il(t))cm.call(t,n)&&yf(e,n,t[n]);return e},um=(e,t)=>{var n={};for(var o in e)sm.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&il)for(var o of il(e))t.indexOf(o)<0&&cm.call(e,o)&&(n[o]=e[o]);return n};const fm="[vue-draggable-plus]: ";function wk(e){console.warn(fm+e)}function kk(e){console.error(fm+e)}function Ef(e,t,n){return n>=0&&n<e.length&&e.splice(n,0,e.splice(t,1)[0]),e}function Ok(e){return e.replace(/-(\w)/g,(t,n)=>n?n.toUpperCase():"")}function Pk(e){return Object.keys(e).reduce((t,n)=>(typeof e[n]<"u"&&(t[Ok(n)]=e[n]),t),{})}function Sf(e,t){return Array.isArray(e)&&e.splice(t,1),e}function Cf(e,t,n){return Array.isArray(e)&&e.splice(t,0,n),e}function Ak(e){return typeof e>"u"}function Ik(e){return typeof e=="string"}function Tf(e,t,n){const o=e.children[n];e.insertBefore(t,o)}function ia(e){e.parentNode&&e.parentNode.removeChild(e)}function xk(e,t=document){var n;let o=null;return typeof(t==null?void 0:t.querySelector)=="function"?o=(n=t==null?void 0:t.querySelector)==null?void 0:n.call(t,e):o=document.querySelector(e),o||wk(`Element not found: ${e}`),o}function Lk(e,t,n=null){return function(...o){return e.apply(n,o),t.apply(n,o)}}function Dk(e,t){const n=Fo({},e);return Object.keys(t).forEach(o=>{n[o]?n[o]=Lk(e[o],t[o]):n[o]=t[o]}),n}function Rk(e){return e instanceof HTMLElement}function wf(e,t){Object.keys(e).forEach(n=>{t(n,e[n])})}function Nk(e){return e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97)}const Mk=Object.assign;/**!
 * Sortable 1.15.2
 * <AUTHOR>   <<EMAIL>>
 * <AUTHOR>    <<EMAIL>>
 * @license MIT
 */function kf(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),n.push.apply(n,o)}return n}function yn(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?kf(Object(n),!0).forEach(function(o){Vk(e,o,n[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):kf(Object(n)).forEach(function(o){Object.defineProperty(e,o,Object.getOwnPropertyDescriptor(n,o))})}return e}function Ni(e){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Ni=function(t){return typeof t}:Ni=function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ni(e)}function Vk(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ln(){return Ln=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},Ln.apply(this,arguments)}function $k(e,t){if(e==null)return{};var n={},o=Object.keys(e),r,i;for(i=0;i<o.length;i++)r=o[i],!(t.indexOf(r)>=0)&&(n[r]=e[r]);return n}function Fk(e,t){if(e==null)return{};var n=$k(e,t),o,r;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)o=i[r],!(t.indexOf(o)>=0)&&Object.prototype.propertyIsEnumerable.call(e,o)&&(n[o]=e[o])}return n}var Bk="1.15.2";function In(e){if(typeof window<"u"&&window.navigator)return!!navigator.userAgent.match(e)}var Rn=In(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),li=In(/Edge/i),Of=In(/firefox/i),Rr=In(/safari/i)&&!In(/chrome/i)&&!In(/android/i),dm=In(/iP(ad|od|hone)/i),pm=In(/chrome/i)&&In(/android/i),hm={capture:!1,passive:!1};function Ne(e,t,n){e.addEventListener(t,n,!Rn&&hm)}function Ie(e,t,n){e.removeEventListener(t,n,!Rn&&hm)}function ll(e,t){if(t){if(t[0]===">"&&(t=t.substring(1)),e)try{if(e.matches)return e.matches(t);if(e.msMatchesSelector)return e.msMatchesSelector(t);if(e.webkitMatchesSelector)return e.webkitMatchesSelector(t)}catch{return!1}return!1}}function Uk(e){return e.host&&e!==document&&e.host.nodeType?e.host:e.parentNode}function sn(e,t,n,o){if(e){n=n||document;do{if(t!=null&&(t[0]===">"?e.parentNode===n&&ll(e,t):ll(e,t))||o&&e===n)return e;if(e===n)break}while(e=Uk(e))}return null}var Pf=/\s+/g;function Ut(e,t,n){if(e&&t)if(e.classList)e.classList[n?"add":"remove"](t);else{var o=(" "+e.className+" ").replace(Pf," ").replace(" "+t+" "," ");e.className=(o+(n?" "+t:"")).replace(Pf," ")}}function be(e,t,n){var o=e&&e.style;if(o){if(n===void 0)return document.defaultView&&document.defaultView.getComputedStyle?n=document.defaultView.getComputedStyle(e,""):e.currentStyle&&(n=e.currentStyle),t===void 0?n:n[t];!(t in o)&&t.indexOf("webkit")===-1&&(t="-webkit-"+t),o[t]=n+(typeof n=="string"?"":"px")}}function Xo(e,t){var n="";if(typeof e=="string")n=e;else do{var o=be(e,"transform");o&&o!=="none"&&(n=o+" "+n)}while(!t&&(e=e.parentNode));var r=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return r&&new r(n)}function mm(e,t,n){if(e){var o=e.getElementsByTagName(t),r=0,i=o.length;if(n)for(;r<i;r++)n(o[r],r);return o}return[]}function bn(){var e=document.scrollingElement;return e||document.documentElement}function lt(e,t,n,o,r){if(!(!e.getBoundingClientRect&&e!==window)){var i,l,s,a,c,f,u;if(e!==window&&e.parentNode&&e!==bn()?(i=e.getBoundingClientRect(),l=i.top,s=i.left,a=i.bottom,c=i.right,f=i.height,u=i.width):(l=0,s=0,a=window.innerHeight,c=window.innerWidth,f=window.innerHeight,u=window.innerWidth),(t||n)&&e!==window&&(r=r||e.parentNode,!Rn))do if(r&&r.getBoundingClientRect&&(be(r,"transform")!=="none"||n&&be(r,"position")!=="static")){var d=r.getBoundingClientRect();l-=d.top+parseInt(be(r,"border-top-width")),s-=d.left+parseInt(be(r,"border-left-width")),a=l+i.height,c=s+i.width;break}while(r=r.parentNode);if(o&&e!==window){var p=Xo(r||e),h=p&&p.a,g=p&&p.d;p&&(l/=g,s/=h,u/=h,f/=g,a=l+f,c=s+u)}return{top:l,left:s,bottom:a,right:c,width:u,height:f}}}function Af(e,t,n){for(var o=Xn(e,!0),r=lt(e)[t];o;){var i=lt(o)[n],l=void 0;if(l=r>=i,!l)return o;if(o===bn())break;o=Xn(o,!1)}return!1}function ar(e,t,n,o){for(var r=0,i=0,l=e.children;i<l.length;){if(l[i].style.display!=="none"&&l[i]!==ye.ghost&&(o||l[i]!==ye.dragged)&&sn(l[i],n.draggable,e,!1)){if(r===t)return l[i];r++}i++}return null}function Gs(e,t){for(var n=e.lastElementChild;n&&(n===ye.ghost||be(n,"display")==="none"||t&&!ll(n,t));)n=n.previousElementSibling;return n||null}function Jt(e,t){var n=0;if(!e||!e.parentNode)return-1;for(;e=e.previousElementSibling;)e.nodeName.toUpperCase()!=="TEMPLATE"&&e!==ye.clone&&(!t||ll(e,t))&&n++;return n}function If(e){var t=0,n=0,o=bn();if(e)do{var r=Xo(e),i=r.a,l=r.d;t+=e.scrollLeft*i,n+=e.scrollTop*l}while(e!==o&&(e=e.parentNode));return[t,n]}function jk(e,t){for(var n in e)if(e.hasOwnProperty(n)){for(var o in t)if(t.hasOwnProperty(o)&&t[o]===e[n][o])return Number(n)}return-1}function Xn(e,t){if(!e||!e.getBoundingClientRect)return bn();var n=e,o=!1;do if(n.clientWidth<n.scrollWidth||n.clientHeight<n.scrollHeight){var r=be(n);if(n.clientWidth<n.scrollWidth&&(r.overflowX=="auto"||r.overflowX=="scroll")||n.clientHeight<n.scrollHeight&&(r.overflowY=="auto"||r.overflowY=="scroll")){if(!n.getBoundingClientRect||n===document.body)return bn();if(o||t)return n;o=!0}}while(n=n.parentNode);return bn()}function Hk(e,t){if(e&&t)for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);return e}function la(e,t){return Math.round(e.top)===Math.round(t.top)&&Math.round(e.left)===Math.round(t.left)&&Math.round(e.height)===Math.round(t.height)&&Math.round(e.width)===Math.round(t.width)}var Nr;function gm(e,t){return function(){if(!Nr){var n=arguments,o=this;n.length===1?e.call(o,n[0]):e.apply(o,n),Nr=setTimeout(function(){Nr=void 0},t)}}}function Wk(){clearTimeout(Nr),Nr=void 0}function vm(e,t,n){e.scrollLeft+=t,e.scrollTop+=n}function _m(e){var t=window.Polymer,n=window.jQuery||window.Zepto;return t&&t.dom?t.dom(e).cloneNode(!0):n?n(e).clone(!0)[0]:e.cloneNode(!0)}function bm(e,t,n){var o={};return Array.from(e.children).forEach(function(r){var i,l,s,a;if(!(!sn(r,t.draggable,e,!1)||r.animated||r===n)){var c=lt(r);o.left=Math.min((i=o.left)!==null&&i!==void 0?i:1/0,c.left),o.top=Math.min((l=o.top)!==null&&l!==void 0?l:1/0,c.top),o.right=Math.max((s=o.right)!==null&&s!==void 0?s:-1/0,c.right),o.bottom=Math.max((a=o.bottom)!==null&&a!==void 0?a:-1/0,c.bottom)}}),o.width=o.right-o.left,o.height=o.bottom-o.top,o.x=o.left,o.y=o.top,o}var Gt="Sortable"+new Date().getTime();function Gk(){var e=[],t;return{captureAnimationState:function(){if(e=[],!!this.options.animation){var n=[].slice.call(this.el.children);n.forEach(function(o){if(!(be(o,"display")==="none"||o===ye.ghost)){e.push({target:o,rect:lt(o)});var r=yn({},e[e.length-1].rect);if(o.thisAnimationDuration){var i=Xo(o,!0);i&&(r.top-=i.f,r.left-=i.e)}o.fromRect=r}})}},addAnimationState:function(n){e.push(n)},removeAnimationState:function(n){e.splice(jk(e,{target:n}),1)},animateAll:function(n){var o=this;if(!this.options.animation){clearTimeout(t),typeof n=="function"&&n();return}var r=!1,i=0;e.forEach(function(l){var s=0,a=l.target,c=a.fromRect,f=lt(a),u=a.prevFromRect,d=a.prevToRect,p=l.rect,h=Xo(a,!0);h&&(f.top-=h.f,f.left-=h.e),a.toRect=f,a.thisAnimationDuration&&la(u,f)&&!la(c,f)&&(p.top-f.top)/(p.left-f.left)===(c.top-f.top)/(c.left-f.left)&&(s=Yk(p,u,d,o.options)),la(f,c)||(a.prevFromRect=c,a.prevToRect=f,s||(s=o.options.animation),o.animate(a,p,f,s)),s&&(r=!0,i=Math.max(i,s),clearTimeout(a.animationResetTimer),a.animationResetTimer=setTimeout(function(){a.animationTime=0,a.prevFromRect=null,a.fromRect=null,a.prevToRect=null,a.thisAnimationDuration=null},s),a.thisAnimationDuration=s)}),clearTimeout(t),r?t=setTimeout(function(){typeof n=="function"&&n()},i):typeof n=="function"&&n(),e=[]},animate:function(n,o,r,i){if(i){be(n,"transition",""),be(n,"transform","");var l=Xo(this.el),s=l&&l.a,a=l&&l.d,c=(o.left-r.left)/(s||1),f=(o.top-r.top)/(a||1);n.animatingX=!!c,n.animatingY=!!f,be(n,"transform","translate3d("+c+"px,"+f+"px,0)"),this.forRepaintDummy=Kk(n),be(n,"transition","transform "+i+"ms"+(this.options.easing?" "+this.options.easing:"")),be(n,"transform","translate3d(0,0,0)"),typeof n.animated=="number"&&clearTimeout(n.animated),n.animated=setTimeout(function(){be(n,"transition",""),be(n,"transform",""),n.animated=!1,n.animatingX=!1,n.animatingY=!1},i)}}}}function Kk(e){return e.offsetWidth}function Yk(e,t,n,o){return Math.sqrt(Math.pow(t.top-e.top,2)+Math.pow(t.left-e.left,2))/Math.sqrt(Math.pow(t.top-n.top,2)+Math.pow(t.left-n.left,2))*o.animation}var xo=[],aa={initializeByDefault:!0},ai={mount:function(e){for(var t in aa)aa.hasOwnProperty(t)&&!(t in e)&&(e[t]=aa[t]);xo.forEach(function(n){if(n.pluginName===e.pluginName)throw"Sortable: Cannot mount plugin ".concat(e.pluginName," more than once")}),xo.push(e)},pluginEvent:function(e,t,n){var o=this;this.eventCanceled=!1,n.cancel=function(){o.eventCanceled=!0};var r=e+"Global";xo.forEach(function(i){t[i.pluginName]&&(t[i.pluginName][r]&&t[i.pluginName][r](yn({sortable:t},n)),t.options[i.pluginName]&&t[i.pluginName][e]&&t[i.pluginName][e](yn({sortable:t},n)))})},initializePlugins:function(e,t,n,o){xo.forEach(function(l){var s=l.pluginName;if(!(!e.options[s]&&!l.initializeByDefault)){var a=new l(e,t,e.options);a.sortable=e,a.options=e.options,e[s]=a,Ln(n,a.defaults)}});for(var r in e.options)if(e.options.hasOwnProperty(r)){var i=this.modifyOption(e,r,e.options[r]);typeof i<"u"&&(e.options[r]=i)}},getEventProperties:function(e,t){var n={};return xo.forEach(function(o){typeof o.eventProperties=="function"&&Ln(n,o.eventProperties.call(t[o.pluginName],e))}),n},modifyOption:function(e,t,n){var o;return xo.forEach(function(r){e[r.pluginName]&&r.optionListeners&&typeof r.optionListeners[t]=="function"&&(o=r.optionListeners[t].call(e[r.pluginName],n))}),o}};function zk(e){var t=e.sortable,n=e.rootEl,o=e.name,r=e.targetEl,i=e.cloneEl,l=e.toEl,s=e.fromEl,a=e.oldIndex,c=e.newIndex,f=e.oldDraggableIndex,u=e.newDraggableIndex,d=e.originalEvent,p=e.putSortable,h=e.extraEventProperties;if(t=t||n&&n[Gt],!!t){var g,O=t.options,A="on"+o.charAt(0).toUpperCase()+o.substr(1);window.CustomEvent&&!Rn&&!li?g=new CustomEvent(o,{bubbles:!0,cancelable:!0}):(g=document.createEvent("Event"),g.initEvent(o,!0,!0)),g.to=l||n,g.from=s||n,g.item=r||n,g.clone=i,g.oldIndex=a,g.newIndex=c,g.oldDraggableIndex=f,g.newDraggableIndex=u,g.originalEvent=d,g.pullMode=p?p.lastPutMode:void 0;var V=yn(yn({},h),ai.getEventProperties(o,t));for(var v in V)g[v]=V[v];n&&n.dispatchEvent(g),O[A]&&O[A].call(t,g)}}var Xk=["evt"],Nt=function(e,t){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},o=n.evt,r=Fk(n,Xk);ai.pluginEvent.bind(ye)(e,t,yn({dragEl:ne,parentEl:Qe,ghostEl:Te,rootEl:Ke,nextEl:bo,lastDownEl:Mi,cloneEl:Xe,cloneHidden:zn,dragStarted:Sr,putSortable:gt,activeSortable:ye.active,originalEvent:o,oldIndex:Bo,oldDraggableIndex:Mr,newIndex:jt,newDraggableIndex:Kn,hideGhostForTarget:Cm,unhideGhostForTarget:Tm,cloneNowHidden:function(){zn=!0},cloneNowShown:function(){zn=!1},dispatchSortableEvent:function(i){xt({sortable:t,name:i,originalEvent:o})}},r))};function xt(e){zk(yn({putSortable:gt,cloneEl:Xe,targetEl:ne,rootEl:Ke,oldIndex:Bo,oldDraggableIndex:Mr,newIndex:jt,newDraggableIndex:Kn},e))}var ne,Qe,Te,Ke,bo,Mi,Xe,zn,Bo,jt,Mr,Kn,ki,gt,Mo=!1,al=!1,sl=[],vo,an,sa,ca,xf,Lf,Sr,Lo,Vr,$r=!1,Oi=!1,Vi,Tt,ua=[],za=!1,cl=[],Dl=typeof document<"u",Pi=dm,Df=li||Rn?"cssFloat":"float",qk=Dl&&!pm&&!dm&&"draggable"in document.createElement("div"),ym=function(){if(Dl){if(Rn)return!1;var e=document.createElement("x");return e.style.cssText="pointer-events:auto",e.style.pointerEvents==="auto"}}(),Em=function(e,t){var n=be(e),o=parseInt(n.width)-parseInt(n.paddingLeft)-parseInt(n.paddingRight)-parseInt(n.borderLeftWidth)-parseInt(n.borderRightWidth),r=ar(e,0,t),i=ar(e,1,t),l=r&&be(r),s=i&&be(i),a=l&&parseInt(l.marginLeft)+parseInt(l.marginRight)+lt(r).width,c=s&&parseInt(s.marginLeft)+parseInt(s.marginRight)+lt(i).width;if(n.display==="flex")return n.flexDirection==="column"||n.flexDirection==="column-reverse"?"vertical":"horizontal";if(n.display==="grid")return n.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(r&&l.float&&l.float!=="none"){var f=l.float==="left"?"left":"right";return i&&(s.clear==="both"||s.clear===f)?"vertical":"horizontal"}return r&&(l.display==="block"||l.display==="flex"||l.display==="table"||l.display==="grid"||a>=o&&n[Df]==="none"||i&&n[Df]==="none"&&a+c>o)?"vertical":"horizontal"},Jk=function(e,t,n){var o=n?e.left:e.top,r=n?e.right:e.bottom,i=n?e.width:e.height,l=n?t.left:t.top,s=n?t.right:t.bottom,a=n?t.width:t.height;return o===l||r===s||o+i/2===l+a/2},Qk=function(e,t){var n;return sl.some(function(o){var r=o[Gt].options.emptyInsertThreshold;if(!(!r||Gs(o))){var i=lt(o),l=e>=i.left-r&&e<=i.right+r,s=t>=i.top-r&&t<=i.bottom+r;if(l&&s)return n=o}}),n},Sm=function(e){function t(r,i){return function(l,s,a,c){var f=l.options.group.name&&s.options.group.name&&l.options.group.name===s.options.group.name;if(r==null&&(i||f))return!0;if(r==null||r===!1)return!1;if(i&&r==="clone")return r;if(typeof r=="function")return t(r(l,s,a,c),i)(l,s,a,c);var u=(i?l:s).options.group.name;return r===!0||typeof r=="string"&&r===u||r.join&&r.indexOf(u)>-1}}var n={},o=e.group;(!o||Ni(o)!="object")&&(o={name:o}),n.name=o.name,n.checkPull=t(o.pull,!0),n.checkPut=t(o.put),n.revertClone=o.revertClone,e.group=n},Cm=function(){!ym&&Te&&be(Te,"display","none")},Tm=function(){!ym&&Te&&be(Te,"display","")};Dl&&!pm&&document.addEventListener("click",function(e){if(al)return e.preventDefault(),e.stopPropagation&&e.stopPropagation(),e.stopImmediatePropagation&&e.stopImmediatePropagation(),al=!1,!1},!0);var _o=function(e){if(ne){e=e.touches?e.touches[0]:e;var t=Qk(e.clientX,e.clientY);if(t){var n={};for(var o in e)e.hasOwnProperty(o)&&(n[o]=e[o]);n.target=n.rootEl=t,n.preventDefault=void 0,n.stopPropagation=void 0,t[Gt]._onDragOver(n)}}},Zk=function(e){ne&&ne.parentNode[Gt]._isOutsideThisEl(e.target)};function ye(e,t){if(!(e&&e.nodeType&&e.nodeType===1))throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(e));this.el=e,this.options=t=Ln({},t),e[Gt]=this;var n={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(e.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return Em(e,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(i,l){i.setData("Text",l.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:ye.supportPointer!==!1&&"PointerEvent"in window&&!Rr,emptyInsertThreshold:5};ai.initializePlugins(this,e,n);for(var o in n)!(o in t)&&(t[o]=n[o]);Sm(t);for(var r in this)r.charAt(0)==="_"&&typeof this[r]=="function"&&(this[r]=this[r].bind(this));this.nativeDraggable=t.forceFallback?!1:qk,this.nativeDraggable&&(this.options.touchStartThreshold=1),t.supportPointer?Ne(e,"pointerdown",this._onTapStart):(Ne(e,"mousedown",this._onTapStart),Ne(e,"touchstart",this._onTapStart)),this.nativeDraggable&&(Ne(e,"dragover",this),Ne(e,"dragenter",this)),sl.push(this.el),t.store&&t.store.get&&this.sort(t.store.get(this)||[]),Ln(this,Gk())}ye.prototype={constructor:ye,_isOutsideThisEl:function(e){!this.el.contains(e)&&e!==this.el&&(Lo=null)},_getDirection:function(e,t){return typeof this.options.direction=="function"?this.options.direction.call(this,e,t,ne):this.options.direction},_onTapStart:function(e){if(e.cancelable){var t=this,n=this.el,o=this.options,r=o.preventOnFilter,i=e.type,l=e.touches&&e.touches[0]||e.pointerType&&e.pointerType==="touch"&&e,s=(l||e).target,a=e.target.shadowRoot&&(e.path&&e.path[0]||e.composedPath&&e.composedPath()[0])||s,c=o.filter;if(a1(n),!ne&&!(/mousedown|pointerdown/.test(i)&&e.button!==0||o.disabled)&&!a.isContentEditable&&!(!this.nativeDraggable&&Rr&&s&&s.tagName.toUpperCase()==="SELECT")&&(s=sn(s,o.draggable,n,!1),!(s&&s.animated)&&Mi!==s)){if(Bo=Jt(s),Mr=Jt(s,o.draggable),typeof c=="function"){if(c.call(this,e,s,this)){xt({sortable:t,rootEl:a,name:"filter",targetEl:s,toEl:n,fromEl:n}),Nt("filter",t,{evt:e}),r&&e.cancelable&&e.preventDefault();return}}else if(c&&(c=c.split(",").some(function(f){if(f=sn(a,f.trim(),n,!1),f)return xt({sortable:t,rootEl:f,name:"filter",targetEl:s,fromEl:n,toEl:n}),Nt("filter",t,{evt:e}),!0}),c)){r&&e.cancelable&&e.preventDefault();return}o.handle&&!sn(a,o.handle,n,!1)||this._prepareDragStart(e,l,s)}}},_prepareDragStart:function(e,t,n){var o=this,r=o.el,i=o.options,l=r.ownerDocument,s;if(n&&!ne&&n.parentNode===r){var a=lt(n);if(Ke=r,ne=n,Qe=ne.parentNode,bo=ne.nextSibling,Mi=n,ki=i.group,ye.dragged=ne,vo={target:ne,clientX:(t||e).clientX,clientY:(t||e).clientY},xf=vo.clientX-a.left,Lf=vo.clientY-a.top,this._lastX=(t||e).clientX,this._lastY=(t||e).clientY,ne.style["will-change"]="all",s=function(){if(Nt("delayEnded",o,{evt:e}),ye.eventCanceled){o._onDrop();return}o._disableDelayedDragEvents(),!Of&&o.nativeDraggable&&(ne.draggable=!0),o._triggerDragStart(e,t),xt({sortable:o,name:"choose",originalEvent:e}),Ut(ne,i.chosenClass,!0)},i.ignore.split(",").forEach(function(c){mm(ne,c.trim(),fa)}),Ne(l,"dragover",_o),Ne(l,"mousemove",_o),Ne(l,"touchmove",_o),Ne(l,"mouseup",o._onDrop),Ne(l,"touchend",o._onDrop),Ne(l,"touchcancel",o._onDrop),Of&&this.nativeDraggable&&(this.options.touchStartThreshold=4,ne.draggable=!0),Nt("delayStart",this,{evt:e}),i.delay&&(!i.delayOnTouchOnly||t)&&(!this.nativeDraggable||!(li||Rn))){if(ye.eventCanceled){this._onDrop();return}Ne(l,"mouseup",o._disableDelayedDrag),Ne(l,"touchend",o._disableDelayedDrag),Ne(l,"touchcancel",o._disableDelayedDrag),Ne(l,"mousemove",o._delayedDragTouchMoveHandler),Ne(l,"touchmove",o._delayedDragTouchMoveHandler),i.supportPointer&&Ne(l,"pointermove",o._delayedDragTouchMoveHandler),o._dragStartTimer=setTimeout(s,i.delay)}else s()}},_delayedDragTouchMoveHandler:function(e){var t=e.touches?e.touches[0]:e;Math.max(Math.abs(t.clientX-this._lastX),Math.abs(t.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){ne&&fa(ne),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var e=this.el.ownerDocument;Ie(e,"mouseup",this._disableDelayedDrag),Ie(e,"touchend",this._disableDelayedDrag),Ie(e,"touchcancel",this._disableDelayedDrag),Ie(e,"mousemove",this._delayedDragTouchMoveHandler),Ie(e,"touchmove",this._delayedDragTouchMoveHandler),Ie(e,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(e,t){t=t||e.pointerType=="touch"&&e,!this.nativeDraggable||t?this.options.supportPointer?Ne(document,"pointermove",this._onTouchMove):t?Ne(document,"touchmove",this._onTouchMove):Ne(document,"mousemove",this._onTouchMove):(Ne(ne,"dragend",this),Ne(Ke,"dragstart",this._onDragStart));try{document.selection?$i(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch{}},_dragStarted:function(e,t){if(Mo=!1,Ke&&ne){Nt("dragStarted",this,{evt:t}),this.nativeDraggable&&Ne(document,"dragover",Zk);var n=this.options;!e&&Ut(ne,n.dragClass,!1),Ut(ne,n.ghostClass,!0),ye.active=this,e&&this._appendGhost(),xt({sortable:this,name:"start",originalEvent:t})}else this._nulling()},_emulateDragOver:function(){if(an){this._lastX=an.clientX,this._lastY=an.clientY,Cm();for(var e=document.elementFromPoint(an.clientX,an.clientY),t=e;e&&e.shadowRoot&&(e=e.shadowRoot.elementFromPoint(an.clientX,an.clientY),e!==t);)t=e;if(ne.parentNode[Gt]._isOutsideThisEl(e),t)do{if(t[Gt]){var n=void 0;if(n=t[Gt]._onDragOver({clientX:an.clientX,clientY:an.clientY,target:e,rootEl:t}),n&&!this.options.dragoverBubble)break}e=t}while(t=t.parentNode);Tm()}},_onTouchMove:function(e){if(vo){var t=this.options,n=t.fallbackTolerance,o=t.fallbackOffset,r=e.touches?e.touches[0]:e,i=Te&&Xo(Te,!0),l=Te&&i&&i.a,s=Te&&i&&i.d,a=Pi&&Tt&&If(Tt),c=(r.clientX-vo.clientX+o.x)/(l||1)+(a?a[0]-ua[0]:0)/(l||1),f=(r.clientY-vo.clientY+o.y)/(s||1)+(a?a[1]-ua[1]:0)/(s||1);if(!ye.active&&!Mo){if(n&&Math.max(Math.abs(r.clientX-this._lastX),Math.abs(r.clientY-this._lastY))<n)return;this._onDragStart(e,!0)}if(Te){i?(i.e+=c-(sa||0),i.f+=f-(ca||0)):i={a:1,b:0,c:0,d:1,e:c,f};var u="matrix(".concat(i.a,",").concat(i.b,",").concat(i.c,",").concat(i.d,",").concat(i.e,",").concat(i.f,")");be(Te,"webkitTransform",u),be(Te,"mozTransform",u),be(Te,"msTransform",u),be(Te,"transform",u),sa=c,ca=f,an=r}e.cancelable&&e.preventDefault()}},_appendGhost:function(){if(!Te){var e=this.options.fallbackOnBody?document.body:Ke,t=lt(ne,!0,Pi,!0,e),n=this.options;if(Pi){for(Tt=e;be(Tt,"position")==="static"&&be(Tt,"transform")==="none"&&Tt!==document;)Tt=Tt.parentNode;Tt!==document.body&&Tt!==document.documentElement?(Tt===document&&(Tt=bn()),t.top+=Tt.scrollTop,t.left+=Tt.scrollLeft):Tt=bn(),ua=If(Tt)}Te=ne.cloneNode(!0),Ut(Te,n.ghostClass,!1),Ut(Te,n.fallbackClass,!0),Ut(Te,n.dragClass,!0),be(Te,"transition",""),be(Te,"transform",""),be(Te,"box-sizing","border-box"),be(Te,"margin",0),be(Te,"top",t.top),be(Te,"left",t.left),be(Te,"width",t.width),be(Te,"height",t.height),be(Te,"opacity","0.8"),be(Te,"position",Pi?"absolute":"fixed"),be(Te,"zIndex","100000"),be(Te,"pointerEvents","none"),ye.ghost=Te,e.appendChild(Te),be(Te,"transform-origin",xf/parseInt(Te.style.width)*100+"% "+Lf/parseInt(Te.style.height)*100+"%")}},_onDragStart:function(e,t){var n=this,o=e.dataTransfer,r=n.options;if(Nt("dragStart",this,{evt:e}),ye.eventCanceled){this._onDrop();return}Nt("setupClone",this),ye.eventCanceled||(Xe=_m(ne),Xe.removeAttribute("id"),Xe.draggable=!1,Xe.style["will-change"]="",this._hideClone(),Ut(Xe,this.options.chosenClass,!1),ye.clone=Xe),n.cloneId=$i(function(){Nt("clone",n),!ye.eventCanceled&&(n.options.removeCloneOnHide||Ke.insertBefore(Xe,ne),n._hideClone(),xt({sortable:n,name:"clone"}))}),!t&&Ut(ne,r.dragClass,!0),t?(al=!0,n._loopId=setInterval(n._emulateDragOver,50)):(Ie(document,"mouseup",n._onDrop),Ie(document,"touchend",n._onDrop),Ie(document,"touchcancel",n._onDrop),o&&(o.effectAllowed="move",r.setData&&r.setData.call(n,o,ne)),Ne(document,"drop",n),be(ne,"transform","translateZ(0)")),Mo=!0,n._dragStartId=$i(n._dragStarted.bind(n,t,e)),Ne(document,"selectstart",n),Sr=!0,Rr&&be(document.body,"user-select","none")},_onDragOver:function(e){var t=this.el,n=e.target,o,r,i,l=this.options,s=l.group,a=ye.active,c=ki===s,f=l.sort,u=gt||a,d,p=this,h=!1;if(za)return;function g(X,W){Nt(X,p,yn({evt:e,isOwner:c,axis:d?"vertical":"horizontal",revert:i,dragRect:o,targetRect:r,canSort:f,fromSortable:u,target:n,completed:A,onMove:function(ie,ce){return Ai(Ke,t,ne,o,ie,lt(ie),e,ce)},changed:V},W))}function O(){g("dragOverAnimationCapture"),p.captureAnimationState(),p!==u&&u.captureAnimationState()}function A(X){return g("dragOverCompleted",{insertion:X}),X&&(c?a._hideClone():a._showClone(p),p!==u&&(Ut(ne,gt?gt.options.ghostClass:a.options.ghostClass,!1),Ut(ne,l.ghostClass,!0)),gt!==p&&p!==ye.active?gt=p:p===ye.active&&gt&&(gt=null),u===p&&(p._ignoreWhileAnimating=n),p.animateAll(function(){g("dragOverAnimationComplete"),p._ignoreWhileAnimating=null}),p!==u&&(u.animateAll(),u._ignoreWhileAnimating=null)),(n===ne&&!ne.animated||n===t&&!n.animated)&&(Lo=null),!l.dragoverBubble&&!e.rootEl&&n!==document&&(ne.parentNode[Gt]._isOutsideThisEl(e.target),!X&&_o(e)),!l.dragoverBubble&&e.stopPropagation&&e.stopPropagation(),h=!0}function V(){jt=Jt(ne),Kn=Jt(ne,l.draggable),xt({sortable:p,name:"change",toEl:t,newIndex:jt,newDraggableIndex:Kn,originalEvent:e})}if(e.preventDefault!==void 0&&e.cancelable&&e.preventDefault(),n=sn(n,l.draggable,t,!0),g("dragOver"),ye.eventCanceled)return h;if(ne.contains(e.target)||n.animated&&n.animatingX&&n.animatingY||p._ignoreWhileAnimating===n)return A(!1);if(al=!1,a&&!l.disabled&&(c?f||(i=Qe!==Ke):gt===this||(this.lastPutMode=ki.checkPull(this,a,ne,e))&&s.checkPut(this,a,ne,e))){if(d=this._getDirection(e,n)==="vertical",o=lt(ne),g("dragOverValid"),ye.eventCanceled)return h;if(i)return Qe=Ke,O(),this._hideClone(),g("revert"),ye.eventCanceled||(bo?Ke.insertBefore(ne,bo):Ke.appendChild(ne)),A(!0);var v=Gs(t,l.draggable);if(!v||o1(e,d,this)&&!v.animated){if(v===ne)return A(!1);if(v&&t===e.target&&(n=v),n&&(r=lt(n)),Ai(Ke,t,ne,o,n,r,e,!!n)!==!1)return O(),v&&v.nextSibling?t.insertBefore(ne,v.nextSibling):t.appendChild(ne),Qe=t,V(),A(!0)}else if(v&&n1(e,d,this)){var b=ar(t,0,l,!0);if(b===ne)return A(!1);if(n=b,r=lt(n),Ai(Ke,t,ne,o,n,r,e,!1)!==!1)return O(),t.insertBefore(ne,b),Qe=t,V(),A(!0)}else if(n.parentNode===t){r=lt(n);var w=0,S,N=ne.parentNode!==t,M=!Jk(ne.animated&&ne.toRect||o,n.animated&&n.toRect||r,d),x=d?"top":"left",L=Af(n,"top","top")||Af(ne,"top","top"),D=L?L.scrollTop:void 0;Lo!==n&&(S=r[x],$r=!1,Oi=!M&&l.invertSwap||N),w=r1(e,n,r,d,M?1:l.swapThreshold,l.invertedSwapThreshold==null?l.swapThreshold:l.invertedSwapThreshold,Oi,Lo===n);var I;if(w!==0){var j=Jt(ne);do j-=w,I=Qe.children[j];while(I&&(be(I,"display")==="none"||I===Te))}if(w===0||I===n)return A(!1);Lo=n,Vr=w;var Q=n.nextElementSibling,G=!1;G=w===1;var U=Ai(Ke,t,ne,o,n,r,e,G);if(U!==!1)return(U===1||U===-1)&&(G=U===1),za=!0,setTimeout(t1,30),O(),G&&!Q?t.appendChild(ne):n.parentNode.insertBefore(ne,G?Q:n),L&&vm(L,0,D-L.scrollTop),Qe=ne.parentNode,S!==void 0&&!Oi&&(Vi=Math.abs(S-lt(n)[x])),V(),A(!0)}if(t.contains(ne))return A(!1)}return!1},_ignoreWhileAnimating:null,_offMoveEvents:function(){Ie(document,"mousemove",this._onTouchMove),Ie(document,"touchmove",this._onTouchMove),Ie(document,"pointermove",this._onTouchMove),Ie(document,"dragover",_o),Ie(document,"mousemove",_o),Ie(document,"touchmove",_o)},_offUpEvents:function(){var e=this.el.ownerDocument;Ie(e,"mouseup",this._onDrop),Ie(e,"touchend",this._onDrop),Ie(e,"pointerup",this._onDrop),Ie(e,"touchcancel",this._onDrop),Ie(document,"selectstart",this)},_onDrop:function(e){var t=this.el,n=this.options;if(jt=Jt(ne),Kn=Jt(ne,n.draggable),Nt("drop",this,{evt:e}),Qe=ne&&ne.parentNode,jt=Jt(ne),Kn=Jt(ne,n.draggable),ye.eventCanceled){this._nulling();return}Mo=!1,Oi=!1,$r=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),Xa(this.cloneId),Xa(this._dragStartId),this.nativeDraggable&&(Ie(document,"drop",this),Ie(t,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),Rr&&be(document.body,"user-select",""),be(ne,"transform",""),e&&(Sr&&(e.cancelable&&e.preventDefault(),!n.dropBubble&&e.stopPropagation()),Te&&Te.parentNode&&Te.parentNode.removeChild(Te),(Ke===Qe||gt&&gt.lastPutMode!=="clone")&&Xe&&Xe.parentNode&&Xe.parentNode.removeChild(Xe),ne&&(this.nativeDraggable&&Ie(ne,"dragend",this),fa(ne),ne.style["will-change"]="",Sr&&!Mo&&Ut(ne,gt?gt.options.ghostClass:this.options.ghostClass,!1),Ut(ne,this.options.chosenClass,!1),xt({sortable:this,name:"unchoose",toEl:Qe,newIndex:null,newDraggableIndex:null,originalEvent:e}),Ke!==Qe?(jt>=0&&(xt({rootEl:Qe,name:"add",toEl:Qe,fromEl:Ke,originalEvent:e}),xt({sortable:this,name:"remove",toEl:Qe,originalEvent:e}),xt({rootEl:Qe,name:"sort",toEl:Qe,fromEl:Ke,originalEvent:e}),xt({sortable:this,name:"sort",toEl:Qe,originalEvent:e})),gt&&gt.save()):jt!==Bo&&jt>=0&&(xt({sortable:this,name:"update",toEl:Qe,originalEvent:e}),xt({sortable:this,name:"sort",toEl:Qe,originalEvent:e})),ye.active&&((jt==null||jt===-1)&&(jt=Bo,Kn=Mr),xt({sortable:this,name:"end",toEl:Qe,originalEvent:e}),this.save()))),this._nulling()},_nulling:function(){Nt("nulling",this),Ke=ne=Qe=Te=bo=Xe=Mi=zn=vo=an=Sr=jt=Kn=Bo=Mr=Lo=Vr=gt=ki=ye.dragged=ye.ghost=ye.clone=ye.active=null,cl.forEach(function(e){e.checked=!0}),cl.length=sa=ca=0},handleEvent:function(e){switch(e.type){case"drop":case"dragend":this._onDrop(e);break;case"dragenter":case"dragover":ne&&(this._onDragOver(e),e1(e));break;case"selectstart":e.preventDefault();break}},toArray:function(){for(var e=[],t,n=this.el.children,o=0,r=n.length,i=this.options;o<r;o++)t=n[o],sn(t,i.draggable,this.el,!1)&&e.push(t.getAttribute(i.dataIdAttr)||l1(t));return e},sort:function(e,t){var n={},o=this.el;this.toArray().forEach(function(r,i){var l=o.children[i];sn(l,this.options.draggable,o,!1)&&(n[r]=l)},this),t&&this.captureAnimationState(),e.forEach(function(r){n[r]&&(o.removeChild(n[r]),o.appendChild(n[r]))}),t&&this.animateAll()},save:function(){var e=this.options.store;e&&e.set&&e.set(this)},closest:function(e,t){return sn(e,t||this.options.draggable,this.el,!1)},option:function(e,t){var n=this.options;if(t===void 0)return n[e];var o=ai.modifyOption(this,e,t);typeof o<"u"?n[e]=o:n[e]=t,e==="group"&&Sm(n)},destroy:function(){Nt("destroy",this);var e=this.el;e[Gt]=null,Ie(e,"mousedown",this._onTapStart),Ie(e,"touchstart",this._onTapStart),Ie(e,"pointerdown",this._onTapStart),this.nativeDraggable&&(Ie(e,"dragover",this),Ie(e,"dragenter",this)),Array.prototype.forEach.call(e.querySelectorAll("[draggable]"),function(t){t.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),sl.splice(sl.indexOf(this.el),1),this.el=e=null},_hideClone:function(){if(!zn){if(Nt("hideClone",this),ye.eventCanceled)return;be(Xe,"display","none"),this.options.removeCloneOnHide&&Xe.parentNode&&Xe.parentNode.removeChild(Xe),zn=!0}},_showClone:function(e){if(e.lastPutMode!=="clone"){this._hideClone();return}if(zn){if(Nt("showClone",this),ye.eventCanceled)return;ne.parentNode==Ke&&!this.options.group.revertClone?Ke.insertBefore(Xe,ne):bo?Ke.insertBefore(Xe,bo):Ke.appendChild(Xe),this.options.group.revertClone&&this.animate(ne,Xe),be(Xe,"display",""),zn=!1}}};function e1(e){e.dataTransfer&&(e.dataTransfer.dropEffect="move"),e.cancelable&&e.preventDefault()}function Ai(e,t,n,o,r,i,l,s){var a,c=e[Gt],f=c.options.onMove,u;return window.CustomEvent&&!Rn&&!li?a=new CustomEvent("move",{bubbles:!0,cancelable:!0}):(a=document.createEvent("Event"),a.initEvent("move",!0,!0)),a.to=t,a.from=e,a.dragged=n,a.draggedRect=o,a.related=r||t,a.relatedRect=i||lt(t),a.willInsertAfter=s,a.originalEvent=l,e.dispatchEvent(a),f&&(u=f.call(c,a,l)),u}function fa(e){e.draggable=!1}function t1(){za=!1}function n1(e,t,n){var o=lt(ar(n.el,0,n.options,!0)),r=bm(n.el,n.options,Te),i=10;return t?e.clientX<r.left-i||e.clientY<o.top&&e.clientX<o.right:e.clientY<r.top-i||e.clientY<o.bottom&&e.clientX<o.left}function o1(e,t,n){var o=lt(Gs(n.el,n.options.draggable)),r=bm(n.el,n.options,Te),i=10;return t?e.clientX>r.right+i||e.clientY>o.bottom&&e.clientX>o.left:e.clientY>r.bottom+i||e.clientX>o.right&&e.clientY>o.top}function r1(e,t,n,o,r,i,l,s){var a=o?e.clientY:e.clientX,c=o?n.height:n.width,f=o?n.top:n.left,u=o?n.bottom:n.right,d=!1;if(!l){if(s&&Vi<c*r){if(!$r&&(Vr===1?a>f+c*i/2:a<u-c*i/2)&&($r=!0),$r)d=!0;else if(Vr===1?a<f+Vi:a>u-Vi)return-Vr}else if(a>f+c*(1-r)/2&&a<u-c*(1-r)/2)return i1(t)}return d=d||l,d&&(a<f+c*i/2||a>u-c*i/2)?a>f+c/2?1:-1:0}function i1(e){return Jt(ne)<Jt(e)?1:-1}function l1(e){for(var t=e.tagName+e.className+e.src+e.href+e.textContent,n=t.length,o=0;n--;)o+=t.charCodeAt(n);return o.toString(36)}function a1(e){cl.length=0;for(var t=e.getElementsByTagName("input"),n=t.length;n--;){var o=t[n];o.checked&&cl.push(o)}}function $i(e){return setTimeout(e,0)}function Xa(e){return clearTimeout(e)}Dl&&Ne(document,"touchmove",function(e){(ye.active||Mo)&&e.cancelable&&e.preventDefault()});ye.utils={on:Ne,off:Ie,css:be,find:mm,is:function(e,t){return!!sn(e,t,e,!1)},extend:Hk,throttle:gm,closest:sn,toggleClass:Ut,clone:_m,index:Jt,nextTick:$i,cancelNextTick:Xa,detectDirection:Em,getChild:ar};ye.get=function(e){return e[Gt]};ye.mount=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];t[0].constructor===Array&&(t=t[0]),t.forEach(function(o){if(!o.prototype||!o.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(o));o.utils&&(ye.utils=yn(yn({},ye.utils),o.utils)),ai.mount(o)})};ye.create=function(e,t){return new ye(e,t)};ye.version=Bk;var it=[],Cr,qa,Ja=!1,da,pa,ul,Tr;function s1(){function e(){this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0};for(var t in this)t.charAt(0)==="_"&&typeof this[t]=="function"&&(this[t]=this[t].bind(this))}return e.prototype={dragStarted:function(t){var n=t.originalEvent;this.sortable.nativeDraggable?Ne(document,"dragover",this._handleAutoScroll):this.options.supportPointer?Ne(document,"pointermove",this._handleFallbackAutoScroll):n.touches?Ne(document,"touchmove",this._handleFallbackAutoScroll):Ne(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(t){var n=t.originalEvent;!this.options.dragOverBubble&&!n.rootEl&&this._handleAutoScroll(n)},drop:function(){this.sortable.nativeDraggable?Ie(document,"dragover",this._handleAutoScroll):(Ie(document,"pointermove",this._handleFallbackAutoScroll),Ie(document,"touchmove",this._handleFallbackAutoScroll),Ie(document,"mousemove",this._handleFallbackAutoScroll)),Rf(),Fi(),Wk()},nulling:function(){ul=qa=Cr=Ja=Tr=da=pa=null,it.length=0},_handleFallbackAutoScroll:function(t){this._handleAutoScroll(t,!0)},_handleAutoScroll:function(t,n){var o=this,r=(t.touches?t.touches[0]:t).clientX,i=(t.touches?t.touches[0]:t).clientY,l=document.elementFromPoint(r,i);if(ul=t,n||this.options.forceAutoScrollFallback||li||Rn||Rr){ha(t,this.options,l,n);var s=Xn(l,!0);Ja&&(!Tr||r!==da||i!==pa)&&(Tr&&Rf(),Tr=setInterval(function(){var a=Xn(document.elementFromPoint(r,i),!0);a!==s&&(s=a,Fi()),ha(t,o.options,a,n)},10),da=r,pa=i)}else{if(!this.options.bubbleScroll||Xn(l,!0)===bn()){Fi();return}ha(t,this.options,Xn(l,!1),!1)}}},Ln(e,{pluginName:"scroll",initializeByDefault:!0})}function Fi(){it.forEach(function(e){clearInterval(e.pid)}),it=[]}function Rf(){clearInterval(Tr)}var ha=gm(function(e,t,n,o){if(t.scroll){var r=(e.touches?e.touches[0]:e).clientX,i=(e.touches?e.touches[0]:e).clientY,l=t.scrollSensitivity,s=t.scrollSpeed,a=bn(),c=!1,f;qa!==n&&(qa=n,Fi(),Cr=t.scroll,f=t.scrollFn,Cr===!0&&(Cr=Xn(n,!0)));var u=0,d=Cr;do{var p=d,h=lt(p),g=h.top,O=h.bottom,A=h.left,V=h.right,v=h.width,b=h.height,w=void 0,S=void 0,N=p.scrollWidth,M=p.scrollHeight,x=be(p),L=p.scrollLeft,D=p.scrollTop;p===a?(w=v<N&&(x.overflowX==="auto"||x.overflowX==="scroll"||x.overflowX==="visible"),S=b<M&&(x.overflowY==="auto"||x.overflowY==="scroll"||x.overflowY==="visible")):(w=v<N&&(x.overflowX==="auto"||x.overflowX==="scroll"),S=b<M&&(x.overflowY==="auto"||x.overflowY==="scroll"));var I=w&&(Math.abs(V-r)<=l&&L+v<N)-(Math.abs(A-r)<=l&&!!L),j=S&&(Math.abs(O-i)<=l&&D+b<M)-(Math.abs(g-i)<=l&&!!D);if(!it[u])for(var Q=0;Q<=u;Q++)it[Q]||(it[Q]={});(it[u].vx!=I||it[u].vy!=j||it[u].el!==p)&&(it[u].el=p,it[u].vx=I,it[u].vy=j,clearInterval(it[u].pid),(I!=0||j!=0)&&(c=!0,it[u].pid=setInterval((function(){o&&this.layer===0&&ye.active._onTouchMove(ul);var G=it[this.layer].vy?it[this.layer].vy*s:0,U=it[this.layer].vx?it[this.layer].vx*s:0;typeof f=="function"&&f.call(ye.dragged.parentNode[Gt],U,G,e,ul,it[this.layer].el)!=="continue"||vm(it[this.layer].el,U,G)}).bind({layer:u}),24))),u++}while(t.bubbleScroll&&d!==a&&(d=Xn(d,!1)));Ja=c}},30),wm=function(e){var t=e.originalEvent,n=e.putSortable,o=e.dragEl,r=e.activeSortable,i=e.dispatchSortableEvent,l=e.hideGhostForTarget,s=e.unhideGhostForTarget;if(t){var a=n||r;l();var c=t.changedTouches&&t.changedTouches.length?t.changedTouches[0]:t,f=document.elementFromPoint(c.clientX,c.clientY);s(),a&&!a.el.contains(f)&&(i("spill"),this.onSpill({dragEl:o,putSortable:n}))}};function Ks(){}Ks.prototype={startIndex:null,dragStart:function(e){var t=e.oldDraggableIndex;this.startIndex=t},onSpill:function(e){var t=e.dragEl,n=e.putSortable;this.sortable.captureAnimationState(),n&&n.captureAnimationState();var o=ar(this.sortable.el,this.startIndex,this.options);o?this.sortable.el.insertBefore(t,o):this.sortable.el.appendChild(t),this.sortable.animateAll(),n&&n.animateAll()},drop:wm};Ln(Ks,{pluginName:"revertOnSpill"});function Ys(){}Ys.prototype={onSpill:function(e){var t=e.dragEl,n=e.putSortable,o=n||this.sortable;o.captureAnimationState(),t.parentNode&&t.parentNode.removeChild(t),o.animateAll()},drop:wm};Ln(Ys,{pluginName:"removeOnSpill"});ye.mount(new s1);ye.mount(Ys,Ks);function c1(e){return e==null?e:JSON.parse(JSON.stringify(e))}function u1(e){Et()&&wo(e)}function f1(e){Et()?Pt(e):bt(e)}let km=null,Om=null;function Nf(e=null,t=null){km=e,Om=t}function d1(){return{data:km,clonedData:Om}}const Mf=Symbol("cloneElement");function p1(...e){var t,n;const o=(t=Et())==null?void 0:t.proxy;let r=null;const i=e[0];let[,l,s]=e;Array.isArray(E(l))||(s=l,l=null);let a=null;const{immediate:c=!0,clone:f=c1,customUpdate:u}=(n=E(s))!=null?n:{};function d(M){var x;const{from:L,oldIndex:D,item:I}=M;r=Array.from(L.childNodes);const j=E((x=E(l))==null?void 0:x[D]),Q=f(j);Nf(j,Q),I[Mf]=Q}function p(M){const x=M.item[Mf];if(!Ak(x)){if(ia(M.item),Ge(l)){const L=[...E(l)];l.value=Cf(L,M.newDraggableIndex,x);return}Cf(E(l),M.newDraggableIndex,x)}}function h(M){const{from:x,item:L,oldIndex:D,oldDraggableIndex:I,pullMode:j,clone:Q}=M;if(Tf(x,L,D),j==="clone"){ia(Q);return}if(Ge(l)){const G=[...E(l)];l.value=Sf(G,I);return}Sf(E(l),I)}function g(M){if(u){u(M);return}const{from:x,item:L,oldIndex:D,newIndex:I}=M;if(ia(L),Tf(x,L,D),Ge(l)){const j=[...E(l)];l.value=Ef(j,D,I);return}Ef(E(l),D,I)}function O(M){const{newIndex:x,oldIndex:L,from:D,to:I}=M;let j=null;const Q=x===L&&D===I;try{if(Q){let G=null;r==null||r.some((U,X)=>{if(G&&(r==null?void 0:r.length)!==I.childNodes.length)return D.insertBefore(G,U.nextSibling),!0;const W=I.childNodes[X];G=I==null?void 0:I.replaceChild(U,W)})}}catch(G){j=G}finally{r=null}bt(()=>{if(Nf(),j)throw j})}const A={onUpdate:g,onStart:d,onAdd:p,onRemove:h,onEnd:O};function V(M){const x=E(i);return M||(M=Ik(x)?xk(x,o==null?void 0:o.$el):x),M&&!Rk(M)&&(M=M.$el),M||kk("Root element not found"),M}function v(){var M;const x=(M=E(s))!=null?M:{},L=um(x,["immediate","clone"]);return wf(L,(D,I)=>{Nk(D)&&(L[D]=(j,...Q)=>{const G=d1();return Mk(j,G),I(j,...Q)})}),Dk(l===null?{}:A,L)}const b=M=>{M=V(M),a&&w.destroy(),a=new ye(M,v())};De(()=>s,()=>{a&&wf(v(),(M,x)=>{a==null||a.option(M,x)})},{deep:!0});const w={option:(M,x)=>a==null?void 0:a.option(M,x),destroy:()=>{a==null||a.destroy(),a=null},save:()=>a==null?void 0:a.save(),toArray:()=>a==null?void 0:a.toArray(),closest:(...M)=>a==null?void 0:a.closest(...M)},S=()=>w==null?void 0:w.option("disabled",!0),N=()=>w==null?void 0:w.option("disabled",!1);return f1(()=>{c&&b()}),u1(w.destroy),Fo({start:b,pause:S,resume:N},w)}const Qa=["update","start","add","remove","choose","unchoose","end","sort","filter","clone","move","change"],h1=["clone","animation","ghostClass","group","sort","disabled","store","handle","draggable","swapThreshold","invertSwap","invertedSwapThreshold","removeCloneOnHide","direction","chosenClass","dragClass","ignore","filter","preventOnFilter","easing","setData","dropBubble","dragoverBubble","dataIdAttr","delay","delayOnTouchOnly","touchStartThreshold","forceFallback","fallbackClass","fallbackOnBody","fallbackTolerance","fallbackOffset","supportPointer","emptyInsertThreshold","scroll","forceAutoScrollFallback","scrollSensitivity","scrollSpeed","bubbleScroll","modelValue","tag","target","customUpdate",...Qa.map(e=>`on${e.replace(/^\S/,t=>t.toUpperCase())}`)],m1=ue({name:"VueDraggable",model:{prop:"modelValue",event:"update:modelValue"},props:h1,emits:["update:modelValue",...Qa],setup(e,{slots:t,emit:n,expose:o,attrs:r}){const i=Qa.reduce((f,u)=>{const d=`on${u.replace(/^\S/,p=>p.toUpperCase())}`;return f[d]=(...p)=>n(u,...p),f},{}),l=he(()=>{const f=e_(e),u=um(f,["modelValue"]),d=Object.entries(u).reduce((p,[h,g])=>{const O=E(g);return O!==void 0&&(p[h]=O),p},{});return Fo(Fo({},i),Pk(Fo(Fo({},r),d)))}),s=he({get:()=>e.modelValue,set:f=>n("update:modelValue",f)}),a=fe(),c=un(p1(e.target||a,s,l));return o(c),()=>{var f;return ur(e.tag||"div",{ref:a},(f=t==null?void 0:t.default)==null?void 0:f.call(t,c))}}}),Pm=ue({__name:"index",setup(e){const t=St();function n(o){var r;(r=t.data.value.iptvChannelFavoriteList)==null||r.value.splice(o,1)}return(o,r)=>{const i=Kt,l=tt,s=ot,a=rt;return E(t).data.value.iptvChannelFavoriteList?(le(),_e(a,{key:0,inset:""},{default:$(()=>[m(E(m1),{modelValue:E(t).data.value.iptvChannelFavoriteList.value,"onUpdate:modelValue":r[0]||(r[0]=c=>E(t).data.value.iptvChannelFavoriteList.value=c),animation:150},{default:$(()=>{var c;return[(le(!0),kt(Be,null,Sl((c=E(t).data.value.iptvChannelFavoriteList)==null?void 0:c.value,(f,u)=>(le(),_e(l,{key:u,title:f.channel.name,label:`${f.iptvSourceName} / ${f.groupName}`,center:""},{"right-icon":$(()=>[m(i,{name:"cross",onClick:d=>n(u)},null,8,["onClick"])]),_:2},1032,["title","label"]))),128))]}),_:1},8,["modelValue"]),m(l,null,{default:$(()=>[m(s,{type:"primary",block:"",onClick:E(t).update},{default:$(()=>[Oe(" Update ")]),_:1},8,["onClick"])]),_:1})]),_:1})):at("",!0)}}});typeof we=="function"&&we(Pm);const g1=Object.freeze(Object.defineProperty({__proto__:null,default:Pm},Symbol.toStringTag,{value:"Module"})),Am=ue({__name:"index",setup(e){const t=co(),n=St(),o=fe({type:"url",name:"",url:"",sourceType:0,format:"m3u_plus"});async function r(){var i;if(o.value.type==="file")o.value.sourceType=1;else if(o.value.type==="content"){o.value.sourceType=1;const l=await nn.writeFileContentWithDir("file",`iptv_source_local_${Date.now()}.txt`,o.value.content);o.value.url=l}else o.value.type==="xtream"&&(o.value.sourceType=2);n.data.value.iptvSourceList={value:[...((i=n.data.value.iptvSourceList)==null?void 0:i.value)??[],o.value]},await n.update(),t.back()}return(i,l)=>{const s=kw,a=vw,c=tt,f=on,u=ot,d=rt;return E(o)?(le(),_e(d,{key:0,inset:""},{default:$(()=>[m(c,{title:"Type",center:""},{value:$(()=>[m(a,{modelValue:E(o).type,"onUpdate:modelValue":l[0]||(l[0]=p=>E(o).type=p),direction:"horizontal",class:"justify-end"},{default:$(()=>[m(s,{name:"url"},{default:$(()=>[Oe(" Remote ")]),_:1}),m(s,{name:"file"},{default:$(()=>[Oe(" File ")]),_:1}),m(s,{name:"content"},{default:$(()=>[Oe(" Static ")]),_:1}),m(s,{name:"xtream"},{default:$(()=>[Oe(" XTREAM ")]),_:1})]),_:1},8,["modelValue"])]),_:1}),m(c,{title:"Name",center:""},{value:$(()=>[m(f,{modelValue:E(o).name,"onUpdate:modelValue":l[1]||(l[1]=p=>E(o).name=p),"input-align":"right"},null,8,["modelValue"])]),_:1}),E(o).type==="url"||E(o).type==="xtream"?(le(),_e(c,{key:0,title:"URL",center:""},{value:$(()=>[m(f,{modelValue:E(o).url,"onUpdate:modelValue":l[2]||(l[2]=p=>E(o).url=p),"input-align":"right"},null,8,["modelValue"])]),_:1})):at("",!0),E(o).type==="url"||E(o).type==="xtream"?(le(),_e(c,{key:1,title:"UA"},{value:$(()=>[m(f,{modelValue:E(o).httpUserAgent,"onUpdate:modelValue":l[3]||(l[3]=p=>E(o).httpUserAgent=p),type:"textarea",rows:"5"},null,8,["modelValue"])]),_:1})):E(o).type==="file"?(le(),_e(c,{key:2,title:"File Path",center:""},{value:$(()=>[m(f,{modelValue:E(o).url,"onUpdate:modelValue":l[4]||(l[4]=p=>E(o).url=p),"input-align":"right"},null,8,["modelValue"])]),_:1})):E(o).type==="content"?(le(),_e(c,{key:3,title:"Content"},{value:$(()=>[m(f,{modelValue:E(o).content,"onUpdate:modelValue":l[5]||(l[5]=p=>E(o).content=p),type:"textarea",rows:"5"},null,8,["modelValue"])]),_:1})):at("",!0),E(o).type==="xtream"?(le(),_e(c,{key:4,title:"Username"},{value:$(()=>[m(f,{modelValue:E(o).userName,"onUpdate:modelValue":l[6]||(l[6]=p=>E(o).userName=p),type:"textarea",rows:"5"},null,8,["modelValue"])]),_:1})):at("",!0),E(o).type==="xtream"?(le(),_e(c,{key:5,title:"Password"},{value:$(()=>[m(f,{modelValue:E(o).password,"onUpdate:modelValue":l[7]||(l[7]=p=>E(o).password=p),type:"textarea",rows:"5"},null,8,["modelValue"])]),_:1})):at("",!0),E(o).type==="xtream"?(le(),_e(c,{key:6,title:"Output Type"},{value:$(()=>[m(f,{modelValue:E(o).format,"onUpdate:modelValue":l[8]||(l[8]=p=>E(o).format=p),type:"textarea",rows:"5"},null,8,["modelValue"])]),_:1})):at("",!0),m(c,{title:"Transform JS",center:""},{value:$(()=>[m(f,{modelValue:E(o).transformJs,"onUpdate:modelValue":l[9]||(l[9]=p=>E(o).transformJs=p),type:"textarea",rows:"5"},null,8,["modelValue"])]),_:1}),m(c,null,{default:$(()=>[m(u,{type:"primary",block:"",onClick:r},{default:$(()=>[Oe(" Confirm ")]),_:1})]),_:1})]),_:1})):at("",!0)}}});typeof we=="function"&&we(Am);const v1=At(Am,[["__scopeId","data-v-78af2084"]]),_1=Object.freeze(Object.defineProperty({__proto__:null,default:v1},Symbol.toStringTag,{value:"Module"})),b1={class:"flex gap-4"},Im=ue({__name:"[idx]",setup(e){const t=co(),n=Rs(),o=St(),{idx:r}=n.params,i=he(()=>{var c;return(c=o.data.value.iptvSourceList)==null?void 0:c.value[r]});De(i,async c=>{c&&c.sourceType===1&&(c.content=await nn.getFileContent(c.url))},{immediate:!0});async function l(){var c;((c=i.value)==null?void 0:c.sourceType)===1&&await nn.writeFileContent(i.value.url,i.value.content),await o.update(),t.back()}async function s(){var c;(c=o.data.value.iptvSourceList)==null||c.value.splice(r,1),await o.update(),t.back()}async function a(){o.data.value.iptvSourceCurrent=i.value,await o.update(),t.back()}return(c,f)=>{const u=on,d=tt,p=ot,h=rt;return E(i)?(le(),_e(h,{key:0,inset:""},{default:$(()=>[m(d,{title:"Name",center:""},{value:$(()=>[m(u,{modelValue:E(i).name,"onUpdate:modelValue":f[0]||(f[0]=g=>E(i).name=g),"input-align":"right"},null,8,["modelValue"])]),_:1}),E(i).sourceType===1?(le(),kt(Be,{key:0},[m(d,{title:"File Path",center:""},{value:$(()=>[m(u,{modelValue:E(i).url,"onUpdate:modelValue":f[1]||(f[1]=g=>E(i).url=g),"input-align":"right"},null,8,["modelValue"])]),_:1}),m(d,{title:"File Content"},{value:$(()=>[m(u,{modelValue:E(i).content,"onUpdate:modelValue":f[2]||(f[2]=g=>E(i).content=g),type:"textarea",rows:"5"},null,8,["modelValue"])]),_:1})],64)):(le(),_e(d,{key:1,title:"URL",center:""},{value:$(()=>[m(u,{modelValue:E(i).url,"onUpdate:modelValue":f[3]||(f[3]=g=>E(i).url=g),"input-align":"right"},null,8,["modelValue"])]),_:1})),E(i).sourceType===2?(le(),_e(d,{key:2,title:"Username"},{value:$(()=>[m(u,{modelValue:E(i).userName,"onUpdate:modelValue":f[4]||(f[4]=g=>E(i).userName=g),type:"textarea",rows:"5"},null,8,["modelValue"])]),_:1})):at("",!0),E(i).sourceType===2?(le(),_e(d,{key:3,title:"Password"},{value:$(()=>[m(u,{modelValue:E(i).password,"onUpdate:modelValue":f[5]||(f[5]=g=>E(i).password=g),type:"textarea",rows:"5"},null,8,["modelValue"])]),_:1})):at("",!0),E(i).sourceType===2?(le(),_e(d,{key:4,title:"Output Format"},{value:$(()=>[m(u,{modelValue:E(i).format,"onUpdate:modelValue":f[6]||(f[6]=g=>E(i).format=g),type:"textarea",rows:"5"},null,8,["modelValue"])]),_:1})):at("",!0),E(i).sourceType!==1?(le(),_e(d,{key:5,title:"UA"},{value:$(()=>[m(u,{modelValue:E(i).httpUserAgent,"onUpdate:modelValue":f[7]||(f[7]=g=>E(i).httpUserAgent=g),type:"textarea",rows:"5"},null,8,["modelValue"])]),_:1})):at("",!0),m(d,{title:"Transform JS",center:""},{value:$(()=>[m(u,{modelValue:E(i).transformJs,"onUpdate:modelValue":f[8]||(f[8]=g=>E(i).transformJs=g),type:"textarea",rows:"5"},null,8,["modelValue"])]),_:1}),m(d,null,{default:$(()=>[Pe("div",b1,[m(p,{type:"danger",block:"",onClick:s},{default:$(()=>[Oe(" Delete ")]),_:1}),m(p,{type:"primary",block:"",onClick:l},{default:$(()=>[Oe(" Update ")]),_:1}),m(p,{type:"warning",block:"",onClick:a},{default:$(()=>[Oe(" Switch ")]),_:1})])]),_:1})]),_:1})):at("",!0)}}});typeof we=="function"&&we(Im);const y1=At(Im,[["__scopeId","data-v-ba438752"]]),E1=Object.freeze(Object.defineProperty({__proto__:null,default:y1},Symbol.toStringTag,{value:"Module"})),S1={class:"flex items-center gap-2"},C1={class:"text-sm"},xm=ue({__name:"index",setup(e){const t=St(),n=co();function o(){n.push("iptv-source-add")}return(r,i)=>{const l=Kh,s=tt,a=ot,c=rt;return le(),_e(c,{inset:""},{default:$(()=>{var f;return[(le(!0),kt(Be,null,Sl((f=E(t).data.value.iptvSourceList)==null?void 0:f.value,(u,d)=>(le(),_e(s,{key:d,label:u.url,"is-link":"",center:"",to:`iptv-source-detail/${d}`},{title:$(()=>[Pe("div",S1,[Pe("span",C1,xn(u.name),1),u.sourceType===1?(le(),_e(l,{key:0,plain:"",type:"warning"},{default:$(()=>[Oe(" Local ")]),_:1})):u.sourceType===2?(le(),_e(l,{key:1,plain:"",type:"danger"},{default:$(()=>[Oe(" XTREAM ")]),_:1})):(le(),_e(l,{key:2,plain:"",type:"primary"},{default:$(()=>[Oe(" Remote ")]),_:1})),u.transformJs?(le(),_e(l,{key:3,plain:"",type:"success"},{default:$(()=>[Oe(" Transform JS ")]),_:1})):at("",!0)])]),_:2},1032,["label","to"]))),128)),m(s,null,{default:$(()=>[m(a,{type:"primary",block:"",onClick:o},{default:$(()=>[Oe(" Add ")]),_:1})]),_:1})]}),_:1})}}});typeof we=="function"&&we(xm);const T1=At(xm,[["__scopeId","data-v-afe46b9d"]]),w1=Object.freeze(Object.defineProperty({__proto__:null,default:T1},Symbol.toStringTag,{value:"Module"}));var Lm={exports:{}};(function(e,t){(function(n,o){e.exports=o()})(Rp,function(){var n=1e3,o=6e4,r=36e5,i="millisecond",l="second",s="minute",a="hour",c="day",f="week",u="month",d="quarter",p="year",h="date",g="Invalid Date",O=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,A=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,V={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(Q){var G=["th","st","nd","rd"],U=Q%100;return"["+Q+(G[(U-20)%10]||G[U]||G[0])+"]"}},v=function(Q,G,U){var X=String(Q);return!X||X.length>=G?Q:""+Array(G+1-X.length).join(U)+Q},b={s:v,z:function(Q){var G=-Q.utcOffset(),U=Math.abs(G),X=Math.floor(U/60),W=U%60;return(G<=0?"+":"-")+v(X,2,"0")+":"+v(W,2,"0")},m:function Q(G,U){if(G.date()<U.date())return-Q(U,G);var X=12*(U.year()-G.year())+(U.month()-G.month()),W=G.clone().add(X,u),ie=U-W<0,ce=G.clone().add(X+(ie?-1:1),u);return+(-(X+(U-W)/(ie?W-ce:ce-W))||0)},a:function(Q){return Q<0?Math.ceil(Q)||0:Math.floor(Q)},p:function(Q){return{M:u,y:p,w:f,d:c,D:h,h:a,m:s,s:l,ms:i,Q:d}[Q]||String(Q||"").toLowerCase().replace(/s$/,"")},u:function(Q){return Q===void 0}},w="en",S={};S[w]=V;var N="$isDayjsObject",M=function(Q){return Q instanceof I||!(!Q||!Q[N])},x=function Q(G,U,X){var W;if(!G)return w;if(typeof G=="string"){var ie=G.toLowerCase();S[ie]&&(W=ie),U&&(S[ie]=U,W=ie);var ce=G.split("-");if(!W&&ce.length>1)return Q(ce[0])}else{var ge=G.name;S[ge]=G,W=ge}return!X&&W&&(w=W),W||!X&&w},L=function(Q,G){if(M(Q))return Q.clone();var U=typeof G=="object"?G:{};return U.date=Q,U.args=arguments,new I(U)},D=b;D.l=x,D.i=M,D.w=function(Q,G){return L(Q,{locale:G.$L,utc:G.$u,x:G.$x,$offset:G.$offset})};var I=function(){function Q(U){this.$L=x(U.locale,null,!0),this.parse(U),this.$x=this.$x||U.x||{},this[N]=!0}var G=Q.prototype;return G.parse=function(U){this.$d=function(X){var W=X.date,ie=X.utc;if(W===null)return new Date(NaN);if(D.u(W))return new Date;if(W instanceof Date)return new Date(W);if(typeof W=="string"&&!/Z$/i.test(W)){var ce=W.match(O);if(ce){var ge=ce[2]-1||0,Ce=(ce[7]||"0").substring(0,3);return ie?new Date(Date.UTC(ce[1],ge,ce[3]||1,ce[4]||0,ce[5]||0,ce[6]||0,Ce)):new Date(ce[1],ge,ce[3]||1,ce[4]||0,ce[5]||0,ce[6]||0,Ce)}}return new Date(W)}(U),this.init()},G.init=function(){var U=this.$d;this.$y=U.getFullYear(),this.$M=U.getMonth(),this.$D=U.getDate(),this.$W=U.getDay(),this.$H=U.getHours(),this.$m=U.getMinutes(),this.$s=U.getSeconds(),this.$ms=U.getMilliseconds()},G.$utils=function(){return D},G.isValid=function(){return this.$d.toString()!==g},G.isSame=function(U,X){var W=L(U);return this.startOf(X)<=W&&W<=this.endOf(X)},G.isAfter=function(U,X){return L(U)<this.startOf(X)},G.isBefore=function(U,X){return this.endOf(X)<L(U)},G.$g=function(U,X,W){return D.u(U)?this[X]:this.set(W,U)},G.unix=function(){return Math.floor(this.valueOf()/1e3)},G.valueOf=function(){return this.$d.getTime()},G.startOf=function(U,X){var W=this,ie=!!D.u(X)||X,ce=D.p(U),ge=function(z,ee){var se=D.w(W.$u?Date.UTC(W.$y,ee,z):new Date(W.$y,ee,z),W);return ie?se:se.endOf(c)},Ce=function(z,ee){return D.w(W.toDate()[z].apply(W.toDate("s"),(ie?[0,0,0,0]:[23,59,59,999]).slice(ee)),W)},Re=this.$W,Me=this.$M,ze=this.$D,We="set"+(this.$u?"UTC":"");switch(ce){case p:return ie?ge(1,0):ge(31,11);case u:return ie?ge(1,Me):ge(0,Me+1);case f:var P=this.$locale().weekStart||0,q=(Re<P?Re+7:Re)-P;return ge(ie?ze-q:ze+(6-q),Me);case c:case h:return Ce(We+"Hours",0);case a:return Ce(We+"Minutes",1);case s:return Ce(We+"Seconds",2);case l:return Ce(We+"Milliseconds",3);default:return this.clone()}},G.endOf=function(U){return this.startOf(U,!1)},G.$set=function(U,X){var W,ie=D.p(U),ce="set"+(this.$u?"UTC":""),ge=(W={},W[c]=ce+"Date",W[h]=ce+"Date",W[u]=ce+"Month",W[p]=ce+"FullYear",W[a]=ce+"Hours",W[s]=ce+"Minutes",W[l]=ce+"Seconds",W[i]=ce+"Milliseconds",W)[ie],Ce=ie===c?this.$D+(X-this.$W):X;if(ie===u||ie===p){var Re=this.clone().set(h,1);Re.$d[ge](Ce),Re.init(),this.$d=Re.set(h,Math.min(this.$D,Re.daysInMonth())).$d}else ge&&this.$d[ge](Ce);return this.init(),this},G.set=function(U,X){return this.clone().$set(U,X)},G.get=function(U){return this[D.p(U)]()},G.add=function(U,X){var W,ie=this;U=Number(U);var ce=D.p(X),ge=function(Me){var ze=L(ie);return D.w(ze.date(ze.date()+Math.round(Me*U)),ie)};if(ce===u)return this.set(u,this.$M+U);if(ce===p)return this.set(p,this.$y+U);if(ce===c)return ge(1);if(ce===f)return ge(7);var Ce=(W={},W[s]=o,W[a]=r,W[l]=n,W)[ce]||1,Re=this.$d.getTime()+U*Ce;return D.w(Re,this)},G.subtract=function(U,X){return this.add(-1*U,X)},G.format=function(U){var X=this,W=this.$locale();if(!this.isValid())return W.invalidDate||g;var ie=U||"YYYY-MM-DDTHH:mm:ssZ",ce=D.z(this),ge=this.$H,Ce=this.$m,Re=this.$M,Me=W.weekdays,ze=W.months,We=W.meridiem,P=function(ee,se,ke,T){return ee&&(ee[se]||ee(X,ie))||ke[se].slice(0,T)},q=function(ee){return D.s(ge%12||12,ee,"0")},z=We||function(ee,se,ke){var T=ee<12?"AM":"PM";return ke?T.toLowerCase():T};return ie.replace(A,function(ee,se){return se||function(ke){switch(ke){case"YY":return String(X.$y).slice(-2);case"YYYY":return D.s(X.$y,4,"0");case"M":return Re+1;case"MM":return D.s(Re+1,2,"0");case"MMM":return P(W.monthsShort,Re,ze,3);case"MMMM":return P(ze,Re);case"D":return X.$D;case"DD":return D.s(X.$D,2,"0");case"d":return String(X.$W);case"dd":return P(W.weekdaysMin,X.$W,Me,2);case"ddd":return P(W.weekdaysShort,X.$W,Me,3);case"dddd":return Me[X.$W];case"H":return String(ge);case"HH":return D.s(ge,2,"0");case"h":return q(1);case"hh":return q(2);case"a":return z(ge,Ce,!0);case"A":return z(ge,Ce,!1);case"m":return String(Ce);case"mm":return D.s(Ce,2,"0");case"s":return String(X.$s);case"ss":return D.s(X.$s,2,"0");case"SSS":return D.s(X.$ms,3,"0");case"Z":return ce}return null}(ee)||ce.replace(":","")})},G.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},G.diff=function(U,X,W){var ie,ce=this,ge=D.p(X),Ce=L(U),Re=(Ce.utcOffset()-this.utcOffset())*o,Me=this-Ce,ze=function(){return D.m(ce,Ce)};switch(ge){case p:ie=ze()/12;break;case u:ie=ze();break;case d:ie=ze()/3;break;case f:ie=(Me-Re)/6048e5;break;case c:ie=(Me-Re)/864e5;break;case a:ie=Me/r;break;case s:ie=Me/o;break;case l:ie=Me/n;break;default:ie=Me}return W?ie:D.a(ie)},G.daysInMonth=function(){return this.endOf(u).$D},G.$locale=function(){return S[this.$L]},G.locale=function(U,X){if(!U)return this.$L;var W=this.clone(),ie=x(U,X,!0);return ie&&(W.$L=ie),W},G.clone=function(){return D.w(this.$d,this)},G.toDate=function(){return new Date(this.valueOf())},G.toJSON=function(){return this.isValid()?this.toISOString():null},G.toISOString=function(){return this.$d.toISOString()},G.toString=function(){return this.$d.toUTCString()},Q}(),j=I.prototype;return L.prototype=j,[["$ms",i],["$s",l],["$m",s],["$H",a],["$W",c],["$M",u],["$y",p],["$D",h]].forEach(function(Q){j[Q[1]]=function(G){return this.$g(G,Q[0],Q[1])}}),L.extend=function(Q,G){return Q.$i||(Q(G,I,L),Q.$i=!0),L},L.locale=x,L.isDayjs=M,L.unix=function(Q){return L(1e3*Q)},L.en=S[w],L.Ls=S,L.p={},L})})(Lm);var k1=Lm.exports;const O1=Np(k1),P1={class:"flex flex-col"},Dm=ue({__name:"index",setup(e){const t=fe([]);return Pt(async()=>{t.value=(await nn.getLogs()).reverse()}),(n,o)=>{const r=tt,i=rt;return le(),_e(i,{inset:""},{default:$(()=>[(le(!0),kt(Be,null,Sl(E(t),l=>(le(),_e(r,{key:l.time,title:`${l.level} | ${l.tag}`,value:E(O1)(l.time).format("HH:mm:ss")},{label:$(()=>[Pe("div",P1,[Pe("div",null,xn(l.message),1),Pe("div",null,xn(l.cause),1)])]),_:2},1032,["title","value"]))),128))]),_:1})}}});typeof we=="function"&&we(Dm);const A1=At(Dm,[["__scopeId","data-v-4d81afa5"]]),I1=Object.freeze(Object.defineProperty({__proto__:null,default:A1},Symbol.toStringTag,{value:"Module"})),x1=e=>(Cs("data-v-f22b39f8"),e=e(),Ts(),e),L1=x1(()=>Pe("div",{class:"flex flex-col gap-1"},[Pe("div",null,"Example:"),Pe("div",null,"Header-Name-1: Header-Value-1"),Pe("div",null,"Header-Name-2: Header-Value-2")],-1)),Rm=ue({__name:"index",setup(e){const t=St(),n=Object.entries(gf).map(s=>({text:s[1],value:s[0]})),o=Object.entries(vf).map(s=>({text:s[1],value:s[0]})),r=Object.entries(_f).map(s=>({text:s[1],value:s[0]})),i=[{text:"1s",value:1*1e3},{text:"2s",value:2*1e3},{text:"3s",value:3*1e3},{text:"4s",value:4*1e3},{text:"5s",value:5*1e3},{text:"10s",value:10*1e3},{text:"15s",value:15*1e3},{text:"20s",value:20*1e3},{text:"25s",value:25*1e3},{text:"30s",value:30*1e3},{text:"45s",value:45*1e3},{text:"1min",value:60*1e3}],l=[{text:"0s",value:0*1e3},{text:"1s",value:1*1e3},{text:"2s",value:2*1e3},{text:"3s",value:3*1e3},{text:"4s",value:4*1e3},{text:"5s",value:5*1e3},{text:"6s",value:6*1e3},{text:"7s",value:7*1e3},{text:"8s",value:8*1e3},{text:"9s",value:9*1e3},{text:"10s",value:10*1e3},{text:"15s",value:15*1e3},{text:"20s",value:20*1e3},{text:"25s",value:25*1e3},{text:"30s",value:30*1e3},{text:"45s",value:45*1e3},{text:"1min",value:60*1e3}];return(s,a)=>{const c=on,f=pr,u=tt,d=Ws,p=ot,h=rt;return le(),_e(h,{inset:""},{default:$(()=>[m(u,{title:"Video Player Core",center:"","is-link":""},{value:$(()=>[m(f,{value:E(t).data.value.videoPlayerCore,"onUpdate:value":a[0]||(a[0]=g=>E(t).data.value.videoPlayerCore=g),columns:E(n)},{default:$(()=>[m(c,{"model-value":E(gf)[E(t).data.value.videoPlayerCore],"input-align":"right",readonly:""},null,8,["model-value"])]),_:1},8,["value","columns"])]),_:1}),m(u,{title:"Render Mode",center:"","is-link":""},{value:$(()=>[m(f,{value:E(t).data.value.videoPlayerRenderMode,"onUpdate:value":a[1]||(a[1]=g=>E(t).data.value.videoPlayerRenderMode=g),columns:E(o)},{default:$(()=>[m(c,{"model-value":E(vf)[E(t).data.value.videoPlayerRenderMode],"input-align":"right",readonly:""},null,8,["model-value"])]),_:1},8,["value","columns"])]),_:1}),m(u,{title:"Global Display Mode",center:"","is-link":""},{value:$(()=>[m(f,{value:E(t).data.value.videoPlayerDisplayMode,"onUpdate:value":a[2]||(a[2]=g=>E(t).data.value.videoPlayerDisplayMode=g),columns:E(r)},{default:$(()=>[m(c,{"model-value":E(_f)[E(t).data.value.videoPlayerDisplayMode],"input-align":"right",readonly:""},null,8,["model-value"])]),_:1},8,["value","columns"])]),_:1}),m(u,{title:"Load Timeout",center:"","is-link":""},{value:$(()=>[m(f,{value:E(t).data.value.videoPlayerLoadTimeout,"onUpdate:value":a[3]||(a[3]=g=>E(t).data.value.videoPlayerLoadTimeout=g),columns:i},{default:$(()=>{var g;return[m(c,{"model-value":((g=i.find(O=>O.value===E(t).data.value.videoPlayerLoadTimeout))==null?void 0:g.text)??`${E(t).data.value.videoPlayerLoadTimeout}ms`,"input-align":"right",readonly:""},null,8,["model-value"])]}),_:1},8,["value"])]),_:1}),m(u,{title:"Buffer Time",center:"","is-link":""},{value:$(()=>[m(f,{value:E(t).data.value.videoPlayerBufferTime,"onUpdate:value":a[4]||(a[4]=g=>E(t).data.value.videoPlayerBufferTime=g),columns:l},{default:$(()=>{var g;return[m(c,{"model-value":((g=l.find(O=>O.value===E(t).data.value.videoPlayerBufferTime))==null?void 0:g.text)??`${E(t).data.value.videoPlayerLoadTimeout}ms`,"input-align":"right",readonly:""},null,8,["model-value"])]}),_:1},8,["value"])]),_:1}),m(u,{title:"Global UA",center:""},{value:$(()=>[m(c,{modelValue:E(t).data.value.videoPlayerUserAgent,"onUpdate:modelValue":a[5]||(a[5]=g=>E(t).data.value.videoPlayerUserAgent=g),"input-align":"right"},null,8,["modelValue"])]),_:1}),m(u,{center:""},{title:$(()=>[m(d,{title:"Custom Headers"},{help:$(()=>[L1]),_:1})]),value:$(()=>[m(c,{modelValue:E(t).data.value.videoPlayerHeaders,"onUpdate:modelValue":a[6]||(a[6]=g=>E(t).data.value.videoPlayerHeaders=g),"input-align":"right",type:"textarea",autosize:""},null,8,["modelValue"])]),_:1}),m(u,null,{default:$(()=>[m(p,{type:"primary",block:"",onClick:E(t).update},{default:$(()=>[Oe(" Update ")]),_:1},8,["onClick"])]),_:1})]),_:1})}}});typeof we=="function"&&we(Rm);const D1=At(Rm,[["__scopeId","data-v-f22b39f8"]]),R1=Object.freeze(Object.defineProperty({__proto__:null,default:D1},Symbol.toStringTag,{value:"Module"})),N1={class:"prose prose-sm m-auto text-left"},M1=ub('<h2>File-based Routing</h2><p>Routes will be auto-generated for Vue files in this dir with the same file structure. Check out <a href="https://github.com/posva/unplugin-vue-router" target="_blank" rel="noopener"><code>unplugin-vue-router</code></a> for more details.</p><h3>Path Aliasing</h3><p><code>~/</code> is aliased to <code>./src/</code> folder.</p><p>For example, instead of having</p><pre class="shiki shiki-themes vitesse-light vitesse-dark" style="--shiki-light:#393a34;--shiki-dark:#dbd7caee;--shiki-light-bg:#ffffff;--shiki-dark-bg:#121212;" tabindex="0"><code class="language-ts"><span class="line"><span style="--shiki-light:#1E754F;--shiki-dark:#4D9375;">import</span><span style="--shiki-light:#999999;--shiki-dark:#666666;"> {</span><span style="--shiki-light:#B07D48;--shiki-dark:#BD976A;"> isDark</span><span style="--shiki-light:#999999;--shiki-dark:#666666;"> }</span><span style="--shiki-light:#1E754F;--shiki-dark:#4D9375;"> from</span><span style="--shiki-light:#B5695977;--shiki-dark:#C98A7D77;"> &#39;</span><span style="--shiki-light:#B56959;--shiki-dark:#C98A7D;">../../../../composables</span><span style="--shiki-light:#B5695977;--shiki-dark:#C98A7D77;">&#39;</span></span></code></pre><p>now, you can use</p><pre class="shiki shiki-themes vitesse-light vitesse-dark" style="--shiki-light:#393a34;--shiki-dark:#dbd7caee;--shiki-light-bg:#ffffff;--shiki-dark-bg:#121212;" tabindex="0"><code class="language-ts"><span class="line"><span style="--shiki-light:#1E754F;--shiki-dark:#4D9375;">import</span><span style="--shiki-light:#999999;--shiki-dark:#666666;"> {</span><span style="--shiki-light:#B07D48;--shiki-dark:#BD976A;"> isDark</span><span style="--shiki-light:#999999;--shiki-dark:#666666;"> }</span><span style="--shiki-light:#1E754F;--shiki-dark:#4D9375;"> from</span><span style="--shiki-light:#B5695977;--shiki-dark:#C98A7D77;"> &#39;</span><span style="--shiki-light:#B56959;--shiki-dark:#C98A7D;">~/composables</span><span style="--shiki-light:#B5695977;--shiki-dark:#C98A7D77;">&#39;</span></span></code></pre>',8),V1=[M1],$1=[],F1={__name:"README",setup(e,{expose:t}){return t({frontmatter:{meta:[]}}),ih({meta:[]}),(r,i)=>(le(),kt("div",N1,V1))}},B1=Object.freeze(Object.defineProperty({__proto__:null,default:F1,meta:$1},Symbol.toStringTag,{value:"Module"})),Nm=e=>(Cs("data-v-e7c32ba5"),e=e(),Ts(),e),U1=Nm(()=>Pe("div",{class:"w-410px flex flex-col gap-1"},[Pe("div",null,"Example:"),Pe("div",null,"base64: iVBORw0KGgoAAAANSUhEUg..."),Pe("div",null,"Network URL: http://image.dev/bg.png"),Pe("div",null,"Local file: file:///storage/emulated/0/Download/bg.png")],-1)),j1=Nm(()=>Pe("div",{class:"w-410px flex flex-col gap-1"},[Pe("div",null,"Example:"),Pe("div",null,"base64: iVBORw0KGgoAAAANSUhEUg..."),Pe("div",null,"Network URL: http://image.dev/bg.png"),Pe("div",null,"Local file: file:///storage/emulated/0/Download/bg.png")],-1)),Mm=ue({__name:"index",setup(e){const t=St();return De(t,()=>{t.data.value.themeAppCurrent||(t.data.value.themeAppCurrent={name:"",background:""})}),(n,o)=>{const r=on,i=tt,l=Ws,s=ot,a=rt;return E(t).data.value.themeAppCurrent?(le(),_e(a,{key:0,inset:""},{default:$(()=>[m(i,{title:"Theme Name",center:""},{value:$(()=>[m(r,{modelValue:E(t).data.value.themeAppCurrent.name,"onUpdate:modelValue":o[0]||(o[0]=c=>E(t).data.value.themeAppCurrent.name=c),"input-align":"right"},null,8,["modelValue"])]),_:1}),m(i,{center:""},{title:$(()=>[m(l,{title:"Background"},{help:$(()=>[U1]),_:1})]),value:$(()=>[m(r,{modelValue:E(t).data.value.themeAppCurrent.background,"onUpdate:modelValue":o[1]||(o[1]=c=>E(t).data.value.themeAppCurrent.background=c),"input-align":"right"},null,8,["modelValue"])]),_:1}),m(i,{center:""},{title:$(()=>[m(l,{title:"Texture"},{help:$(()=>[j1]),_:1})]),value:$(()=>[m(r,{modelValue:E(t).data.value.themeAppCurrent.texture,"onUpdate:modelValue":o[2]||(o[2]=c=>E(t).data.value.themeAppCurrent.texture=c),"input-align":"right"},null,8,["modelValue"])]),_:1}),m(i,{title:"Texture Alpha",center:""},{value:$(()=>[m(r,{modelValue:E(t).data.value.themeAppCurrent.textureAlpha,"onUpdate:modelValue":o[3]||(o[3]=c=>E(t).data.value.themeAppCurrent.textureAlpha=c),type:"number","input-align":"right"},null,8,["modelValue"])]),_:1}),m(i,null,{default:$(()=>[m(s,{type:"primary",block:"",onClick:E(t).update},{default:$(()=>[Oe(" Update ")]),_:1},8,["onClick"])]),_:1})]),_:1})):at("",!0)}}});typeof we=="function"&&we(Mm);const H1=At(Mm,[["__scopeId","data-v-e7c32ba5"]]),W1=Object.freeze(Object.defineProperty({__proto__:null,default:H1},Symbol.toStringTag,{value:"Module"})),Vm=ue({__name:"index",setup(e){const t=St(),n=Object.entries(mf).map(l=>({text:l[1],value:l[0]})),o=[...Array.from({length:6},(l,s)=>(s+1)*5).map(l=>({text:`${l}s`,value:l*1e3})),{text:"Never close",value:9223372036854776e3}],r=[{text:"Auto",value:0},...Array.from({length:16},(l,s)=>(s+5)/10).map(l=>({text:`×${l}`,value:l}))],i=[...Array.from({length:16},(l,s)=>(s+5)/10).map(l=>({text:`×${l}`,value:l}))];return(l,s)=>{const a=uo,c=tt,f=on,u=pr,d=ot,p=rt;return le(),_e(p,{inset:""},{default:$(()=>[m(c,{title:"Show EPG Progress",center:""},{value:$(()=>[m(a,{modelValue:E(t).data.value.uiShowEpgProgrammeProgress,"onUpdate:modelValue":s[0]||(s[0]=h=>E(t).data.value.uiShowEpgProgrammeProgress=h)},null,8,["modelValue"])]),_:1}),m(c,{title:"Permanent EPG Progress",center:""},{value:$(()=>[m(a,{modelValue:E(t).data.value.uiShowEpgProgrammePermanentProgress,"onUpdate:modelValue":s[1]||(s[1]=h=>E(t).data.value.uiShowEpgProgrammePermanentProgress=h)},null,8,["modelValue"])]),_:1}),m(c,{title:"Show Channel Logo",center:""},{value:$(()=>[m(a,{modelValue:E(t).data.value.uiShowChannelLogo,"onUpdate:modelValue":s[2]||(s[2]=h=>E(t).data.value.uiShowChannelLogo=h)},null,8,["modelValue"])]),_:1}),m(c,{title:"Channel Preview",center:""},{value:$(()=>[m(a,{modelValue:E(t).data.value.uiShowChannelPreview,"onUpdate:modelValue":s[3]||(s[3]=h=>E(t).data.value.uiShowChannelPreview=h)},null,8,["modelValue"])]),_:1}),m(c,{title:"Classic Channel Panel",center:""},{value:$(()=>[m(a,{modelValue:E(t).data.value.uiUseClassicPanelScreen,"onUpdate:modelValue":s[4]||(s[4]=h=>E(t).data.value.uiUseClassicPanelScreen=h)},null,8,["modelValue"])]),_:1}),m(c,{title:"Time Display",center:"","is-link":""},{value:$(()=>[m(u,{value:E(t).data.value.uiTimeShowMode,"onUpdate:value":s[5]||(s[5]=h=>E(t).data.value.uiTimeShowMode=h),columns:E(n)},{default:$(()=>[m(f,{"model-value":E(mf)[E(t).data.value.uiTimeShowMode],"input-align":"right",readonly:""},null,8,["model-value"])]),_:1},8,["value","columns"])]),_:1}),m(c,{title:"Auto Close Delay",center:"","is-link":""},{value:$(()=>[m(u,{value:E(t).data.value.uiScreenAutoCloseDelay,"onUpdate:value":s[6]||(s[6]=h=>E(t).data.value.uiScreenAutoCloseDelay=h),columns:o},{default:$(()=>{var h;return[m(f,{"model-value":((h=o.find(g=>g.value===E(t).data.value.uiScreenAutoCloseDelay))==null?void 0:h.text)??`${E(t).data.value.uiScreenAutoCloseDelay}ms`,"input-align":"right",readonly:""},null,8,["model-value"])]}),_:1},8,["value"])]),_:1}),m(c,{title:"UI Scale Ratio",center:"","is-link":""},{value:$(()=>[m(u,{value:E(t).data.value.uiDensityScaleRatio,"onUpdate:value":s[7]||(s[7]=h=>E(t).data.value.uiDensityScaleRatio=h),columns:r},{default:$(()=>{var h;return[m(f,{"model-value":((h=r.find(g=>g.value===E(t).data.value.uiDensityScaleRatio))==null?void 0:h.text)??`×${E(t).data.value.uiDensityScaleRatio}`,"input-align":"right",readonly:""},null,8,["model-value"])]}),_:1},8,["value"])]),_:1}),m(c,{title:"Font Scale Ratio",center:"","is-link":""},{value:$(()=>[m(u,{value:E(t).data.value.uiFontScaleRatio,"onUpdate:value":s[8]||(s[8]=h=>E(t).data.value.uiFontScaleRatio=h),columns:i},{default:$(()=>{var h;return[m(f,{"model-value":((h=i.find(g=>g.value===E(t).data.value.uiFontScaleRatio))==null?void 0:h.text)??`×${E(t).data.value.uiFontScaleRatio}`,"input-align":"right",readonly:""},null,8,["model-value"])]}),_:1},8,["value"])]),_:1}),m(c,{title:"Focus Optimization",center:""},{value:$(()=>[m(a,{modelValue:E(t).data.value.uiFocusOptimize,"onUpdate:modelValue":s[9]||(s[9]=h=>E(t).data.value.uiFocusOptimize=h)},null,8,["modelValue"])]),_:1}),m(c,null,{default:$(()=>[m(d,{type:"primary",block:"",onClick:E(t).update},{default:$(()=>[Oe(" Update ")]),_:1},8,["onClick"])]),_:1})]),_:1})}}});typeof we=="function"&&we(Vm);const G1=Object.freeze(Object.defineProperty({__proto__:null,default:Vm},Symbol.toStringTag,{value:"Module"})),$m=ue({__name:"index",setup(e){const t=St(),n=[{text:"Stable",value:"stable"},{text:"Beta",value:"beta"},{text:"Dev",value:"dev"}];return(o,r)=>{const i=on,l=pr,s=tt,a=uo,c=ot,f=rt;return le(),_e(f,{inset:""},{default:$(()=>[m(s,{title:"Update Channel",center:"","is-link":""},{value:$(()=>[m(l,{value:E(t).data.value.updateChannel,"onUpdate:value":r[0]||(r[0]=u=>E(t).data.value.updateChannel=u),columns:n},{default:$(()=>{var u;return[m(i,{"model-value":((u=n.find(d=>d.value===E(t).data.value.updateChannel))==null?void 0:u.text)??`${E(t).data.value.updateChannel}`,"input-align":"right",readonly:""},null,8,["model-value"])]}),_:1},8,["value"])]),_:1}),m(s,{title:"Force Update Reminder",center:""},{value:$(()=>[m(a,{modelValue:E(t).data.value.updateForceRemind,"onUpdate:modelValue":r[1]||(r[1]=u=>E(t).data.value.updateForceRemind=u)},null,8,["modelValue"])]),_:1}),m(s,null,{default:$(()=>[m(c,{type:"primary",block:"",onClick:E(t).update},{default:$(()=>[Oe(" Update ")]),_:1},8,["onClick"])]),_:1})]),_:1})}}});typeof we=="function"&&we($m);const K1=Object.freeze(Object.defineProperty({__proto__:null,default:$m},Symbol.toStringTag,{value:"Module"}));try{self["workbox:window:7.0.0"]&&_()}catch{}function Za(e,t){return new Promise(function(n){var o=new MessageChannel;o.port1.onmessage=function(r){n(r.data)},e.postMessage(t,[o.port2])})}function Y1(e){var t=function(n,o){if(typeof n!="object"||!n)return n;var r=n[Symbol.toPrimitive];if(r!==void 0){var i=r.call(n,o);if(typeof i!="object")return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(n)}(e,"string");return typeof t=="symbol"?t:t+""}function z1(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,Y1(o.key),o)}}function es(e,t){return es=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,o){return n.__proto__=o,n},es(e,t)}function Vf(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}function X1(e,t){var n=typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n)return(n=n.call(e)).next.bind(n);if(Array.isArray(e)||(n=function(r,i){if(r){if(typeof r=="string")return Vf(r,i);var l=Object.prototype.toString.call(r).slice(8,-1);return l==="Object"&&r.constructor&&(l=r.constructor.name),l==="Map"||l==="Set"?Array.from(r):l==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(l)?Vf(r,i):void 0}}(e))||t){n&&(e=n);var o=0;return function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}try{self["workbox:core:7.0.0"]&&_()}catch{}var ma=function(){var e=this;this.promise=new Promise(function(t,n){e.resolve=t,e.reject=n})};function ga(e,t){var n=location.href;return new URL(e,n).href===new URL(t,n).href}var Vo=function(e,t){this.type=e,Object.assign(this,t)};function Bn(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}function q1(){}var J1={type:"SKIP_WAITING"};function $f(e,t){return e&&e.then?e.then(q1):Promise.resolve()}var Q1=function(e){function t(s,a){var c,f;return a===void 0&&(a={}),(c=e.call(this)||this).nn={},c.tn=0,c.rn=new ma,c.en=new ma,c.on=new ma,c.un=0,c.an=new Set,c.cn=function(){var u=c.fn,d=u.installing;c.tn>0||!ga(d.scriptURL,c.sn.toString())||performance.now()>c.un+6e4?(c.vn=d,u.removeEventListener("updatefound",c.cn)):(c.hn=d,c.an.add(d),c.rn.resolve(d)),++c.tn,d.addEventListener("statechange",c.ln)},c.ln=function(u){var d=c.fn,p=u.target,h=p.state,g=p===c.vn,O={sw:p,isExternal:g,originalEvent:u};!g&&c.mn&&(O.isUpdate=!0),c.dispatchEvent(new Vo(h,O)),h==="installed"?c.wn=self.setTimeout(function(){h==="installed"&&d.waiting===p&&c.dispatchEvent(new Vo("waiting",O))},200):h==="activating"&&(clearTimeout(c.wn),g||c.en.resolve(p))},c.yn=function(u){var d=c.hn,p=d!==navigator.serviceWorker.controller;c.dispatchEvent(new Vo("controlling",{isExternal:p,originalEvent:u,sw:d,isUpdate:c.mn})),p||c.on.resolve(d)},c.gn=(f=function(u){var d=u.data,p=u.ports,h=u.source;return Bn(c.getSW(),function(){c.an.has(h)&&c.dispatchEvent(new Vo("message",{data:d,originalEvent:u,ports:p,sw:h}))})},function(){for(var u=[],d=0;d<arguments.length;d++)u[d]=arguments[d];try{return Promise.resolve(f.apply(this,u))}catch(p){return Promise.reject(p)}}),c.sn=s,c.nn=a,navigator.serviceWorker.addEventListener("message",c.gn),c}var n,o;o=e,(n=t).prototype=Object.create(o.prototype),n.prototype.constructor=n,es(n,o);var r,i,l=t.prototype;return l.register=function(s){var a=(s===void 0?{}:s).immediate,c=a!==void 0&&a;try{var f=this;return Bn(function(u,d){var p=u();return p&&p.then?p.then(d):d(p)}(function(){if(!c&&document.readyState!=="complete")return $f(new Promise(function(u){return window.addEventListener("load",u)}))},function(){return f.mn=!!navigator.serviceWorker.controller,f.dn=f.pn(),Bn(f.bn(),function(u){f.fn=u,f.dn&&(f.hn=f.dn,f.en.resolve(f.dn),f.on.resolve(f.dn),f.dn.addEventListener("statechange",f.ln,{once:!0}));var d=f.fn.waiting;return d&&ga(d.scriptURL,f.sn.toString())&&(f.hn=d,Promise.resolve().then(function(){f.dispatchEvent(new Vo("waiting",{sw:d,wasWaitingBeforeRegister:!0}))}).then(function(){})),f.hn&&(f.rn.resolve(f.hn),f.an.add(f.hn)),f.fn.addEventListener("updatefound",f.cn),navigator.serviceWorker.addEventListener("controllerchange",f.yn),f.fn})}))}catch(u){return Promise.reject(u)}},l.update=function(){try{return this.fn?Bn($f(this.fn.update())):Bn()}catch(s){return Promise.reject(s)}},l.getSW=function(){return this.hn!==void 0?Promise.resolve(this.hn):this.rn.promise},l.messageSW=function(s){try{return Bn(this.getSW(),function(a){return Za(a,s)})}catch(a){return Promise.reject(a)}},l.messageSkipWaiting=function(){this.fn&&this.fn.waiting&&Za(this.fn.waiting,J1)},l.pn=function(){var s=navigator.serviceWorker.controller;return s&&ga(s.scriptURL,this.sn.toString())?s:void 0},l.bn=function(){try{var s=this;return Bn(function(a,c){try{var f=a()}catch(u){return c(u)}return f&&f.then?f.then(void 0,c):f}(function(){return Bn(navigator.serviceWorker.register(s.sn,s.nn),function(a){return s.un=performance.now(),a})},function(a){throw a}))}catch(a){return Promise.reject(a)}},r=t,(i=[{key:"active",get:function(){return this.en.promise}},{key:"controlling",get:function(){return this.on.promise}}])&&z1(r.prototype,i),Object.defineProperty(r,"prototype",{writable:!1}),r}(function(){function e(){this.Pn=new Map}var t=e.prototype;return t.addEventListener=function(n,o){this.jn(n).add(o)},t.removeEventListener=function(n,o){this.jn(n).delete(o)},t.dispatchEvent=function(n){n.target=this;for(var o,r=X1(this.jn(n.type));!(o=r()).done;)(0,o.value)(n)},t.jn=function(n){return this.Pn.has(n)||this.Pn.set(n,new Set),this.Pn.get(n)},e}());const Z1=Object.freeze(Object.defineProperty({__proto__:null,Workbox:Q1,WorkboxEvent:Vo,messageSW:Za},Symbol.toStringTag,{value:"Module"}));

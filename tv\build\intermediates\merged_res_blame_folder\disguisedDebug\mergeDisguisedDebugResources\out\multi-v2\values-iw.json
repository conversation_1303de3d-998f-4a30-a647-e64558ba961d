{"logs": [{"outputFile": "top.yogiczy.mytv.tv-mergeDisguisedDebugResources-85:/values-iw/values-iw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\0c82a76523f617aef246a3faf4397c6a\\transformed\\ui-release\\res\\values-iw\\values-iw.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "186,275,359,452,547,630,707,792,878,957,1035,1117,1186,1270,1344,1422,1498,1572,1643", "endColumns": "88,83,92,94,82,76,84,85,78,77,81,68,83,73,77,75,73,70,118", "endOffsets": "270,354,447,542,625,702,787,873,952,1030,1112,1181,1265,1339,1417,1493,1567,1638,1757"}, "to": {"startLines": "69,70,71,72,73,128,129,247,248,250,251,255,257,258,259,260,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5202,5291,5375,5468,5563,9469,9546,20296,20382,20536,20614,20935,21080,21164,21238,21316,21493,21567,21638", "endColumns": "88,83,92,94,82,76,84,85,78,77,81,68,83,73,77,75,73,70,118", "endOffsets": "5286,5370,5463,5558,5641,9541,9626,20377,20456,20609,20691,20999,21159,21233,21311,21387,21562,21633,21752"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\35f391f48f1163378f0ecf1dfb563ddd\\transformed\\material3-release\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,288,406,521,620,715,828,955,1070,1210,1295,1393,1484,1580,1697,1817,1920,2056,2191,2312,2465,2583,2693,2808,2926,3018,3115,3227,3351,3449,3548,3652,3786,3927,4034,4134,4215,4320,4424,4534,4620,4705,4808,4888,4972,5073,5172,5263,5358,5444,5546,5645,5742,5867,5947,6048", "endColumns": "116,115,117,114,98,94,112,126,114,139,84,97,90,95,116,119,102,135,134,120,152,117,109,114,117,91,96,111,123,97,98,103,133,140,106,99,80,104,103,109,85,84,102,79,83,100,98,90,94,85,101,98,96,124,79,100,95", "endOffsets": "167,283,401,516,615,710,823,950,1065,1205,1290,1388,1479,1575,1692,1812,1915,2051,2186,2307,2460,2578,2688,2803,2921,3013,3110,3222,3346,3444,3543,3647,3781,3922,4029,4129,4210,4315,4419,4529,4615,4700,4803,4883,4967,5068,5167,5258,5353,5439,5541,5640,5737,5862,5942,6043,6139"}, "to": {"startLines": "131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9694,9811,9927,10045,10160,10259,10354,10467,10594,10709,10849,10934,11032,11123,11219,11336,11456,11559,11695,11830,11951,12104,12222,12332,12447,12565,12657,12754,12866,12990,13088,13187,13291,13425,13566,13673,13773,13854,13959,14063,14173,14259,14344,14447,14527,14611,14712,14811,14902,14997,15083,15185,15284,15381,15506,15586,15687", "endColumns": "116,115,117,114,98,94,112,126,114,139,84,97,90,95,116,119,102,135,134,120,152,117,109,114,117,91,96,111,123,97,98,103,133,140,106,99,80,104,103,109,85,84,102,79,83,100,98,90,94,85,101,98,96,124,79,100,95", "endOffsets": "9806,9922,10040,10155,10254,10349,10462,10589,10704,10844,10929,11027,11118,11214,11331,11451,11554,11690,11825,11946,12099,12217,12327,12442,12560,12652,12749,12861,12985,13083,13182,13286,13420,13561,13668,13768,13849,13954,14058,14168,14254,14339,14442,14522,14606,14707,14806,14897,14992,15078,15180,15279,15376,15501,15581,15682,15778"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\07b9e5f639314a52052890932176ce08\\transformed\\media3-ui-1.8.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,523,757,836,914,990,1075,1159,1221,1283,1372,1458,1523,1587,1650,1718,1839,1950,2069,2140,2217,2286,2347,2437,2526,2590,2653,2707,2778,2826,2887,2946,3013,3074,3137,3198,3255,3321,3373,3427,3495,3563,3617", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "17,12,12,78,77,75,84,83,61,61,88,85,64,63,62,67,120,110,118,70,76,68,60,89,88,63,62,53,70,47,60,58,66,60,62,60,56,65,51,53,67,67,53,65", "endOffsets": "279,518,752,831,909,985,1070,1154,1216,1278,1367,1453,1518,1582,1645,1713,1834,1945,2064,2135,2212,2281,2342,2432,2521,2585,2648,2702,2773,2821,2882,2941,3008,3069,3132,3193,3250,3316,3368,3422,3490,3558,3612,3678"}, "to": {"startLines": "2,11,16,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,379,618,5771,5850,5928,6004,6089,6173,6235,6297,6386,6472,6537,6601,6664,6732,6853,6964,7083,7154,7231,7300,7361,7451,7540,7604,8281,8335,8406,8454,8515,8574,8641,8702,8765,8826,8883,8949,9001,9055,9123,9191,9245", "endLines": "10,15,20,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "endColumns": "17,12,12,78,77,75,84,83,61,61,88,85,64,63,62,67,120,110,118,70,76,68,60,89,88,63,62,53,70,47,60,58,66,60,62,60,56,65,51,53,67,67,53,65", "endOffsets": "374,613,847,5845,5923,5999,6084,6168,6230,6292,6381,6467,6532,6596,6659,6727,6848,6959,7078,7149,7226,7295,7356,7446,7535,7599,7662,8330,8401,8449,8510,8569,8636,8697,8760,8821,8878,8944,8996,9050,9118,9186,9240,9306"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\e4a290dba5f52ccc9bc02699d6e15cbe\\transformed\\material-release\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "201", "startColumns": "4", "startOffsets": "16686", "endColumns": "87", "endOffsets": "16769"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\cdd23b539dbc00d913771dd5e581033e\\transformed\\foundation-release\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,222", "endColumns": "79,86,87", "endOffsets": "130,217,305"}, "to": {"startLines": "53,265,266", "startColumns": "4,4,4", "startOffsets": "3719,21757,21844", "endColumns": "79,86,87", "endOffsets": "3794,21839,21927"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\75b82da373a07aa04f7fa0a8c467d672\\transformed\\media3-exoplayer-1.8.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,246,311,385,447,527,607", "endColumns": "64,58,66,64,73,61,79,79,61", "endOffsets": "115,174,241,306,380,442,522,602,664"}, "to": {"startLines": "100,101,102,103,104,105,106,107,108", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7667,7732,7791,7858,7923,7997,8059,8139,8219", "endColumns": "64,58,66,64,73,61,79,79,61", "endOffsets": "7727,7786,7853,7918,7992,8054,8134,8214,8276"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\03c59f01b4d50573fe2a148684529e0c\\transformed\\material-1.12.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,311,388,463,540,640,731,824,937,1017,1077,1142,1230,1300,1363,1455,1518,1578,1637,1700,1761,1815,1917,1974,2033,2087,2155,2266,2347,2422,2509,2589,2671,2803,2874,2947,3071,3159,3235,3288,3342,3408,3481,3557,3628,3706,3776,3851,3933,4001,4102,4187,4257,4347,4438,4512,4585,4674,4725,4806,4873,4955,5040,5102,5166,5229,5297,5391,5486,5576,5673,5730,5788,5863,5945,6020", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,76,74,76,99,90,92,112,79,59,64,87,69,62,91,62,59,58,62,60,53,101,56,58,53,67,110,80,74,86,79,81,131,70,72,123,87,75,52,53,65,72,75,70,77,69,74,81,67,100,84,69,89,90,73,72,88,50,80,66,81,84,61,63,62,67,93,94,89,96,56,57,74,81,74,75", "endOffsets": "306,383,458,535,635,726,819,932,1012,1072,1137,1225,1295,1358,1450,1513,1573,1632,1695,1756,1810,1912,1969,2028,2082,2150,2261,2342,2417,2504,2584,2666,2798,2869,2942,3066,3154,3230,3283,3337,3403,3476,3552,3623,3701,3771,3846,3928,3996,4097,4182,4252,4342,4433,4507,4580,4669,4720,4801,4868,4950,5035,5097,5161,5224,5292,5386,5481,5571,5668,5725,5783,5858,5940,6015,6091"}, "to": {"startLines": "21,54,55,56,57,58,66,67,68,74,75,126,127,130,188,189,190,191,192,193,194,195,196,197,198,199,200,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,249,253,254,256", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "852,3799,3876,3951,4028,4128,4916,5009,5122,5646,5706,9311,9399,9631,15783,15875,15938,15998,16057,16120,16181,16235,16337,16394,16453,16507,16575,16774,16855,16930,17017,17097,17179,17311,17382,17455,17579,17667,17743,17796,17850,17916,17989,18065,18136,18214,18284,18359,18441,18509,18610,18695,18765,18855,18946,19020,19093,19182,19233,19314,19381,19463,19548,19610,19674,19737,19805,19899,19994,20084,20181,20238,20461,20778,20860,21004", "endLines": "25,54,55,56,57,58,66,67,68,74,75,126,127,130,188,189,190,191,192,193,194,195,196,197,198,199,200,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,249,253,254,256", "endColumns": "12,76,74,76,99,90,92,112,79,59,64,87,69,62,91,62,59,58,62,60,53,101,56,58,53,67,110,80,74,86,79,81,131,70,72,123,87,75,52,53,65,72,75,70,77,69,74,81,67,100,84,69,89,90,73,72,88,50,80,66,81,84,61,63,62,67,93,94,89,96,56,57,74,81,74,75", "endOffsets": "1058,3871,3946,4023,4123,4214,5004,5117,5197,5701,5766,9394,9464,9689,15870,15933,15993,16052,16115,16176,16230,16332,16389,16448,16502,16570,16681,16850,16925,17012,17092,17174,17306,17377,17450,17574,17662,17738,17791,17845,17911,17984,18060,18131,18209,18279,18354,18436,18504,18605,18690,18760,18850,18941,19015,19088,19177,19228,19309,19376,19458,19543,19605,19669,19732,19800,19894,19989,20079,20176,20233,20291,20531,20855,20930,21075"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\c64c27f9b5a3bd14c8d70afc2c5cfe89\\transformed\\core-1.16.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "59,60,61,62,63,64,65,261", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4219,4313,4415,4512,4609,4710,4810,21392", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "4308,4410,4507,4604,4705,4805,4911,21488"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\71973fdd2f8b1122e619daae98b107aa\\transformed\\appcompat-1.7.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,310,418,502,604,720,799,877,968,1062,1156,1250,1350,1443,1538,1631,1722,1814,1895,2000,2103,2201,2306,2408,2510,2664,2761", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "205,305,413,497,599,715,794,872,963,1057,1151,1245,1345,1438,1533,1626,1717,1809,1890,1995,2098,2196,2301,2403,2505,2659,2756,2838"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,252", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1063,1168,1268,1376,1460,1562,1678,1757,1835,1926,2020,2114,2208,2308,2401,2496,2589,2680,2772,2853,2958,3061,3159,3264,3366,3468,3622,20696", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "1163,1263,1371,1455,1557,1673,1752,1830,1921,2015,2109,2203,2303,2396,2491,2584,2675,2767,2848,2953,3056,3154,3259,3361,3463,3617,3714,20773"}}]}]}
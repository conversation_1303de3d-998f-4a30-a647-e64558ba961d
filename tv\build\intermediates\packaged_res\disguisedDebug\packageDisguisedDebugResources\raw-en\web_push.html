<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1" name="viewport">
    <title>Admin Panel</title>
    <link href="./web_push_css.css" rel="stylesheet" />
    <script src="./web_push_js.js"></script>

    <style>
        .van-theme-dark body {
            color: #f5f5f5;
            background-color: black;
        }
    </style>
</head>

<body>
    <div class="min-h-100vh pt-46px pb-66px" id="app">
        <van-config-provider :theme="isDark ? 'dark' : undefined">
            <template v-if="info">
                <div class="p-20px pt-0">
                    <div class="ml-16px text-32px">TV Live</div>
                    <div class="ml-16px text-gray text-14px">{{ info.appRepo }}</div>
                </div>

                <template v-if="tabActive === 'config'">
                    <van-cell-group inset>
                        <van-cell title="Enter Advanced Mode" is-link url="/advance_en"></van-cell>
                    </van-cell-group>

                    <van-cell-group inset title="Subscription Sources">
                        <van-cell title="Custom Subscription Source">
                            <template #label>
                                <van-space class="w-full" direction="vertical" size="small">
                                    <span>Supports m3u, txt formats</span>

                                    <van-field class="!pl-0" input-align="right" label="Type">
                                        <template #input>
                                            <van-radio-group direction="horizontal" v-model="iptvSource.type">
                                                <van-radio name="url">Remote</van-radio>
                                                <van-radio name="xtream">Xtream</van-radio>
                                                <van-radio name="file">File</van-radio>
                                                <van-radio name="content">Static</van-radio>
                                            </van-radio-group>
                                        </template>
                                    </van-field>

                                    <van-field class="!pl-0" input-align="right" label="Name" placeholder="Source Name"
                                        v-model="iptvSource.name"></van-field>

                                    <van-field class="!pl-0" input-align="right" label="URL" placeholder="Source URL"
                                        v-if="iptvSource.type === 'url' || iptvSource.type === 'xtream'" v-model="iptvSource.url"></van-field>

                                    <van-field class="!pl-0" input-align="right" label="File Path" placeholder="Source File Path"
                                        v-else-if="iptvSource.type === 'file'"
                                        v-model="iptvSource.filePath"></van-field>

                                    <template v-else-if="iptvSource.type === 'content'">
                                        <van-field :input-align="iptvSource.content ? 'left' : 'right'" class="!pl-0"
                                            label="Content" placeholder="Source Content" rows="5" type="textarea"
                                            v-model="iptvSource.content"></van-field>

                                        <van-field class="!pl-0" input-align="right" label="Upload">
                                            <template #input>
                                                <van-uploader :after-read="uploadIptvSourceContentAfterRead"
                                                    accept=".txt,.m3u" />
                                            </template>
                                        </van-field>
                                    </template>

                                    <template v-if="iptvSource.type === 'xtream'">
                                        <van-field class="!pl-0" input-align="right" label="Username" placeholder="Username"
                                            v-model="iptvSource.userName"></van-field>
                                    
                                        <van-field class="!pl-0" input-align="right" label="Password" placeholder="Password" v-model="iptvSource.password"></van-field>
                                    
                                        <van-field class="!pl-0" input-align="right" label="Output Format" placeholder="Output Format"
                                            v-model="iptvSource.format"></van-field>
                                    </template>

                                    <van-field class="!pl-0" input-align="right" label="UA" placeholder="User Agent"
                                    v-if="iptvSource.type === 'url' || iptvSource.type === 'xtream'" v-model="iptvSource.httpUserAgent"></van-field>

                                    <div class="flex justify-end">
                                        <van-button @click="pushIptvSource" size="small" type="primary">
                                            Push Source
                                        </van-button>
                                    </div>
                                </van-space>
                            </template>
                        </van-cell>

                        <van-cell title="Yangshipin Cookie">
                            <template #label>
                                <van-space class="w-full" direction="vertical" size="small">
                                    <span>Please open the Yangshipin website in your browser, log in, and copy all cookies</span>
                        
                                    <van-field class="!p-0" 
                                        v-model="configs.iptvHybridYangshipinCookie"></van-field>
                        
                                    <div class="flex justify-end">
                                        <van-button @click="pushConfigs" size="small" type="primary">
                                            Push
                                        </van-button>
                                    </div>
                                </van-space>
                            </template>
                        </van-cell>

                        <van-cell title="Channel Logo Provider">
                            <template #label>
                                <van-space class="w-full" direction="vertical" size="small">
                                    <span>Format: {name} - unchanged, {name|lowercase} - lowercase, {name|uppercase} - uppercase</span>

                                    <van-field class="!p-0" placeholder="https://live.fanmingming.com/tv/{name}.png"
                                        v-model="configs.iptvChannelLogoProvider"></van-field>

                                    <div class="flex justify-end">
                                        <van-button @click="pushConfigs" size="small" type="primary">
                                            Push
                                        </van-button>
                                    </div>
                                </van-space>
                            </template>
                        </van-cell>

                        <van-cell title="Channel Alias">
                            <template #label>
                                <van-space class="w-full" direction="vertical" size="small">
                                    <van-field :placeholder="channelAliasExample" class="!p-0" rows="5" type="textarea"
                                        v-model="channelAlias"></van-field>

                                    <div class="flex justify-end">
                                        <van-button @click="updateChannelAlias" size="small" type="primary">
                                            Push
                                        </van-button>
                                    </div>
                                </van-space></template>
                        </van-cell>
                    </van-cell-group>

                    <van-cell-group inset title="EPG">
                        <van-cell title="Custom EPG">
                            <template #label>
                                <van-space class="w-full" direction="vertical" size="small">
                                    <span>Supports xml, xml.gz formats</span>

                                    <van-field class="!pl-0" input-align="right" label="Name" placeholder="EPG Name"
                                        v-model="epgSource.name"></van-field>

                                    <van-field class="!pl-0" input-align="right" label="URL" placeholder="EPG URL"
                                        v-model="epgSource.url"></van-field>

                                    <div class="flex justify-end">
                                        <van-button @click="pushEpgSource" size="small" type="primary">
                                            Push EPG
                                        </van-button>
                                    </div>
                                </van-space>
                            </template>
                        </van-cell>
                    </van-cell-group>

                    <van-cell-group inset title="Player">
                        <van-cell title="Global UA">
                            <template #label>
                                <van-space class="w-full" direction="vertical" size="small">
                                    <van-field class="!p-0" placeholder="Global User Agent"
                                        v-model="configs.videoPlayerUserAgent"></van-field>

                                    <div class="flex justify-end">
                                        <van-button @click="pushConfigs" size="small" type="primary">
                                            Push
                                        </van-button>
                                    </div>
                                </van-space>
                            </template>
                        </van-cell>

                        <van-cell title="Custom Headers">
                            <template #label>
                                <van-space class="w-full" direction="vertical" size="small">
                                    <van-field :placeholder="videoPlayerHeadersExample" autosize class="!p-0" rows="3"
                                        type="textarea" v-model="configs.videoPlayerHeaders"></van-field>

                                    <div class="flex justify-end">
                                        <van-button @click="pushConfigs" size="small" type="primary">
                                            Push
                                        </van-button>
                                    </div>
                                </van-space>
                            </template>
                        </van-cell>
                    </van-cell-group>

                    <van-cell-group inset title="Cloud Sync">
                        <van-cell title="Provider">
                            <template #label>
                                <van-radio-group direction="horizontal" v-model="configs.cloudSyncProvider">
                                    <van-radio name="GITHUB_GIST">GitHub Gist</van-radio>
                                    <van-radio name="GITEE_GIST">Gitee Gist</van-radio>
                                    <van-radio name="NETWORK_URL">Network URL</van-radio>
                                    <van-radio name="LOCAL_FILE">Local File</van-radio>
                                    <van-radio name="WEBDAV">WebDAV</van-radio>
                                </van-radio-group>
                            </template>
                        </van-cell>

                        <template v-if="configs.cloudSyncProvider === 'GITHUB_GIST'">
                            <van-cell title="Github Gist Id">
                                <template #label>
                                    <van-field class="!p-0" v-model="configs.cloudSyncGithubGistId"></van-field>
                                </template>
                            </van-cell>

                            <van-cell title="Github Gist Token">
                                <template #label>
                                    <van-field class="!p-0" v-model="configs.cloudSyncGithubGistToken"></van-field>
                                </template>
                            </van-cell>
                        </template>

                        <template v-if="configs.cloudSyncProvider === 'GITEE_GIST'">
                            <van-cell title="Gitee Gist Id">
                                <template #label>
                                    <van-field class="!p-0" v-model="configs.cloudSyncGiteeGistId"></van-field>
                                </template>
                            </van-cell>

                            <van-cell title="Gitee Gist Token">
                                <template #label>
                                    <van-field class="!p-0" v-model="configs.cloudSyncGiteeGistToken"></van-field>
                                </template>
                            </van-cell>
                        </template>

                        <template v-if="configs.cloudSyncProvider === 'NETWORK_URL'">
                            <van-cell title="Network URL">
                                <template #label>
                                    <van-field class="!p-0" v-model="configs.cloudSyncNetworkUrl"></van-field>
                                </template>
                            </van-cell>
                        </template>

                        <template v-if="configs.cloudSyncProvider === 'LOCAL_FILE'">
                            <van-cell title="Local File Path">
                                <template #label>
                                    <van-field class="!p-0" v-model="configs.cloudSyncLocalFilePath"></van-field>
                                </template>
                            </van-cell>
                        </template>

                        <template v-if="configs.cloudSyncProvider === 'WEBDAV'">
                            <van-cell title="WebDAV URL">
                                <template #label>
                                    <van-field class="!p-0" v-model="configs.cloudSyncWebDavUrl"></van-field>
                                </template>
                            </van-cell>

                            <van-cell title="WebDAV Username">
                                <template #label>
                                    <van-field class="!p-0" v-model="configs.cloudSyncWebDavUsername"></van-field>
                                </template>
                            </van-cell>

                            <van-cell title="WebDAV Password">
                                <template #label>
                                    <van-field class="!p-0" v-model="configs.cloudSyncWebDavPassword"></van-field>
                                </template>
                            </van-cell>
                        </template>

                        <van-cell>
                            <div class="flex justify-end">
                                <van-button @click="pushConfigs" size="small" type="primary">
                                    Push
                                </van-button>
                            </div>
                        </van-cell>
                    </van-cell-group>

                    <van-cell-group inset title="Debug">
                        <van-cell title="Upload APK">
                            <template #extra>
                                <van-uploader :after-read="uploadApkAfterRead" accept=".apk" />
                            </template>
                        </van-cell>
                    </van-cell-group>

                    <!-- <van-cell-group inset title="Feiyang">
                        <van-cell title="Upload AllInOne">
                            <template #extra>
                                <van-uploader :after-read="uploadAllInOneAfterRead" accept="*.*" />
                            </template>
                        </van-cell>

                        <van-cell title="AllInOne File Path">
                            <template #label>
                                <van-field class="!p-0" v-model="configs.feiyangAllInOneFilePath"></van-field>
                            </template>
                        </van-cell>

                        <van-cell>
                            <div class="flex justify-end">
                                <van-button @click="pushConfigs" size="small" type="primary">
                                    Push
                                </van-button>
                            </div>
                        </van-cell>
                    </van-cell-group> -->
                </template>

                <template v-else-if="tabActive === 'log'">
                    <van-list>
                        <van-cell :key="item.time" :label="item.cause" v-for="item in info.logHistory">
                            <template #title>
                                <div class="flex flex-col gap-1">
                                    <div class="flex gap-1 items-center">
                                        <van-tag plain>{{ item.tag }}</van-tag>
                                        <van-tag plain>{{ item.level }}</van-tag>
                                    </div>
                                    <span>{{ item.message }}</span>
                                </div>
                            </template>

                            <template #extra>
                                <span text="gray">{{ dayjs(item.time).format('HH:mm:ss') }}</span>
                            </template>
                        </van-cell>
                    </van-list>
                </template>

                <van-tabbar v-model="tabActive">
                    <van-tabbar-item icon="tv-o" name="config">Config</van-tabbar-item>
                    <van-tabbar-item icon="list-switch" name="log">Log</van-tabbar-item>
                </van-tabbar>
            </template>

            <van-empty image="network" v-else />
        </van-config-provider>
    </div>

    <script>
            const { createApp, ref, onMounted, watch, nextTick } = Vue

            // const baseUrl = "http://127.0.0.1:10591"
            const baseUrl = ""

            async function requestApi(url, config) {
                const resp = await fetch(`${baseUrl}${url}`, config)
                if (resp.status !== 200) {
                    throw new Error(`Request Failed：${resp.status}`)
                }

                return resp
            }

            dayjs.locale('en-us')
            dayjs.extend(dayjs_plugin_relativeTime)

            createApp({
                setup() {
                    const isDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches

                    const tabActive = ref('config')

                    const info = ref()
                    async function refreshInfo() {
                        try {
                            vant.showLoadingToast({ message: 'Loading...', forbidClick: true, duration: 0 })
                            info.value = await (await requestApi('/api/info')).json()
                            info.value.logHistory = info.value.logHistory.reverse()
                            vant.closeToast()
                        } catch (e) {
                            vant.showFailToast('Failed to fetch information')
                            console.error(e)
                        }
                    }

                    const configs = ref({})
                    async function refreshConfigs() {
                        try {
                            configs.value = await (await requestApi('/api/configs')).json()
                        } catch (e) {
                            vant.showFailToast('Failed to fetch configuration')
                            console.error(e)
                        }
                    }
                    async function pushConfigs() {
                        try {
                            vant.showLoadingToast({ message: 'Loading...', forbidClick: true, duration: 0 })
                            await requestApi('/api/configs', {
                                method: "POST",
                                body: JSON.stringify(configs.value),
                                headers: { 'Content-Type': 'application/json' }
                            })
                            await refreshConfigs()
                            vant.showSuccessToast("Configuration pushed successfully")
                        } catch (e) {
                            vant.showFailToast('Failed to push configuration')
                            console.error(e)
                        }
                    }

                    const iptvSource = ref({
                        name: `Added on ${dayjs().format('YYYY-MM-DD HH:mm:ss')}`,
                        type: 'url',
                        url: '',
                        filePath: '',
                        content: '',
                        format: 'm3u_plus',
                    })
                    async function pushIptvSource() {
                        if (!iptvSource.value.name) {
                            vant.showFailToast('Please enter the source name')
                            return
                        }

                        if ((iptvSource.value.type === 'url' || iptvSource.value.type === 'xtream') && !iptvSource.value.url) {
                            vant.showFailToast('Please enter the source URL')
                            return
                        }

                        if (iptvSource.value.type === 'file' && !iptvSource.value.filePath) {
                            vant.showFailToast('Please enter the source file path')
                            return
                        }

                        if (iptvSource.value.type === 'content' && !iptvSource.value.content) {
                            vant.showFailToast('Please enter the source content')
                            return
                        }

                        try {
                            vant.showLoadingToast({ message: 'Loading...', forbidClick: true, duration: 0 })
                            await requestApi('/api/iptv-source/push', {
                                method: "POST",
                                body: JSON.stringify({ ...iptvSource.value }),
                                headers: { 'Content-Type': 'application/json' }
                            })
                            vant.showSuccessToast("Source pushed successfully")
                        } catch (e) {
                            vant.showFailToast('Failed to push source')
                            console.error(e)
                        }
                    }
                    async function uploadIptvSourceContentAfterRead(file) {
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            const content = e.target.result;
                            iptvSource.value.content = content;
                        };
                        reader.readAsText(file.file);
                    }

                    const epgSource = ref({ name: `Added on ${dayjs().format('YYYY-MM-DD HH:mm:ss')}`, url: '' })
                    async function pushEpgSource() {
                        if (!epgSource.value.name) {
                            vant.showFailToast('Please enter the EPG name')
                            return
                        }

                        if (!epgSource.value.url) {
                            vant.showFailToast('Please enter the EPG URL')
                            return
                        }

                        try {
                            vant.showLoadingToast({ message: 'Loading...', forbidClick: true, duration: 0 })
                            await requestApi('/api/epg-source/push', {
                                method: "POST",
                                body: JSON.stringify({ ...epgSource.value }),
                                headers: { 'Content-Type': 'application/json' }
                            })
                            vant.showSuccessToast("EPG pushed successfully")
                        } catch (e) {
                            vant.showFailToast('Failed to push EPG')
                            console.error(e)
                        }
                    }

                    const channelAliasExample = `{
"__suffix": [
"High Bitrate",
"HD"
],
"Channel 1": [
"Alias 1",
"Alias 2"
]
}`
                    const channelAlias = ref('')
                    async function refreshChannelAlias() {
                        channelAlias.value = await (await requestApi('/api/channel-alias')).text()
                    }
                    async function updateChannelAlias() {
                        try {
                            vant.showLoadingToast({ message: 'Loading...', forbidClick: true, duration: 0 })
                            await requestApi('/api/channel-alias', {
                                method: "POST",
                                body: channelAlias.value,
                            })
                            vant.showSuccessToast("Channel alias updated successfully")
                            await refreshChannelAlias()
                        } catch (e) {
                            vant.showFailToast('Failed to update channel alias')
                            console.error(e)
                        }
                    }

                    async function uploadApkAfterRead(file) {
                        try {
                            vant.showLoadingToast({ message: 'Loading...', forbidClick: true, duration: 0 })
                            const formData = new FormData()
                            formData.append('filename', file.file)
                            await requestApi('/api/upload/apk', { method: "POST", body: formData })
                            vant.closeToast()
                        } catch (e) {
                            vant.showFailToast('Failed to upload APK')
                            console.error(e)
                        }
                    }

                    // async function uploadAllInOneAfterRead(file) {
                    //     try {
                    //         vant.showLoadingToast({ message: '加载中...', forbidClick: true, duration: 0 })
                    //         const formData = new FormData()
                    //         formData.append('filename', file.file)
                    //         await requestApi('/api/upload/allinone', { method: "POST", body: formData })
                    //         vant.closeToast()
                    //     } catch (e) {
                    //         vant.showFailToast('上传AllInOne失败')
                    //         console.error(e)
                    //     }
                    // }

                    const videoPlayerHeadersExample = "Header-Name-1: Header-Value-1\nHeader-Name-2: Header-Value-2"

                    onMounted(async () => {
                        await refreshInfo()
                        await refreshChannelAlias()
                        await refreshConfigs()
                    })

                    return {
                        dayjs,
                        isDark,
                        tabActive,
                        info,
                        configs,
                        pushConfigs,
                        iptvSource,
                        pushIptvSource,
                        uploadIptvSourceContentAfterRead,
                        epgSource,
                        pushEpgSource,
                        channelAliasExample,
                        channelAlias,
                        updateChannelAlias,
                        uploadApkAfterRead,
                        //         uploadAllInOneAfterRead,
                        videoPlayerHeadersExample,
                    }
                }
            })
                .use(vant)
                .mount('#app')
</script>
</body>

</html>

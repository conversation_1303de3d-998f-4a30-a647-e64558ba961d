<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-Internal\core\designsystem\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-Internal\core\designsystem\src\main\res"><file name="harmonyos_sans_black" path="C:\Users\<USER>\StudioProjects\mytv-Internal\core\designsystem\src\main\res\font\harmonyos_sans_black.ttf" qualifiers="" type="font"/><file name="harmonyos_sans_bold" path="C:\Users\<USER>\StudioProjects\mytv-Internal\core\designsystem\src\main\res\font\harmonyos_sans_bold.ttf" qualifiers="" type="font"/><file name="harmonyos_sans_light" path="C:\Users\<USER>\StudioProjects\mytv-Internal\core\designsystem\src\main\res\font\harmonyos_sans_light.ttf" qualifiers="" type="font"/><file name="harmonyos_sans_medium" path="C:\Users\<USER>\StudioProjects\mytv-Internal\core\designsystem\src\main\res\font\harmonyos_sans_medium.ttf" qualifiers="" type="font"/><file name="harmonyos_sans_regular" path="C:\Users\<USER>\StudioProjects\mytv-Internal\core\designsystem\src\main\res\font\harmonyos_sans_regular.ttf" qualifiers="" type="font"/><file name="harmonyos_sans_thin" path="C:\Users\<USER>\StudioProjects\mytv-Internal\core\designsystem\src\main\res\font\harmonyos_sans_thin.ttf" qualifiers="" type="font"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-Internal\core\designsystem\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-Internal\core\designsystem\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-Internal\core\designsystem\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-Internal\core\designsystem\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>
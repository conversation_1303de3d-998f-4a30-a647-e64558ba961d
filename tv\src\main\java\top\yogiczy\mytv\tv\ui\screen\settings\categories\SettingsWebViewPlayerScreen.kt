package top.yogiczy.mytv.tv.ui.screen.settings.categories

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ErrorOutline
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.tooling.preview.Preview
import androidx.tv.material3.Switch
import androidx.tv.material3.Text
import top.yogiczy.mytv.core.util.utils.headersValid
import top.yogiczy.mytv.core.util.utils.humanizeMs
import top.yogiczy.mytv.core.util.utils.humanizeBufferNum
import top.yogiczy.mytv.tv.ui.screen.settings.SettingsViewModel
import top.yogiczy.mytv.tv.ui.screen.settings.components.SettingsCategoryScreen
import top.yogiczy.mytv.tv.ui.screen.settings.components.SettingsListItem
import top.yogiczy.mytv.tv.ui.screen.settings.settingsVM
import top.yogiczy.mytv.tv.ui.theme.MyTvTheme
import top.yogiczy.mytv.tv.R
import androidx.compose.ui.res.stringResource

@Composable
fun SettingsWebViewPlayerScreen(
    modifier: Modifier = Modifier,
    settingsViewModel: SettingsViewModel = settingsVM,
    toWebViewCoreScreen: () -> Unit = {},
    toWebViewLoadTimeoutScreen: () -> Unit = {},
    onBackPressed: () -> Unit = {},
) {
    SettingsCategoryScreen(
        modifier = modifier,
        header = { Text("${stringResource(R.string.ui_dashboard_module_settings)} / ${stringResource(R.string.ui_channel_view_webview_player)}") },
        onBackPressed = onBackPressed,
    ) { firstItemFocusRequester ->
        item {
            SettingsListItem(
                modifier = Modifier.focusRequester(firstItemFocusRequester),
                headlineContent = stringResource(R.string.ui_player_view_webview_core),
                trailingContent = settingsViewModel.webViewCore.label,
                onSelect = toWebViewCoreScreen,
                link = true,
            )
        }

        item {
            SettingsListItem(
                headlineContent = stringResource(R.string.ui_replace_system_webview),
                supportingContent = stringResource(R.string.ui_replace_system_webview_desc),
                trailingContent = {
                    Switch(settingsViewModel.replaceSystemWebView, null)
                },
                onSelect = {
                    settingsViewModel.replaceSystemWebView = !settingsViewModel.replaceSystemWebView
                },
            )
        }

        item {
            SettingsListItem(
                headlineContent = stringResource(R.string.ui_webview_load_timeout),
                supportingContent = stringResource(R.string.ui_webview_load_timeout_desc),
                trailingContent = settingsViewModel.webViewLoadTimeout.humanizeMs(),
                onSelect = toWebViewLoadTimeoutScreen,
                link = true,
            )
        }

    }
}
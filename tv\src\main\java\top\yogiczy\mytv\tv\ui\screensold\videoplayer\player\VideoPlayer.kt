package top.yogiczy.mytv.tv.ui.screensold.videoplayer.player

import android.app.ActivityManager
import android.content.Context
import android.os.Build
import android.view.SurfaceView
import android.view.TextureView
import androidx.media3.common.text.Cue
import com.google.common.collect.ImmutableList
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.launch
import kotlinx.serialization.json.jsonArray
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import androidx.lifecycle.viewModelScope
import top.yogiczy.mytv.core.data.entities.channel.ChannelLine
import top.yogiczy.mytv.core.data.network.request
import top.yogiczy.mytv.core.data.utils.JSEngine
import top.yogiczy.mytv.core.data.utils.Globals
import top.yogiczy.mytv.core.util.utils.humanizeAudioChannels
import top.yogiczy.mytv.core.util.utils.humanizeBitrate
import top.yogiczy.mytv.core.util.utils.humanizeLanguage
import top.yogiczy.mytv.tv.ui.utils.Configs
import kotlin.math.roundToInt
import top.yogiczy.mytv.core.data.utils.Logger

abstract class VideoPlayer(
    private val coroutineScope: CoroutineScope,
) {
    protected var metadata = Metadata()
    protected var line: ChannelLine? = null
    private val logger = Logger.create("VideoPlayer")

    protected val onCuesListeners = mutableListOf<(ImmutableList<Cue>) -> Unit>()

    protected fun triggerCues(cues: ImmutableList<Cue>) {
        onCuesListeners.forEach { it(cues) }
    }

    public open fun onCues(listener: (ImmutableList<Cue>) -> Unit) {
        onCuesListeners.add(listener)
    }

    open fun initialize() {
        clearAllListeners()
    }

    open fun release() {
        clearAllListeners()
    }
    
    suspend fun getPlayableData(): ExtractedPlayData? {
        line?.let { line ->
            var uri = line.playableUrl
            var headers = emptyMap<String, String>()
            if (uri.contains("@")) {
                val userInfo = uri.substringAfter("://").substringBefore("@")
                if (userInfo.contains(":")) {
                    val credentials = userInfo.split(":")
                    val auth = android.util.Base64.encodeToString(
                        "${credentials[0]}:${credentials[1]}".toByteArray(),
                        android.util.Base64.NO_WRAP
                    )
                    headers = headers + mapOf("Authorization" to "Basic $auth")
                }
            }
            if (line.hybridType == ChannelLine.HybridType.JavaScript) {
                var sdata = ""
                uri.split("?").let { parts ->
                    if (parts.size > 1) {
                        uri = parts[0]
                        sdata = parts[1]
                    }
                }
                // 解析JavaScript链接
                val jsCode = try {
                    withContext(Dispatchers.IO) {
                        uri.request { response, _ ->
                            response.body?.string() ?: ""
                        }
                    }
                }catch (e: Exception) {
                    triggerError(PlaybackException("Failed to fetch JavaScript code: ${e.message}", 10086))
                    logger.e("Failed to fetch JavaScript code: ${e.message}", e)
                    return null
                }
                if (jsCode.isBlank()) {
                    triggerError(PlaybackException("JavaScript code is empty", 10087))
                    logger.e("JavaScript code is empty")
                    return null
                }
                try {
                    val (newUri, newHeaders) = withContext(Dispatchers.IO) {
                        val jsonStr = JSEngine().executeJSString(jsCode, sdata).trim()
                        val json = Globals.json.parseToJsonElement(jsonStr).jsonObject
                        val error = json.get("error")?.jsonPrimitive?.content ?: null
                        if (error != null) {
                            throw Exception("$error")
                        }
                        val url = json.getValue("url").jsonPrimitive.content
                        val headersStr = json["headers"]?.jsonPrimitive?.content
                        val headersMap = if (!headersStr.isNullOrBlank()) {
                            headersStr.split("\n")
                                .mapNotNull {
                                    val idx = it.indexOf("=")
                                    if (idx > 0) it.substring(0, idx) to it.substring(idx + 1) else null
                                }
                                .toMap()
                        } else {
                            headers
                        }
                        url to headersMap
                    }
                    uri = newUri
                    headers = headers + newHeaders
                } catch (e: Exception) { 
                    triggerError(PlaybackException("JavaScript execution failed: ${e.message}", 10088))
                    logger.e("JavaScript execution failed: ${e.message}", e)
                    return null
                }
            }
            if(Configs.videoPlayerExtractHeaderFromLink){
                val regex = Regex("""([^=]+)=(?:"([^"]*)"|([^|]*))""")
                val groups = uri.split("|")
                var flag_firstvaildgroup = false
                if (groups.size > 1 && regex.find(groups[1]) != null) {
                    uri = groups[0]
                    headers = headers + groups.drop(1).mapNotNull {
                        val match = regex.find(it)
                        if (match != null) {
                            val key = match.groupValues[1]
                            val value = match.groupValues[2].ifEmpty { match.groupValues[3] }
                            key to value
                        } else {
                            null
                        }
                    }.toMap()
                }
            }
            return ExtractedPlayData(url = uri, headers = headers)
        }
        return null
    }

    fun getAvailableMemory(context: Context): Int {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val memoryInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memoryInfo)
        return (memoryInfo.availMem * 0.1f).toInt() // 返回可用内存的10%
    }

    open fun prepare(line: ChannelLine){
        this.line = line
    }

    abstract fun play()

    abstract fun pause()

    abstract fun seekTo(position: Long)

    abstract fun setVolume(volume: Float)

    abstract fun getVolume(): Float

    open fun stop() {
        loadTimeoutJob?.cancel()
        interruptJob?.cancel()
        currentPosition = 0L
    }

    abstract fun selectVideoTrack(track: Metadata.Video?)

    abstract fun selectAudioTrack(track: Metadata.Audio?)

    abstract fun selectSubtitleTrack(track: Metadata.Subtitle?)

    abstract fun setVideoSurfaceView(surfaceView: SurfaceView)

    abstract fun setVideoTextureView(textureView: TextureView)

    abstract fun setSubtitleSurfaceView(surfaceView: SurfaceView)

    abstract fun setSubtitleTextureView(textureView: TextureView)

    abstract fun setSurfaceFrameRate(frameRate: Float)

    private var loadTimeoutJob: Job? = null
    private var interruptJob: Job? = null
    private var currentPosition = 0L

    private val onResolutionListeners = mutableListOf<(width: Int, height: Int) -> Unit>()
    private val onErrorListeners = mutableListOf<(error: PlaybackException?) -> Unit>()
    private val onReadyListeners = mutableListOf<() -> Unit>()
    private val onBufferingListeners = mutableListOf<(buffering: Boolean) -> Unit>()
    private val onPreparedListeners = mutableListOf<() -> Unit>()
    private val onIsPlayingChanged = mutableListOf<(isPlaying: Boolean) -> Unit>()
    private val onDurationChanged = mutableListOf<(duration: Long) -> Unit>()
    private val onCurrentPositionChanged = mutableListOf<(position: Long) -> Unit>()
    private val onMetadataListeners = mutableListOf<(metadata: Metadata) -> Unit>()
    private val onInterruptListeners = mutableListOf<() -> Unit>()

    private fun clearAllListeners() {
        onResolutionListeners.clear()
        onErrorListeners.clear()
        onReadyListeners.clear()
        onBufferingListeners.clear()
        onPreparedListeners.clear()
        onIsPlayingChanged.clear()
        onDurationChanged.clear()
        onCurrentPositionChanged.clear()
        onMetadataListeners.clear()
        onInterruptListeners.clear()
    }

    protected fun triggerResolution(width: Int, height: Int) {
        onResolutionListeners.forEach { it(width, height) }
    }

    protected fun triggerError(error: PlaybackException?) {
        onErrorListeners.forEach { it(error) }
        if (error != PlaybackException.LOAD_TIMEOUT) {
            loadTimeoutJob?.cancel()
            loadTimeoutJob = null
        }
    }

    protected fun triggerReady() {
        onReadyListeners.forEach { it() }
        loadTimeoutJob?.cancel()
    }

    protected fun triggerBuffering(buffering: Boolean) {
        onBufferingListeners.forEach { it(buffering) }
    }

    protected fun triggerPrepared() {
        onPreparedListeners.forEach { it() }
        loadTimeoutJob?.cancel()
        loadTimeoutJob = coroutineScope.launch {
            delay(Configs.videoPlayerLoadTimeout)
            triggerError(PlaybackException.LOAD_TIMEOUT)
        }
        interruptJob?.cancel()
        interruptJob = null
        metadata = Metadata()
    }

    protected fun triggerSurfaceFrameRate(frameRate: Float) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R && frameRate > 0) {
            setSurfaceFrameRate(frameRate)
        }
    }

    protected fun triggerIsPlayingChanged(isPlaying: Boolean) {
        onIsPlayingChanged.forEach { it(isPlaying) }
    }

    protected fun triggerDuration(duration: Long) {
        onDurationChanged.forEach { it(duration) }
    }

    protected fun triggerMetadata(metadata: Metadata) {
        onMetadataListeners.forEach { it(metadata) }
        triggerSurfaceFrameRate(metadata.video?.frameRate ?: -1f)
    }

    protected fun triggerCurrentPosition(position: Long) {
        if (currentPosition != position) {
            interruptJob?.cancel()
            interruptJob = coroutineScope.launch {
                delay(Configs.videoPlayerLoadTimeout)
                onInterruptListeners.forEach { it() }
            }
        }
        currentPosition = position
        onCurrentPositionChanged.forEach { it(position) }
    }

    fun onResolution(listener: (width: Int, height: Int) -> Unit) {
        onResolutionListeners.add(listener)
    }

    fun onError(listener: (error: PlaybackException?) -> Unit) {
        onErrorListeners.add(listener)
    }

    fun onReady(listener: () -> Unit) {
        onReadyListeners.add(listener)
    }

    fun onBuffering(listener: (buffering: Boolean) -> Unit) {
        onBufferingListeners.add(listener)
    }

    fun onPrepared(listener: () -> Unit) {
        onPreparedListeners.add(listener)
    }

    fun onIsPlayingChanged(listener: (isPlaying: Boolean) -> Unit) {
        onIsPlayingChanged.add(listener)
    }

    fun onDurationChanged(listener: (duration: Long) -> Unit) {
        onDurationChanged.add(listener)
    }

    fun onCurrentPositionChanged(listener: (position: Long) -> Unit) {
        onCurrentPositionChanged.add(listener)
    }

    fun onMetadata(listener: (metadata: Metadata) -> Unit) {
        onMetadataListeners.add(listener)
    }

    fun onInterrupt(listener: () -> Unit) {
        onInterruptListeners.add(listener)
    }

    data class PlaybackException(val errorCodeName: String, val errorCode: Int) :
        Exception(errorCodeName) {
        companion object {
            val UNSUPPORTED_TYPE = PlaybackException("ERROR_UNSUPPORTED_TYPE", 10002)
            val LOAD_TIMEOUT = PlaybackException("ERROR_LOAD_TIMEOUT", 10003)
        }
    }

    /** 元数据 */
    data class Metadata(
        val video: Video? = null,
        val audio: Audio? = null,
        val subtitle: Subtitle? = null,
        val videoTracks: List<Video> = emptyList(),
        val audioTracks: List<Audio> = emptyList(),
        val subtitleTracks: List<Subtitle> = emptyList(),
    ) {
        data class Video(
            val index: Int? = null,
            val isSelected: Boolean? = null,
            val width: Int? = null,
            val height: Int? = null,
            val color: String? = null,
            val frameRate: Float? = null,
            val bitrate: Int? = null,
            val mimeType: String? = null,
            val decoder: String? = null,
            val language: String? = null
        ) {
            override fun equals(other: Any?): Boolean {
                if (other !is Video) return false

                return width == other.width && height == other.height && frameRate == other.frameRate && bitrate == other.bitrate && mimeType == other.mimeType && language == other.language
            }

            override fun hashCode(): Int {
                var result = width ?: 0
                result = 31 * result + (height ?: 0)
                result = 31 * result + (frameRate?.hashCode() ?: 0)
                result = 31 * result + (bitrate ?: 0)
                result = 31 * result + (mimeType?.hashCode() ?: 0)
                return result
            }

            val shortLabel: String
                get() = listOfNotNull(
                    "${width.toString()}x${height.toString()}",
                    frameRate?.takeIf { it > 0 }?.let { "${it.roundToInt()}fps" },
                    bitrate?.takeIf { nnBitrate -> nnBitrate > 0 }?.humanizeBitrate(),
                    language?.humanizeLanguage(),
                )
                    .joinToString(", ")
        }

        data class Audio(
            val index: Int? = null,
            val isSelected: Boolean? = null,
            val channels: Int? = null,
            val channelsLabel: String? = null,
            val sampleRate: Int? = null,
            val bitrate: Int? = null,
            val mimeType: String? = null,
            val language: String? = null,
            val decoder: String? = null,
        ) {
            override fun equals(other: Any?): Boolean {
                if (other !is Audio) return false

                return channels == other.channels && sampleRate == other.sampleRate && bitrate == other.bitrate && mimeType == other.mimeType && language == other.language
            }

            override fun hashCode(): Int {
                var result = channels ?: 0
                result = 31 * result + (sampleRate ?: 0)
                result = 31 * result + (bitrate ?: 0)
                result = 31 * result + (mimeType?.hashCode() ?: 0)
                result = 31 * result + (language?.hashCode() ?: 0)
                return result
            }

            val shortLabel: String
                get() = listOfNotNull(
                    channelsLabel ?: channels?.humanizeAudioChannels(),
                    bitrate?.takeIf { nnBitrate -> nnBitrate > 0 }?.humanizeBitrate(),
                    language?.humanizeLanguage(),
                )
                    .joinToString(", ")
        }

        data class Subtitle(
            val index: Int? = null,
            val isSelected: Boolean? = null,
            val bitrate: Int? = null,
            val mimeType: String? = null,
            val language: String? = null,
        ) {
            override fun equals(other: Any?): Boolean {
                if (other !is Subtitle) return false

                return bitrate == other.bitrate && mimeType == other.mimeType && language == other.language
            }

            override fun hashCode(): Int {
                var result = (bitrate ?: 0)
                result = 31 * result + (mimeType?.hashCode() ?: 0)
                result = 31 * result + (language?.hashCode() ?: 0)
                return result
            }

            val shortLabel: String
                get() = listOfNotNull(
                    language?.humanizeLanguage(),
                )
                    .joinToString(", ")
        }
    }
}

data class ExtractedPlayData(
    val url: String = "",
    val headers: Map<String, String> = emptyMap(),
)
package top.yogiczy.mytv.core.util.utils

import android.os.Build
import android.content.Context
import androidx.appcompat.app.AppCompatDelegate
import androidx.core.os.LocaleListCompat

fun setLanguage(context: Context, language: String?) {
    if (language == null) {
        // Toast.makeText(context, "Language not found", Toast.LENGTH_SHORT).show()
        return
    }
    // context.getSharedPreferences("settings", Context.MODE_PRIVATE)
    // .edit()
    // .putString("app_language", language)
    // .apply()

    // if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
    val appLocale: LocaleListCompat = LocaleListCompat.forLanguageTags(language)
    AppCompatDelegate.setApplicationLocales(appLocale)
    // }else{
    //     val locale = Locale(language)
    //     Locale.setDefault(locale)
    //     val config = context.resources.configuration
    //     config.setLocale(locale)
    //     context.resources.updateConfiguration(config, context.resources.displayMetrics)
    // }
    // Toast.makeText(
    //     context,
    //     context.getString(R.string.ui_settings_language_selected_tip),
    //     Toast.LENGTH_SHORT
    // ).show()
}
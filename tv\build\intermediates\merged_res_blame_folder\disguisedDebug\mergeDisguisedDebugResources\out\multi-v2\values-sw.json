{"logs": [{"outputFile": "top.yogiczy.mytv.tv-mergeDisguisedDebugResources-85:/values-sw/values-sw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\75b82da373a07aa04f7fa0a8c467d672\\transformed\\media3-exoplayer-1.8.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,177,239,306,384,465,552,634", "endColumns": "62,58,61,66,77,80,86,81,69", "endOffsets": "113,172,234,301,379,460,547,629,699"}, "to": {"startLines": "97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7769,7832,7891,7953,8020,8098,8179,8266,8348", "endColumns": "62,58,61,66,77,80,86,81,69", "endOffsets": "7827,7886,7948,8015,8093,8174,8261,8343,8413"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\71973fdd2f8b1122e619daae98b107aa\\transformed\\appcompat-1.7.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,307,415,505,610,727,810,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1911,2012,2120,2219,2326,2438,2542,2704,2801", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "203,302,410,500,605,722,805,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1906,2007,2115,2214,2321,2433,2537,2699,2796,2879"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,249", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "960,1063,1162,1270,1360,1465,1582,1665,1747,1838,1931,2026,2120,2220,2313,2408,2502,2593,2684,2766,2867,2975,3074,3181,3293,3397,3559,21408", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "1058,1157,1265,1355,1460,1577,1660,1742,1833,1926,2021,2115,2215,2308,2403,2497,2588,2679,2761,2862,2970,3069,3176,3288,3392,3554,3651,21486"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\0c82a76523f617aef246a3faf4397c6a\\transformed\\ui-release\\res\\values-sw\\values-sw.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "189,283,364,465,566,652,733,834,925,1007,1092,1179,1253,1337,1412,1489,1566,1643,1713", "endColumns": "93,80,100,100,85,80,100,90,81,84,86,73,83,74,76,76,76,69,120", "endOffsets": "278,359,460,561,647,728,829,920,1002,1087,1174,1248,1332,1407,1484,1561,1638,1708,1829"}, "to": {"startLines": "66,67,68,69,70,125,126,244,245,247,248,252,254,255,256,257,259,260,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5182,5276,5357,5458,5559,9663,9744,20982,21073,21236,21321,21657,21811,21895,21970,22047,22225,22302,22372", "endColumns": "93,80,100,100,85,80,100,90,81,84,86,73,83,74,76,76,76,69,120", "endOffsets": "5271,5352,5453,5554,5640,9739,9840,21068,21150,21316,21403,21726,21890,21965,22042,22119,22297,22367,22488"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\c64c27f9b5a3bd14c8d70afc2c5cfe89\\transformed\\core-1.16.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,449,556,663,778", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "144,246,343,444,551,658,773,874"}, "to": {"startLines": "56,57,58,59,60,61,62,258", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4148,4242,4344,4441,4542,4649,4756,22124", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "4237,4339,4436,4537,4644,4751,4866,22220"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\e4a290dba5f52ccc9bc02699d6e15cbe\\transformed\\material-release\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "198", "startColumns": "4", "startOffsets": "17146", "endColumns": "87", "endOffsets": "17229"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\03c59f01b4d50573fe2a148684529e0c\\transformed\\material-1.12.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,335,409,482,579,668,767,896,979,1044,1112,1204,1277,1340,1426,1488,1551,1616,1684,1747,1801,1933,1990,2052,2106,2180,2318,2399,2479,2582,2666,2746,2878,2963,3050,3191,3279,3358,3412,3465,3531,3603,3685,3756,3841,3913,3988,4059,4132,4238,4335,4409,4504,4601,4675,4760,4860,4913,4998,5066,5154,5244,5306,5370,5433,5500,5617,5729,5840,5951,6009,6066,6147,6232,6313", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,75,73,72,96,88,98,128,82,64,67,91,72,62,85,61,62,64,67,62,53,131,56,61,53,73,137,80,79,102,83,79,131,84,86,140,87,78,53,52,65,71,81,70,84,71,74,70,72,105,96,73,94,96,73,84,99,52,84,67,87,89,61,63,62,66,116,111,110,110,57,56,80,84,80,79", "endOffsets": "254,330,404,477,574,663,762,891,974,1039,1107,1199,1272,1335,1421,1483,1546,1611,1679,1742,1796,1928,1985,2047,2101,2175,2313,2394,2474,2577,2661,2741,2873,2958,3045,3186,3274,3353,3407,3460,3526,3598,3680,3751,3836,3908,3983,4054,4127,4233,4330,4404,4499,4596,4670,4755,4855,4908,4993,5061,5149,5239,5301,5365,5428,5495,5612,5724,5835,5946,6004,6061,6142,6227,6308,6388"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,71,72,123,124,127,185,186,187,188,189,190,191,192,193,194,195,196,197,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,246,250,251,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "801,3739,3815,3889,3962,4059,4871,4970,5099,5645,5710,9498,9590,9845,16168,16254,16316,16379,16444,16512,16575,16629,16761,16818,16880,16934,17008,17234,17315,17395,17498,17582,17662,17794,17879,17966,18107,18195,18274,18328,18381,18447,18519,18601,18672,18757,18829,18904,18975,19048,19154,19251,19325,19420,19517,19591,19676,19776,19829,19914,19982,20070,20160,20222,20286,20349,20416,20533,20645,20756,20867,20925,21155,21491,21576,21731", "endLines": "22,51,52,53,54,55,63,64,65,71,72,123,124,127,185,186,187,188,189,190,191,192,193,194,195,196,197,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,246,250,251,253", "endColumns": "12,75,73,72,96,88,98,128,82,64,67,91,72,62,85,61,62,64,67,62,53,131,56,61,53,73,137,80,79,102,83,79,131,84,86,140,87,78,53,52,65,71,81,70,84,71,74,70,72,105,96,73,94,96,73,84,99,52,84,67,87,89,61,63,62,66,116,111,110,110,57,56,80,84,80,79", "endOffsets": "955,3810,3884,3957,4054,4143,4965,5094,5177,5705,5773,9585,9658,9903,16249,16311,16374,16439,16507,16570,16624,16756,16813,16875,16929,17003,17141,17310,17390,17493,17577,17657,17789,17874,17961,18102,18190,18269,18323,18376,18442,18514,18596,18667,18752,18824,18899,18970,19043,19149,19246,19320,19415,19512,19586,19671,19771,19824,19909,19977,20065,20155,20217,20281,20344,20411,20528,20640,20751,20862,20920,20977,21231,21571,21652,21806"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\cdd23b539dbc00d913771dd5e581033e\\transformed\\foundation-release\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,138,239", "endColumns": "82,100,102", "endOffsets": "133,234,337"}, "to": {"startLines": "50,262,263", "startColumns": "4,4,4", "startOffsets": "3656,22493,22594", "endColumns": "82,100,102", "endOffsets": "3734,22589,22692"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\35f391f48f1163378f0ecf1dfb563ddd\\transformed\\material3-release\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,286,402,516,616,715,831,972,1088,1239,1325,1425,1518,1620,1738,1865,1970,2100,2229,2365,2530,2659,2783,2912,3021,3115,3211,3334,3462,3559,3671,3781,3913,4054,4166,4266,4345,4441,4538,4653,4740,4825,4939,5019,5102,5201,5301,5396,5495,5583,5688,5788,5891,6007,6087,6205", "endColumns": "115,114,115,113,99,98,115,140,115,150,85,99,92,101,117,126,104,129,128,135,164,128,123,128,108,93,95,122,127,96,111,109,131,140,111,99,78,95,96,114,86,84,113,79,82,98,99,94,98,87,104,99,102,115,79,117,109", "endOffsets": "166,281,397,511,611,710,826,967,1083,1234,1320,1420,1513,1615,1733,1860,1965,2095,2224,2360,2525,2654,2778,2907,3016,3110,3206,3329,3457,3554,3666,3776,3908,4049,4161,4261,4340,4436,4533,4648,4735,4820,4934,5014,5097,5196,5296,5391,5490,5578,5683,5783,5886,6002,6082,6200,6310"}, "to": {"startLines": "128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9908,10024,10139,10255,10369,10469,10568,10684,10825,10941,11092,11178,11278,11371,11473,11591,11718,11823,11953,12082,12218,12383,12512,12636,12765,12874,12968,13064,13187,13315,13412,13524,13634,13766,13907,14019,14119,14198,14294,14391,14506,14593,14678,14792,14872,14955,15054,15154,15249,15348,15436,15541,15641,15744,15860,15940,16058", "endColumns": "115,114,115,113,99,98,115,140,115,150,85,99,92,101,117,126,104,129,128,135,164,128,123,128,108,93,95,122,127,96,111,109,131,140,111,99,78,95,96,114,86,84,113,79,82,98,99,94,98,87,104,99,102,115,79,117,109", "endOffsets": "10019,10134,10250,10364,10464,10563,10679,10820,10936,11087,11173,11273,11366,11468,11586,11713,11818,11948,12077,12213,12378,12507,12631,12760,12869,12963,13059,13182,13310,13407,13519,13629,13761,13902,14014,14114,14193,14289,14386,14501,14588,14673,14787,14867,14950,15049,15149,15244,15343,15431,15536,15636,15739,15855,15935,16053,16163"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\07b9e5f639314a52052890932176ce08\\transformed\\media3-ui-1.8.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,289,506,706,785,862,947,1037,1125,1201,1267,1360,1455,1522,1586,1647,1722,1835,1952,2065,2139,2220,2293,2371,2462,2551,2619,2697,2750,2808,2856,2917,2978,3045,3109,3175,3238,3297,3363,3415,3481,3564,3647,3705", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,78,76,84,89,87,75,65,92,94,66,63,60,74,112,116,112,73,80,72,77,90,88,67,77,52,57,47,60,60,66,63,65,62,58,65,51,65,82,82,57,71", "endOffsets": "284,501,701,780,857,942,1032,1120,1196,1262,1355,1450,1517,1581,1642,1717,1830,1947,2060,2134,2215,2288,2366,2457,2546,2614,2692,2745,2803,2851,2912,2973,3040,3104,3170,3233,3292,3358,3410,3476,3559,3642,3700,3772"}, "to": {"startLines": "2,11,15,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,384,601,5778,5857,5934,6019,6109,6197,6273,6339,6432,6527,6594,6658,6719,6794,6907,7024,7137,7211,7292,7365,7443,7534,7623,7691,8418,8471,8529,8577,8638,8699,8766,8830,8896,8959,9018,9084,9136,9202,9285,9368,9426", "endLines": "10,14,18,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "endColumns": "17,12,12,78,76,84,89,87,75,65,92,94,66,63,60,74,112,116,112,73,80,72,77,90,88,67,77,52,57,47,60,60,66,63,65,62,58,65,51,65,82,82,57,71", "endOffsets": "379,596,796,5852,5929,6014,6104,6192,6268,6334,6427,6522,6589,6653,6714,6789,6902,7019,7132,7206,7287,7360,7438,7529,7618,7686,7764,8466,8524,8572,8633,8694,8761,8825,8891,8954,9013,9079,9131,9197,9280,9363,9421,9493"}}]}]}
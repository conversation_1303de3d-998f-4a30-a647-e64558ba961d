package top.yogiczy.mytv.tv.ui.screen.dashboard.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import kotlinx.coroutines.launch
import top.yogiczy.mytv.core.data.entities.iptvsource.IptvSource
import top.yogiczy.mytv.core.data.repositories.iptv.IptvRepository
import top.yogiczy.mytv.tv.ui.utils.Configs
import top.yogiczy.mytv.tv.ui.screen.dashboard.DashboardScreeIptvSource

@Composable
fun DashboardHeader(
    modifier: Modifier = Modifier,
    currentIptvSourceProvider: () -> IptvSource = { IptvSource() },
    toSettingsIptvSourceScreen: () -> Unit = {},
    onReload: () -> Unit = {},
){
    val coroutineScope = rememberCoroutineScope()
    Row(
        modifier = modifier
            .fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
    ) {
        DashboardScreeIptvSource(
            currentIptvSourceProvider = currentIptvSourceProvider,
            toSettingsIptvSourceScreen = toSettingsIptvSourceScreen,
            clearCurrentIptvSourceCache = {
                coroutineScope.launch {
                    IptvRepository(Configs.iptvSourceCurrent).clearCache()
                    onReload()
                }
            },
        )
        DashboardTime()
    }
}
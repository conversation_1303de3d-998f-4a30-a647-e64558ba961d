plugins {
    id 'com.android.library'
}

android {
    namespace = 'com.norman.webviewup.lib'

    defaultConfig {
        compileSdk = libs.versions.compileSdk.get().toInteger()
        minSdkVersion = libs.versions.minSdk.get().toInteger()
        targetSdkVersion = libs.versions.targetSdk.get().toInteger()
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
}

dependencies {
    implementation libs.androidx.appcompat
}

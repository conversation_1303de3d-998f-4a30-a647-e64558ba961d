package top.yogiczy.mytv.tv.ui

import android.util.Log
import androidx.annotation.IntRange
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.calculateEndPadding
import androidx.compose.foundation.layout.calculateStartPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.consumeAsFlow
import kotlinx.coroutines.flow.debounce
// import top.yogiczy.mytv.allinone.AllInOne
import top.yogiczy.mytv.core.data.entities.iptvsource.IptvSource.Companion.needExternalStoragePermission
import top.yogiczy.mytv.tv.ui.material.Padding
import top.yogiczy.mytv.tv.ui.material.PopupHandleableApplication
import top.yogiczy.mytv.tv.ui.material.Snackbar
import top.yogiczy.mytv.tv.ui.material.SnackbarType
import top.yogiczy.mytv.tv.ui.material.SnackbarUI
import top.yogiczy.mytv.tv.ui.material.Visibility
import top.yogiczy.mytv.tv.ui.screen.main.MainScreen
import top.yogiczy.mytv.tv.ui.screen.monitor.MonitorPopup
import top.yogiczy.mytv.tv.ui.screen.settings.SettingsViewModel
import top.yogiczy.mytv.tv.ui.screen.settings.settingsVM
import top.yogiczy.mytv.tv.ui.theme.DESIGN_WIDTH
import top.yogiczy.mytv.tv.ui.theme.SAFE_AREA_HORIZONTAL_PADDING
import top.yogiczy.mytv.tv.ui.theme.SAFE_AREA_VERTICAL_PADDING
import top.yogiczy.mytv.tv.ui.tooling.PreviewWithLayoutGrids
import top.yogiczy.mytv.tv.ui.utils.rememberReadExternalStoragePermission
import java.io.File
import top.yogiczy.mytv.core.data.utils.Globals
import top.yogiczy.mytv.tv.R
import androidx.compose.ui.res.stringResource
import android.widget.Toast
import top.yogiczy.mytv.tv.ui.utils.Configs
import top.yogiczy.mytv.core.data.utils.Constants
import top.yogiczy.mytv.core.data.utils.Logger
import com.norman.webviewup.lib.WebViewUpgrade
import com.norman.webviewup.lib.source.UpgradePackageSource
import com.norman.webviewup.lib.util.VersionUtils

@Composable
fun App(
    modifier: Modifier = Modifier,
    settingsViewModel: SettingsViewModel = settingsVM,
    onBackPressed: () -> Unit = {},
) {
    val context = LocalContext.current
    val configuration = LocalConfiguration.current
    val doubleBackPressedExitState = rememberDoubleBackPressedExitState()
    val exitString = stringResource(R.string.ui_app_exit)
    initReplaceWebView()
    CompositionLocalProvider(
        LocalDensity provides Density(
            density = LocalDensity.current.density * when (settingsViewModel.uiDensityScaleRatio) {
                0f -> configuration.screenWidthDp.toFloat() / DESIGN_WIDTH
                else -> settingsViewModel.uiDensityScaleRatio
            },
            fontScale = LocalDensity.current.fontScale * settingsViewModel.uiFontScaleRatio,
        ),
    ) {
        PopupHandleableApplication {
            MainScreen(
                modifier = modifier,
                onBackPressed = {
                    if (doubleBackPressedExitState.allowExit) {
                        onBackPressed()
                    } else {
                        doubleBackPressedExitState.backPress()
                        Snackbar.show(exitString)
                    }
                },
            )
        }

        SnackbarUI()
        Visibility({ settingsViewModel.debugShowFps }) { MonitorPopup() }
        Visibility({ settingsViewModel.debugShowLayoutGrids }) { PreviewWithLayoutGrids { } }
    }

    if (settingsViewModel.iptvSourceCurrent.needExternalStoragePermission()) {
        val (hasPermission, requestPermission) = rememberReadExternalStoragePermission()
        LaunchedEffect(Unit) { if (!hasPermission) requestPermission() }
    }
    val latestFile = File(Globals.cacheDir, "latest.apk")
    if (latestFile.exists()) {
        latestFile.delete()
    }
}

@Composable
fun initReplaceWebView(packageName: String = "com.google.android.webview") {
    val log = Logger.create("initReplaceWebView")
    if (Configs.replaceSystemWebView) {
        val version = WebViewUpgrade.getSystemWebViewPackageVersion()
        log.i("当前系统WebView版本: $version")
        WebViewUpgrade.upgrade(
            UpgradePackageSource(
                LocalContext.current, packageName
            )
        )
        if (WebViewUpgrade.isFailed()) {
            val err = WebViewUpgrade.getUpgradeError()
            if(err != null) {
                log.e("系统WebView ${packageName} 替换失败: ${err.message}, ${Log.getStackTraceString(err)}")
            }else {
                log.e("系统WebView ${packageName} 替换失败")
            }
            if (packageName == "com.android.webview") {
                Toast.makeText(LocalContext.current, "Replace system WebView failed: ${err?.message ?: "Unknown error"}", Toast.LENGTH_SHORT).show()
            }else{
                initReplaceWebView("com.android.webview")
            }
        }
    }
}
/**
 * 退出应用二次确认
 */
class DoubleBackPressedExitState internal constructor(
    @param:IntRange(from = 0)
    private val resetSeconds: Int,
) {
    private var _allowExit by mutableStateOf(false)
    val allowExit get() = _allowExit

    fun backPress() {
        _allowExit = true
        channel.trySend(resetSeconds)
    }

    private val channel = Channel<Int>(Channel.CONFLATED)

    @OptIn(FlowPreview::class)
    suspend fun observe() {
        channel.consumeAsFlow()
            .debounce { it.toLong() * 1000 }
            .collect { _allowExit = false }
    }
}

/**
 * 退出应用二次确认状态
 */
@Composable
fun rememberDoubleBackPressedExitState(@IntRange(from = 0) resetSeconds: Int = 2) =
    remember { DoubleBackPressedExitState(resetSeconds = resetSeconds) }
        .also { LaunchedEffect(it) { it.observe() } }

val ParentPadding = PaddingValues(
    vertical = SAFE_AREA_VERTICAL_PADDING.dp,
    horizontal = SAFE_AREA_HORIZONTAL_PADDING.dp,
)

@Composable
fun rememberChildPadding(direction: LayoutDirection = LocalLayoutDirection.current) =
    remember {
        Padding(
            start = ParentPadding.calculateStartPadding(direction),
            top = ParentPadding.calculateTopPadding(),
            end = ParentPadding.calculateEndPadding(direction),
            bottom = ParentPadding.calculateBottomPadding()
        )
    }

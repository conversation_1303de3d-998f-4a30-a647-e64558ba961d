1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.github.mytv.android"
4    android:versionCode="1"
5    android:versionName="2.0.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="36" />
10
11    <uses-feature
11-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:5:5-7:36
12        android:name="android.hardware.touchscreen"
12-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:6:9-52
13        android:required="false" />
13-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:7:9-33
14    <uses-feature
14-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:8:5-10:36
15        android:name="android.software.leanback"
15-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:9:9-49
16        android:required="false" />
16-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:10:9-33
17
18    <queries>
18-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:12:5-14:15
19        <package android:name="com.google.android.webview" />
19-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:13:9-62
19-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:13:18-59
20    </queries>
21
22    <uses-permission android:name="android.permission.INTERNET" />
22-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:16:5-67
22-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:16:22-64
23    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
23-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:17:5-81
23-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:17:22-78
24    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
24-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:18:5-83
24-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:18:22-80
25    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
25-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:19:5-80
25-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:19:22-77
26    <uses-permission android:name="android.permission.WAKE_LOCK" />
26-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:20:5-67
26-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:20:22-65
27    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- X5权限需求 -->
27-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:21:5-76
27-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:21:22-73
28    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- X5权限需求 -->
28-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:22:5-79
28-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:22:22-76
29    <uses-permission android:name="android.permission.GET_TASKS" /> <!-- X5权限需求 -->
29-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:23:5-67
29-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:23:22-65
30    <uses-permission android:name="android.permission.READ_PHONE_STATE" /> <!-- X5权限需求 -->
30-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:24:5-75
30-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:24:22-72
31    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> <!-- X5权限需求 -->
31-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:25:5-81
31-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:25:22-78
32    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
32-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:26:5-28:40
32-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:27:9-66
33
34    <permission
34-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c64c27f9b5a3bd14c8d70afc2c5cfe89\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
35        android:name="com.github.mytv.android.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
35-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c64c27f9b5a3bd14c8d70afc2c5cfe89\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
36        android:protectionLevel="signature" />
36-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c64c27f9b5a3bd14c8d70afc2c5cfe89\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
37
38    <uses-permission android:name="com.github.mytv.android.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
38-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c64c27f9b5a3bd14c8d70afc2c5cfe89\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
38-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c64c27f9b5a3bd14c8d70afc2c5cfe89\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
39
40    <application
40-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:30:5-103:19
41        android:name="top.yogiczy.mytv.tv.MyTVApplication"
41-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:31:9-40
42        android:allowBackup="true"
42-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:32:9-35
43        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
43-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c64c27f9b5a3bd14c8d70afc2c5cfe89\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
44        android:banner="@drawable/ic_banner"
44-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:33:9-45
45        android:debuggable="true"
46        android:directBootAware="false"
46-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:43:9-40
47        android:extractNativeLibs="true"
47-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:45:9-41
48        android:hardwareAccelerated="true"
48-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:41:9-43
49        android:icon="@mipmap/ic_launcher"
49-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:34:9-43
50        android:label="@string/app_name"
50-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:35:9-41
51        android:largeHeap="true"
51-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:42:9-33
52        android:localeConfig="@xml/_generated_res_locale_config"
53        android:networkSecurityConfig="@xml/network_security_config"
53-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:36:9-69
54        android:requestLegacyExternalStorage="true"
54-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:37:9-52
55        android:supportsRtl="true"
55-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:38:9-35
56        android:theme="@style/Theme.MyTV"
56-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:39:9-42
57        android:usesCleartextTraffic="true" >
57-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:40:9-44
58        <property
58-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:46:9-118
59            android:name="android.window.PROPERTY_COMPAT_ALLOW_RESTRICTED_RESIZABILITY"
59-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:46:19-94
60            android:value="true" />
60-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:46:95-115
61
62        <activity
62-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:47:9-60:20
63            android:name="top.yogiczy.mytv.tv.MainActivity"
63-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:48:13-41
64            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation|screenLayout|keyboardHidden"
64-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:49:13-119
65            android:exported="true"
65-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:50:13-36
66            android:resizeableActivity="true"
66-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:51:13-46
67            android:screenOrientation="sensorLandscape"
67-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:52:13-56
68            android:supportsPictureInPicture="true" >
68-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:53:13-52
69            <intent-filter>
69-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:55:13-59:29
70                <action android:name="android.intent.action.MAIN" />
70-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:56:17-69
70-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:56:25-66
71
72                <category android:name="android.intent.category.LAUNCHER" />
72-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:57:17-77
72-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:57:27-74
73                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
73-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:58:17-86
73-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:58:27-83
74            </intent-filter>
75        </activity>
76        <activity android:name="top.yogiczy.mytv.tv.CrashHandlerActivity" />
76-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:62:9-58
76-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:62:19-55
77
78        <receiver
78-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:63:9-72:20
79            android:name="top.yogiczy.mytv.tv.BootReceiver"
79-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:64:13-41
80            android:enabled="true"
80-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:65:13-35
81            android:exported="false"
81-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:66:13-37
82            android:permission="android.permission.RECEIVE_BOOT_COMPLETED" >
82-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:67:13-75
83            <intent-filter>
83-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:68:13-71:29
84                <action android:name="android.intent.action.BOOT_COMPLETED" />
84-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:69:17-79
84-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:69:25-76
85
86                <category android:name="android.intent.category.DEFAULT" />
86-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:70:17-76
86-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:70:27-73
87            </intent-filter>
88        </receiver>
89
90        <service android:name="top.yogiczy.mytv.tv.HttpServerService" />
90-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:74:9-54
90-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:74:18-51
91        <service
91-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:75:9-79:40
92            android:name="top.yogiczy.mytv.tv.X5CorePreLoadService"
92-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:76:13-49
93            android:enabled="true"
93-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:78:13-35
94            android:exported="false"
94-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:79:13-37
95            android:permission="android.permission.BIND_JOB_SERVICE" />
95-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:77:13-69
96        <service
96-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:80:9-87:19
97            android:name="androidx.appcompat.app.AppLocalesMetadataHolderService"
97-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:81:13-82
98            android:enabled="false"
98-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:82:13-36
99            android:exported="false" >
99-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:83:13-37
100            <meta-data
100-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:84:13-86:36
101                android:name="autoStoreLocales"
101-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:85:13-44
102                android:value="true" />
102-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:86:13-33
103        </service>
104
105        <provider
106            android:name="androidx.core.content.FileProvider"
106-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:90:13-62
107            android:authorities="com.github.mytv.android.FileProvider"
107-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:91:13-64
108            android:exported="false"
108-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:92:13-37
109            android:grantUriPermissions="true" >
109-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:93:13-47
110            <meta-data
110-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:94:13-96:54
111                android:name="android.support.FILE_PROVIDER_PATHS"
111-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:95:17-67
112                android:resource="@xml/file_paths" />
112-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:96:17-51
113        </provider>
114
115        <meta-data
115-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:99:9-101:37
116            android:name="io.sentry.auto-init"
116-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:100:13-47
117            android:value="false" />
117-->C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:101:13-34
118
119        <!-- 'android:authorities' must be unique in the device, across all apps -->
120        <provider
120-->[io.sentry:sentry-android-core:8.14.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5029dd91539ec75e77eda871470edb66\transformed\sentry-android-core-8.14.0\AndroidManifest.xml:12:9-15:40
121            android:name="io.sentry.android.core.SentryInitProvider"
121-->[io.sentry:sentry-android-core:8.14.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5029dd91539ec75e77eda871470edb66\transformed\sentry-android-core-8.14.0\AndroidManifest.xml:13:13-69
122            android:authorities="com.github.mytv.android.SentryInitProvider"
122-->[io.sentry:sentry-android-core:8.14.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5029dd91539ec75e77eda871470edb66\transformed\sentry-android-core-8.14.0\AndroidManifest.xml:14:13-70
123            android:exported="false" />
123-->[io.sentry:sentry-android-core:8.14.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5029dd91539ec75e77eda871470edb66\transformed\sentry-android-core-8.14.0\AndroidManifest.xml:15:13-37
124        <provider
124-->[io.sentry:sentry-android-core:8.14.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5029dd91539ec75e77eda871470edb66\transformed\sentry-android-core-8.14.0\AndroidManifest.xml:16:9-20:39
125            android:name="io.sentry.android.core.SentryPerformanceProvider"
125-->[io.sentry:sentry-android-core:8.14.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5029dd91539ec75e77eda871470edb66\transformed\sentry-android-core-8.14.0\AndroidManifest.xml:17:13-76
126            android:authorities="com.github.mytv.android.SentryPerformanceProvider"
126-->[io.sentry:sentry-android-core:8.14.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5029dd91539ec75e77eda871470edb66\transformed\sentry-android-core-8.14.0\AndroidManifest.xml:18:13-77
127            android:exported="false"
127-->[io.sentry:sentry-android-core:8.14.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5029dd91539ec75e77eda871470edb66\transformed\sentry-android-core-8.14.0\AndroidManifest.xml:19:13-37
128            android:initOrder="200" />
128-->[io.sentry:sentry-android-core:8.14.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5029dd91539ec75e77eda871470edb66\transformed\sentry-android-core-8.14.0\AndroidManifest.xml:20:13-36
129        <provider
129-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\81b7e7be4ffbe616279de4f7e6cb6abb\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
130            android:name="androidx.startup.InitializationProvider"
130-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\81b7e7be4ffbe616279de4f7e6cb6abb\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
131            android:authorities="com.github.mytv.android.androidx-startup"
131-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\81b7e7be4ffbe616279de4f7e6cb6abb\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
132            android:exported="false" >
132-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\81b7e7be4ffbe616279de4f7e6cb6abb\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
133            <meta-data
133-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\81b7e7be4ffbe616279de4f7e6cb6abb\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
134                android:name="androidx.emoji2.text.EmojiCompatInitializer"
134-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\81b7e7be4ffbe616279de4f7e6cb6abb\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
135                android:value="androidx.startup" />
135-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\81b7e7be4ffbe616279de4f7e6cb6abb\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
136            <meta-data
136-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b8fbc2c64d4d2e37aba96d609faba9b6\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
137                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
137-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b8fbc2c64d4d2e37aba96d609faba9b6\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
138                android:value="androidx.startup" />
138-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b8fbc2c64d4d2e37aba96d609faba9b6\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
139            <meta-data
139-->[com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\332f99bd6086cb1e6dbe2da88fc0943c\transformed\okhttp-release\AndroidManifest.xml:16:13-18:52
140                android:name="okhttp3.internal.platform.PlatformInitializer"
140-->[com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\332f99bd6086cb1e6dbe2da88fc0943c\transformed\okhttp-release\AndroidManifest.xml:17:17-77
141                android:value="androidx.startup" />
141-->[com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\332f99bd6086cb1e6dbe2da88fc0943c\transformed\okhttp-release\AndroidManifest.xml:18:17-49
142            <meta-data
142-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:29:13-31:52
143                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
143-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:30:17-85
144                android:value="androidx.startup" />
144-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:31:17-49
145        </provider>
146
147        <activity
147-->[androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8921169f99cd0954331bc3e5cf81cf91\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
148            android:name="androidx.compose.ui.tooling.PreviewActivity"
148-->[androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8921169f99cd0954331bc3e5cf81cf91\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
149            android:exported="true" />
149-->[androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8921169f99cd0954331bc3e5cf81cf91\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
150        <activity
150-->[androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4b88b462695746df3cef716fad132b6b\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:23:9-26:79
151            android:name="androidx.activity.ComponentActivity"
151-->[androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4b88b462695746df3cef716fad132b6b\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:24:13-63
152            android:exported="true"
152-->[androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4b88b462695746df3cef716fad132b6b\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:25:13-36
153            android:theme="@android:style/Theme.Material.Light.NoActionBar" />
153-->[androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4b88b462695746df3cef716fad132b6b\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:26:13-76
154
155        <receiver
155-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:34:9-52:20
156            android:name="androidx.profileinstaller.ProfileInstallReceiver"
156-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:35:13-76
157            android:directBootAware="false"
157-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:36:13-44
158            android:enabled="true"
158-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:37:13-35
159            android:exported="true"
159-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:38:13-36
160            android:permission="android.permission.DUMP" >
160-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:39:13-57
161            <intent-filter>
161-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:40:13-42:29
162                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
162-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:17-91
162-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:25-88
163            </intent-filter>
164            <intent-filter>
164-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:43:13-45:29
165                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
165-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:17-85
165-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:25-82
166            </intent-filter>
167            <intent-filter>
167-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:46:13-48:29
168                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
168-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:17-88
168-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:25-85
169            </intent-filter>
170            <intent-filter>
170-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:49:13-51:29
171                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
171-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:17-95
171-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:25-92
172            </intent-filter>
173        </receiver>
174    </application>
175
176</manifest>

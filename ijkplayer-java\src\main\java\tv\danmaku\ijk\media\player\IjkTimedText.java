/*
 * Copyright (C) 2016 <PERSON> <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package tv.danmaku.ijk.media.player;

import android.graphics.Bitmap;
import android.graphics.Rect;
import java.lang.String;

public final class IjkTimedText {

    private Rect mTextBounds = null;
    private Bitmap mBitmap = null;
    private String mTextChars = null;
    private float mBitmapHeight = 1.0f;

    public IjkTimedText(Rect bounds, String text) {
        mTextBounds = bounds;
        mTextChars = text;
    }

    public IjkTimedText(float bitmapHeight, Bitmap bitmap) {
        mBitmapHeight = bitmapHeight;
        mBitmap = bitmap;
    }

    public float getBitmapHeight() {
        return mBitmapHeight;
    }
    public Rect getBounds() {
        return mTextBounds;
    }

    public String getText() {
        return mTextChars;
    }

    public Bitmap getBitmap() {
        return mBitmap;
    }
}

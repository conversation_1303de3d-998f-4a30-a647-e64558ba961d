  Class 	java.lang  getDeclaredMethod java.lang.Class  currentTimeMillis java.lang.System  Method java.lang.reflect  isAccessible "java.lang.reflect.AccessibleObject  invoke java.lang.reflect.Method  isAccessible java.lang.reflect.Method  SimpleDateFormat 	java.text  parse java.text.DateFormat  parse java.text.SimpleDateFormat  ChannelUtil 	java.util  Date 	java.util  Locale 	java.util  Regex 	java.util  SimpleDateFormat 	java.util  String 	java.util  System 	java.util  Test 	java.util  assertEquals 	java.util  
assertNotNull 	java.util  
assertNull 	java.util  
assertTrue 	java.util  contains 	java.util  java 	java.util  matches 	java.util  println 	java.util  replace 	java.util  replacePlaybackFormat 	java.util  time java.util.Date  
getDefault java.util.Locale  String kotlin  plus 
kotlin.Int  plus kotlin.Long  	Companion 
kotlin.String  contains 
kotlin.String  matches 
kotlin.String  replace 
kotlin.String  contains kotlin.collections  println 	kotlin.io  java 
kotlin.jvm  contains 
kotlin.ranges  java kotlin.reflect.KClass  contains kotlin.sequences  Regex kotlin.text  contains kotlin.text  matches kotlin.text  replace kotlin.text  ChannelUtil 	org.junit  Locale 	org.junit  Regex 	org.junit  SimpleDateFormat 	org.junit  String 	org.junit  System 	org.junit  Test 	org.junit  assertEquals 	org.junit  
assertNotNull 	org.junit  
assertNull 	org.junit  
assertTrue 	org.junit  contains 	org.junit  java 	org.junit  matches 	org.junit  println 	org.junit  replace 	org.junit  replacePlaybackFormat 	org.junit  assertEquals org.junit.Assert  
assertNotNull org.junit.Assert  
assertNull org.junit.Assert  
assertTrue org.junit.Assert  ExampleUnitTest top.yogiczy.mytv.core.data  Test top.yogiczy.mytv.core.data  assertEquals top.yogiczy.mytv.core.data  assertEquals *top.yogiczy.mytv.core.data.ExampleUnitTest  ChannelUtil  top.yogiczy.mytv.core.data.utils  ChannelUtilTest  top.yogiczy.mytv.core.data.utils  Locale  top.yogiczy.mytv.core.data.utils  Regex  top.yogiczy.mytv.core.data.utils  SimpleDateFormat  top.yogiczy.mytv.core.data.utils  
SimpleTest  top.yogiczy.mytv.core.data.utils  String  top.yogiczy.mytv.core.data.utils  System  top.yogiczy.mytv.core.data.utils  Test  top.yogiczy.mytv.core.data.utils  assertEquals  top.yogiczy.mytv.core.data.utils  
assertNotNull  top.yogiczy.mytv.core.data.utils  
assertNull  top.yogiczy.mytv.core.data.utils  
assertTrue  top.yogiczy.mytv.core.data.utils  contains  top.yogiczy.mytv.core.data.utils  java  top.yogiczy.mytv.core.data.utils  matches  top.yogiczy.mytv.core.data.utils  println  top.yogiczy.mytv.core.data.utils  replace  top.yogiczy.mytv.core.data.utils  replacePlaybackFormat  top.yogiczy.mytv.core.data.utils  replacePlaybackFormat ,top.yogiczy.mytv.core.data.utils.ChannelUtil  ChannelUtil 0top.yogiczy.mytv.core.data.utils.ChannelUtilTest  Locale 0top.yogiczy.mytv.core.data.utils.ChannelUtilTest  Regex 0top.yogiczy.mytv.core.data.utils.ChannelUtilTest  SimpleDateFormat 0top.yogiczy.mytv.core.data.utils.ChannelUtilTest  String 0top.yogiczy.mytv.core.data.utils.ChannelUtilTest  System 0top.yogiczy.mytv.core.data.utils.ChannelUtilTest  assertEquals 0top.yogiczy.mytv.core.data.utils.ChannelUtilTest  
assertNotNull 0top.yogiczy.mytv.core.data.utils.ChannelUtilTest  
assertNull 0top.yogiczy.mytv.core.data.utils.ChannelUtilTest  
assertTrue 0top.yogiczy.mytv.core.data.utils.ChannelUtilTest  contains 0top.yogiczy.mytv.core.data.utils.ChannelUtilTest  java 0top.yogiczy.mytv.core.data.utils.ChannelUtilTest  matches 0top.yogiczy.mytv.core.data.utils.ChannelUtilTest  println 0top.yogiczy.mytv.core.data.utils.ChannelUtilTest  replace 0top.yogiczy.mytv.core.data.utils.ChannelUtilTest  replacePlaybackFormat 0top.yogiczy.mytv.core.data.utils.ChannelUtilTest  assertEquals +top.yogiczy.mytv.core.data.utils.SimpleTest  println +top.yogiczy.mytv.core.data.utils.SimpleTest  replace +top.yogiczy.mytv.core.data.utils.SimpleTest  timeZone java.text.DateFormat  timeZone java.text.SimpleDateFormat  TimeZone 	java.util  getTimeZone java.util.TimeZone                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             
{"logs": [{"outputFile": "top.yogiczy.mytv.tv-mergeOriginalDebugResources-85:/values-bn/values-bn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\07b9e5f639314a52052890932176ce08\\transformed\\media3-ui-1.8.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,497,691,776,860,941,1034,1130,1206,1272,1361,1450,1517,1581,1643,1716,1832,1948,2066,2137,2220,2289,2365,2453,2540,2604,2669,2722,2784,2832,2893,2953,3015,3079,3145,3202,3266,3331,3384,3447,3524,3601,3653", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,84,83,80,92,95,75,65,88,88,66,63,61,72,115,115,117,70,82,68,75,87,86,63,64,52,61,47,60,59,61,63,65,56,63,64,52,62,76,76,51,63", "endOffsets": "283,492,686,771,855,936,1029,1125,1201,1267,1356,1445,1512,1576,1638,1711,1827,1943,2061,2132,2215,2284,2360,2448,2535,2599,2664,2717,2779,2827,2888,2948,3010,3074,3140,3197,3261,3326,3379,3442,3519,3596,3648,3712"}, "to": {"startLines": "2,11,15,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,383,592,5837,5922,6006,6087,6180,6276,6352,6418,6507,6596,6663,6727,6789,6862,6978,7094,7212,7283,7366,7435,7511,7599,7686,7750,8574,8627,8689,8737,8798,8858,8920,8984,9050,9107,9171,9236,9289,9352,9429,9506,9558", "endLines": "10,14,18,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "endColumns": "17,12,12,84,83,80,92,95,75,65,88,88,66,63,61,72,115,115,117,70,82,68,75,87,86,63,64,52,61,47,60,59,61,63,65,56,63,64,52,62,76,76,51,63", "endOffsets": "378,587,781,5917,6001,6082,6175,6271,6347,6413,6502,6591,6658,6722,6784,6857,6973,7089,7207,7278,7361,7430,7506,7594,7681,7745,7810,8622,8684,8732,8793,8853,8915,8979,9045,9102,9166,9231,9284,9347,9424,9501,9553,9617"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\0c82a76523f617aef246a3faf4397c6a\\transformed\\ui-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "179,273,357,447,545,631,710,816,903,992,1070,1151,1234,1320,1396,1473,1549,1624,1692", "endColumns": "93,83,89,97,85,78,105,86,88,77,80,82,85,75,76,75,74,67,117", "endOffsets": "268,352,442,540,626,705,811,898,987,1065,1146,1229,1315,1391,1468,1544,1619,1687,1805"}, "to": {"startLines": "66,67,68,69,70,125,126,244,245,247,248,252,254,255,256,257,259,260,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5258,5352,5436,5526,5624,9779,9858,21090,21177,21346,21424,21754,21910,21996,22072,22149,22326,22401,22469", "endColumns": "93,83,89,97,85,78,105,86,88,77,80,82,85,75,76,75,74,67,117", "endOffsets": "5347,5431,5521,5619,5705,9853,9959,21172,21261,21419,21500,21832,21991,22067,22144,22220,22396,22464,22582"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\35f391f48f1163378f0ecf1dfb563ddd\\transformed\\material3-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,193,330,449,583,700,799,915,1057,1178,1320,1405,1511,1605,1706,1835,1964,2075,2204,2331,2461,2641,2763,2883,3005,3136,3231,3326,3459,3606,3703,3808,3918,4045,4177,4284,4385,4462,4565,4665,4771,4862,4952,5055,5135,5220,5321,5425,5518,5623,5710,5816,5915,6023,6141,6221,6321", "endColumns": "137,136,118,133,116,98,115,141,120,141,84,105,93,100,128,128,110,128,126,129,179,121,119,121,130,94,94,132,146,96,104,109,126,131,106,100,76,102,99,105,90,89,102,79,84,100,103,92,104,86,105,98,107,117,79,99,93", "endOffsets": "188,325,444,578,695,794,910,1052,1173,1315,1400,1506,1600,1701,1830,1959,2070,2199,2326,2456,2636,2758,2878,3000,3131,3226,3321,3454,3601,3698,3803,3913,4040,4172,4279,4380,4457,4560,4660,4766,4857,4947,5050,5130,5215,5316,5420,5513,5618,5705,5811,5910,6018,6136,6216,6316,6410"}, "to": {"startLines": "128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10025,10163,10300,10419,10553,10670,10769,10885,11027,11148,11290,11375,11481,11575,11676,11805,11934,12045,12174,12301,12431,12611,12733,12853,12975,13106,13201,13296,13429,13576,13673,13778,13888,14015,14147,14254,14355,14432,14535,14635,14741,14832,14922,15025,15105,15190,15291,15395,15488,15593,15680,15786,15885,15993,16111,16191,16291", "endColumns": "137,136,118,133,116,98,115,141,120,141,84,105,93,100,128,128,110,128,126,129,179,121,119,121,130,94,94,132,146,96,104,109,126,131,106,100,76,102,99,105,90,89,102,79,84,100,103,92,104,86,105,98,107,117,79,99,93", "endOffsets": "10158,10295,10414,10548,10665,10764,10880,11022,11143,11285,11370,11476,11570,11671,11800,11929,12040,12169,12296,12426,12606,12728,12848,12970,13101,13196,13291,13424,13571,13668,13773,13883,14010,14142,14249,14350,14427,14530,14630,14736,14827,14917,15020,15100,15185,15286,15390,15483,15588,15675,15781,15880,15988,16106,16186,16286,16380"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\71973fdd2f8b1122e619daae98b107aa\\transformed\\appcompat-1.7.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,905,996,1089,1183,1277,1377,1470,1565,1659,1750,1841,1927,2037,2141,2244,2352,2460,2565,2730,2835", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "208,314,420,509,614,735,818,900,991,1084,1178,1272,1372,1465,1560,1654,1745,1836,1922,2032,2136,2239,2347,2455,2560,2725,2830,2917"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,249", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "957,1065,1171,1277,1366,1471,1592,1675,1757,1848,1941,2035,2129,2229,2322,2417,2511,2602,2693,2779,2889,2993,3096,3204,3312,3417,3582,21505", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "1060,1166,1272,1361,1466,1587,1670,1752,1843,1936,2030,2124,2224,2317,2412,2506,2597,2688,2774,2884,2988,3091,3199,3307,3412,3577,3682,21587"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\03c59f01b4d50573fe2a148684529e0c\\transformed\\material-1.12.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,369,462,545,646,738,842,959,1040,1101,1167,1258,1324,1385,1475,1539,1606,1667,1736,1798,1852,1959,2018,2079,2133,2207,2327,2412,2502,2608,2698,2782,2917,2988,3058,3190,3277,3360,3418,3474,3540,3613,3693,3764,3846,3915,3991,4071,4140,4249,4344,4427,4517,4612,4686,4760,4853,4907,4992,5059,5145,5230,5292,5356,5419,5485,5587,5686,5779,5878,5940,6000,6080,6163,6242", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,97,92,82,100,91,103,116,80,60,65,90,65,60,89,63,66,60,68,61,53,106,58,60,53,73,119,84,89,105,89,83,134,70,69,131,86,82,57,55,65,72,79,70,81,68,75,79,68,108,94,82,89,94,73,73,92,53,84,66,85,84,61,63,62,65,101,98,92,98,61,59,79,82,78,72", "endOffsets": "266,364,457,540,641,733,837,954,1035,1096,1162,1253,1319,1380,1470,1534,1601,1662,1731,1793,1847,1954,2013,2074,2128,2202,2322,2407,2497,2603,2693,2777,2912,2983,3053,3185,3272,3355,3413,3469,3535,3608,3688,3759,3841,3910,3986,4066,4135,4244,4339,4422,4512,4607,4681,4755,4848,4902,4987,5054,5140,5225,5287,5351,5414,5480,5582,5681,5774,5873,5935,5995,6075,6158,6237,6310"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,71,72,123,124,127,185,186,187,188,189,190,191,192,193,194,195,196,197,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,246,250,251,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "786,3760,3858,3951,4034,4135,4956,5060,5177,5710,5771,9622,9713,9964,16385,16475,16539,16606,16667,16736,16798,16852,16959,17018,17079,17133,17207,17417,17502,17592,17698,17788,17872,18007,18078,18148,18280,18367,18450,18508,18564,18630,18703,18783,18854,18936,19005,19081,19161,19230,19339,19434,19517,19607,19702,19776,19850,19943,19997,20082,20149,20235,20320,20382,20446,20509,20575,20677,20776,20869,20968,21030,21266,21592,21675,21837", "endLines": "22,51,52,53,54,55,63,64,65,71,72,123,124,127,185,186,187,188,189,190,191,192,193,194,195,196,197,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,246,250,251,253", "endColumns": "12,97,92,82,100,91,103,116,80,60,65,90,65,60,89,63,66,60,68,61,53,106,58,60,53,73,119,84,89,105,89,83,134,70,69,131,86,82,57,55,65,72,79,70,81,68,75,79,68,108,94,82,89,94,73,73,92,53,84,66,85,84,61,63,62,65,101,98,92,98,61,59,79,82,78,72", "endOffsets": "952,3853,3946,4029,4130,4222,5055,5172,5253,5766,5832,9708,9774,10020,16470,16534,16601,16662,16731,16793,16847,16954,17013,17074,17128,17202,17322,17497,17587,17693,17783,17867,18002,18073,18143,18275,18362,18445,18503,18559,18625,18698,18778,18849,18931,19000,19076,19156,19225,19334,19429,19512,19602,19697,19771,19845,19938,19992,20077,20144,20230,20315,20377,20441,20504,20570,20672,20771,20864,20963,21025,21085,21341,21670,21749,21905"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\c64c27f9b5a3bd14c8d70afc2c5cfe89\\transformed\\core-1.16.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "56,57,58,59,60,61,62,258", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4227,4326,4428,4530,4633,4734,4836,22225", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "4321,4423,4525,4628,4729,4831,4951,22321"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\75b82da373a07aa04f7fa0a8c467d672\\transformed\\media3-exoplayer-1.8.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,189,256,322,397,464,596,725", "endColumns": "67,65,66,65,74,66,131,128,88", "endOffsets": "118,184,251,317,392,459,591,720,809"}, "to": {"startLines": "97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7815,7883,7949,8016,8082,8157,8224,8356,8485", "endColumns": "67,65,66,65,74,66,131,128,88", "endOffsets": "7878,7944,8011,8077,8152,8219,8351,8480,8569"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\cdd23b539dbc00d913771dd5e581033e\\transformed\\foundation-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,128,213", "endColumns": "72,84,84", "endOffsets": "123,208,293"}, "to": {"startLines": "50,262,263", "startColumns": "4,4,4", "startOffsets": "3687,22587,22672", "endColumns": "72,84,84", "endOffsets": "3755,22667,22752"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\e4a290dba5f52ccc9bc02699d6e15cbe\\transformed\\material-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "89", "endOffsets": "140"}, "to": {"startLines": "198", "startColumns": "4", "startOffsets": "17327", "endColumns": "89", "endOffsets": "17412"}}]}]}
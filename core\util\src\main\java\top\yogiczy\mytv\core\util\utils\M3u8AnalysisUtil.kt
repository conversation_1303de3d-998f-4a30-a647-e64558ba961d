package top.yogiczy.mytv.core.util.utils

import android.media.MediaMetadataRetriever
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.sync.Semaphore
import kotlinx.coroutines.sync.withPermit
import kotlinx.coroutines.withContext
import java.net.HttpURLConnection
import java.net.URL
import top.yogiczy.mytv.core.data.entities.channel.ChannelLine

object M3u8AnalysisUtil {

    private suspend fun getFinalUrl(line: ChannelLine): String = withContext(Dispatchers.IO) {
        try {
            val conn = URL(line.url).openConnection() as HttpURLConnection
            conn.instanceFollowRedirects = false
            line.httpUserAgent?.let { conn.setRequestProperty("User-Agent", it) }
            line.httpReferrer?.let { conn.setRequestProperty("Referer", it) }
            line.httpOrigin?.let { conn.setRequestProperty("Origin", it) }
            line.httpCookie?.let { conn.setRequestProperty("Cookie", it) }
            conn.connect()
            val responseCode = conn.responseCode
            if (responseCode in 300..399) {
                val redirectedUrl = conn.getHeaderField("Location")
                if (!redirectedUrl.isNullOrEmpty()) {
                    return@withContext getFinalUrl(line.copy(url = redirectedUrl))
                }
            }
            return@withContext conn.url.toString()
        } catch (_: Exception) {
            return@withContext line.url
        }
    }

    private suspend fun getFirstTsUrl(line: ChannelLine): String? = withContext(Dispatchers.IO) {
        val url = getFinalUrl(line)
        if (!url.split("?").first().endsWith(".m3u8")) return@withContext url

        try {
            val conn = URL(url).openConnection() as HttpURLConnection
            line.httpUserAgent?.let { conn.setRequestProperty("User-Agent", it) }
            line.httpReferrer?.let { conn.setRequestProperty("Referer", it) }
            line.httpOrigin?.let { conn.setRequestProperty("Origin", it) }
            line.httpCookie?.let { conn.setRequestProperty("Cookie", it) }
            conn.connect()

            val m3u8Content = conn.inputStream.bufferedReader().use { it.readText() }
            val lines = m3u8Content.lines()
            for (strLine in lines) {
                if (!strLine.startsWith("#") && strLine.contains(".ts", ignoreCase = true)) {
                    return@withContext if (strLine.startsWith("http")) strLine
                    else URL(URL(url), strLine).toString()
                } else if (!strLine.startsWith("#") && strLine.contains(".m3u8", ignoreCase = true)) {
                    val m3u8Url = if (strLine.startsWith("http")) strLine
                    else URL(URL(url), strLine).toString()
                    return@withContext getFirstTsUrl(line.copy(url = m3u8Url))
                }
            }

            return@withContext null
        } catch (_: Exception) {
            return@withContext null
        }
    }

    private val semaphore = Semaphore(5)
    suspend fun getFirstFrame(line: ChannelLine) = withContext(Dispatchers.IO) {
        semaphore.withPermit {
            val retriever = MediaMetadataRetriever()
            try {
                val tsUrl = getFirstTsUrl(line) ?: return@withContext null
                Log.d("M3u8AnalysisUtil", "getFirstFrame: $tsUrl")
                val originalHost = URL(line.url).host
                val tsHost = URL(tsUrl).host
                val headers = mutableMapOf<String, String>()
                line.httpUserAgent?.let { headers["User-Agent"] = it }
                if (originalHost == tsHost) {
                    line.httpReferrer?.let { headers["Referer"] = it }
                    line.httpOrigin?.let { headers["Origin"] = it }
                    line.httpCookie?.let { headers["Cookie"] = it }
                }  
                retriever.setDataSource(tsUrl, headers)
                return@withContext retriever.getFrameAtTime(
                    0,
                    MediaMetadataRetriever.OPTION_CLOSEST
                )
            } catch (e: Exception) {
                Log.e("M3u8AnalysisUtil", "getFirstFrame: Failed to retrieve frame", e)
                return@withContext null
            } finally {
                retriever.release()
            }
        }
    }
}
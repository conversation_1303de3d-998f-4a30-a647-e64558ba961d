androidx.activity:activity-compose:1.10.1
androidx.activity:activity-ktx:1.10.1
androidx.activity:activity:1.10.1
androidx.annotation:annotation-experimental:1.4.1
androidx.annotation:annotation-jvm:1.9.1
androidx.appcompat:appcompat-resources:1.7.1
androidx.appcompat:appcompat:1.7.1
androidx.arch.core:core-common:2.2.0
androidx.arch.core:core-runtime:2.2.0
androidx.asynclayoutinflater:asynclayoutinflater:1.0.0
androidx.autofill:autofill:1.0.0
androidx.cardview:cardview:1.0.0
androidx.collection:collection-jvm:1.5.0
androidx.collection:collection-ktx:1.5.0
androidx.compose.animation:animation-android:1.8.3
androidx.compose.animation:animation-core-android:1.8.3
androidx.compose.foundation:foundation-android:1.8.3
androidx.compose.foundation:foundation-layout-android:1.8.3
androidx.compose.material3:material3-android:1.3.2
androidx.compose.material:material-android:1.8.3
androidx.compose.material:material-icons-core-android:1.7.8
androidx.compose.material:material-icons-extended-android:1.7.8
androidx.compose.material:material-ripple-android:1.8.3
androidx.compose.runtime:runtime-android:1.8.3
androidx.compose.runtime:runtime-saveable-android:1.8.3
androidx.compose.ui:ui-android:1.8.3
androidx.compose.ui:ui-geometry-android:1.8.3
androidx.compose.ui:ui-graphics-android:1.8.3
androidx.compose.ui:ui-test-manifest:1.8.3
androidx.compose.ui:ui-text-android:1.8.3
androidx.compose.ui:ui-tooling-android:1.8.3
androidx.compose.ui:ui-tooling-data-android:1.8.3
androidx.compose.ui:ui-tooling-preview-android:1.8.3
androidx.compose.ui:ui-unit-android:1.8.3
androidx.compose.ui:ui-util-android:1.8.3
androidx.concurrent:concurrent-futures:1.1.0
androidx.constraintlayout:constraintlayout-solver:2.0.1
androidx.constraintlayout:constraintlayout:2.0.1
androidx.coordinatorlayout:coordinatorlayout:1.1.0
androidx.core:core-ktx:1.16.0
androidx.core:core-viewtree:1.0.0
androidx.core:core:1.16.0
androidx.cursoradapter:cursoradapter:1.0.0
androidx.customview:customview-poolingcontainer:1.0.0
androidx.customview:customview:1.1.0
androidx.documentfile:documentfile:1.0.0
androidx.drawerlayout:drawerlayout:1.1.1
androidx.dynamicanimation:dynamicanimation:1.0.0
androidx.emoji2:emoji2-views-helper:1.4.0
androidx.emoji2:emoji2:1.4.0
androidx.exifinterface:exifinterface:1.3.7
androidx.fragment:fragment:1.5.4
androidx.graphics:graphics-path:1.0.1
androidx.interpolator:interpolator:1.0.0
androidx.legacy:legacy-support-core-ui:1.0.0
androidx.legacy:legacy-support-core-utils:1.0.0
androidx.legacy:legacy-support-v4:1.0.0
androidx.lifecycle:lifecycle-common-java8:2.9.1
androidx.lifecycle:lifecycle-common-jvm:2.9.1
androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1
androidx.lifecycle:lifecycle-livedata-core:2.9.1
androidx.lifecycle:lifecycle-livedata:2.9.1
androidx.lifecycle:lifecycle-process:2.9.1
androidx.lifecycle:lifecycle-runtime-android:2.9.1
androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1
androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1
androidx.lifecycle:lifecycle-viewmodel-android:2.9.1
androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1
androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1
androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1
androidx.lifecycle:lifecycle-viewmodel:2.9.1
androidx.loader:loader:1.0.0
androidx.localbroadcastmanager:localbroadcastmanager:1.0.0
androidx.media3:media3-common:1.8.0
androidx.media3:media3-container:1.8.0
androidx.media3:media3-database:1.8.0
androidx.media3:media3-datasource-rtmp:1.8.0
androidx.media3:media3-datasource:1.8.0
androidx.media3:media3-decoder:1.8.0
androidx.media3:media3-exoplayer-dash:1.8.0
androidx.media3:media3-exoplayer-hls:1.8.0
androidx.media3:media3-exoplayer-rtsp:1.8.0
androidx.media3:media3-exoplayer-smoothstreaming:1.8.0
androidx.media3:media3-exoplayer:1.8.0
androidx.media3:media3-extractor:1.8.0
androidx.media3:media3-ui:1.8.0
androidx.media:media:1.0.0
androidx.navigation:navigation-common-android:2.9.2
androidx.navigation:navigation-compose-android:2.9.2
androidx.navigation:navigation-runtime-android:2.9.2
androidx.print:print:1.0.0
androidx.profileinstaller:profileinstaller:1.4.1
androidx.recyclerview:recyclerview:1.3.0
androidx.resourceinspection:resourceinspection-annotation:1.0.1
androidx.savedstate:savedstate-android:1.3.0
androidx.savedstate:savedstate-compose-android:1.3.0
androidx.savedstate:savedstate-ktx:1.3.0
androidx.slidingpanelayout:slidingpanelayout:1.0.0
androidx.startup:startup-runtime:1.2.0
androidx.swiperefreshlayout:swiperefreshlayout:1.0.0
androidx.tracing:tracing:1.2.0
androidx.transition:transition:1.5.0
androidx.tv:tv-material:1.1.0-alpha01
androidx.vectordrawable:vectordrawable-animated:1.1.0
androidx.vectordrawable:vectordrawable:1.1.0
androidx.versionedparcelable:versionedparcelable:1.1.1
androidx.viewpager2:viewpager2:1.0.0
androidx.viewpager:viewpager:1.0.0
com.caverock:androidsvg-aar:1.4
com.google.accompanist:accompanist-drawablepainter:0.32.0
com.google.android.material:material:1.12.0
com.google.errorprone:error_prone_annotations:2.15.0
com.google.guava:failureaccess:1.0.2
com.google.guava:guava:33.3.1-android
com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
com.koushikdutta.async:androidasync:3.1.0
com.squareup.okhttp3:okhttp-android:5.1.0
com.squareup.okio:okio-jvm:3.15.0
io.antmedia:rtmp-client:3.2.0
io.coil-kt:coil-base:2.7.0
io.coil-kt:coil-compose-base:2.7.0
io.coil-kt:coil-compose:2.7.0
io.coil-kt:coil-svg:2.7.0
io.coil-kt:coil:2.7.0
io.github.alexzhirkevich:qrose-android:1.0.1
io.github.alexzhirkevich:qrose-core-android:1.0.1
io.github.biezhi:TinyPinyin:2.0.3.RELEASE
io.sentry:sentry-android-core:8.14.0
io.sentry:sentry-android-fragment:8.14.0
io.sentry:sentry-android-navigation:8.14.0
io.sentry:sentry-android-ndk:8.14.0
io.sentry:sentry-android-replay:8.14.0
io.sentry:sentry-android:8.14.0
io.sentry:sentry-compose-android:8.14.0
io.sentry:sentry-kotlin-extensions:8.14.0
io.sentry:sentry-native-ndk:0.8.4
io.sentry:sentry-okhttp:8.14.0
io.sentry:sentry:8.14.0
org.ahocorasick:ahocorasick:0.4.0
org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.24
org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.24
org.jetbrains.kotlin:kotlin-stdlib:2.2.0
org.jetbrains.kotlinx:kotlinx-collections-immutable-jvm:0.4.0
org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1
org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.8.1
org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.9.0
org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.9.0
org.jetbrains:annotations:23.0.0
org.jspecify:jspecify:1.0.0
org.videolan.android:libvlc-all:3.6.2
wang.harlon.quickjs:wrapper-android:3.2.3
wang.harlon.quickjs:wrapper-java:3.2.3
package top.yogiczy.mytv.core.data.repositories.epg.fetcher

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.ResponseBody

/**
 * 节目单xml数据获取
 */
class XmlEpgFetcher : EpgFetcher {
    override fun isSupport(url: String, contentType: String?): <PERSON><PERSON><PERSON> {
        if (contentType == null) return url.split("?").first().endsWith(".xml")
        return contentType.contains("xml", ignoreCase = true)
    }

    override suspend fun fetch(body: ResponseBody) = withContext(Dispatchers.IO) {
        body.byteStream()
    }
}
package top.yogiczy.mytv.core.util.utils

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import android.util.Log
import androidx.core.content.FileProvider
import java.io.File

object ApkInstaller {

    @SuppressLint("SetWorldReadable")
    fun installApk(context: Context, originalApkPath: String) {
        val sourceFile = File(originalApkPath)
        if (!sourceFile.exists()) {
            Log.e("ApkInstaller", "Source APK not found: $originalApkPath")
            return
        }

        // 将 APK 复制到 files/apk/latest.apk
        val targetDir = File(context.filesDir, "apk").apply { mkdirs() }
        val targetApk = File(targetDir, sourceFile.name).apply {
            writeBytes(sourceFile.readBytes())
            setReadable(true, false) // 兼容 Android 6 安装失败问题
        }

        Log.d("ApkInstaller", "Copied APK to: ${targetApk.absolutePath}")
        Log.d("ApkInstaller", "Exists: ${targetApk.exists()}")

        val apkUri: Uri = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            FileProvider.getUriForFile(
                context,
                "${context.packageName}.FileProvider",
                targetApk
            )
        } else {
            Uri.fromFile(targetApk)
        }

        val intent = Intent(Intent.ACTION_VIEW).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_GRANT_READ_URI_PERMISSION
            setDataAndType(apkUri, "application/vnd.android.package-archive")
        }

        try {
            context.startActivity(intent)
        } catch (e: Exception) {
            Log.e("ApkInstaller", "Failed to launch install intent", e)
            // 可选：引导用户去系统设置授权
            requestInstallPermission(context)
        }
    }

    fun requestInstallPermission(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val intent = Intent(Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES).apply {
                data = Uri.parse("package:${context.packageName}")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)
        }
    }
}

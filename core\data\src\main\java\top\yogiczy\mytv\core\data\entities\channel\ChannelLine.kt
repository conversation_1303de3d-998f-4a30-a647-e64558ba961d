package top.yogiczy.mytv.core.data.entities.channel

import kotlinx.serialization.Serializable

/**
 * 频道线路
 */
@Serializable
data class ChannelLine(
    val url: String = "",
    val httpUserAgent: String? = null,
    val httpReferrer: String? = null,
    val httpOrigin: String? = null,
    val httpCookie: String? = null,
    val hybridType: HybridType = HybridType.None,
    val name: String? = if (url.contains("$")) url.split("$").lastOrNull() else null,
    val manifestType: String? = null,
    val licenseType: String? = null,
    val licenseKey: String? = null,
    val playbackType: Int? = null,
    val playbackFormat: String? = null,
    var playbackUrl: String? = null,
) {

    val playableUrl: String
        get() = url.substringBefore("$").let {
            // 修复部分链接无法播放，实际请求时?将去掉，导致链接无法访问，因此加上一个t
            if (url.endsWith("?")) "${it}t" else it
        }
    override fun equals(other: Any?): Boolean {
        if (other !is ChannelLine) return false
        return url == other.url && httpUserAgent == other.httpUserAgent 
            && httpReferrer == other.httpReferrer && hybridType == other.hybridType
            && httpOrigin == other.httpOrigin && httpCookie == other.httpCookie
            && name == other.name && manifestType == other.manifestType
            && licenseType == other.licenseType && licenseKey == other.licenseKey
            && playbackType == other.playbackType && playbackFormat == other.playbackFormat
    }

    companion object {
        val EXAMPLE =
            ChannelLine(
                url = "http://*******\$LR•IPV6『线路1』",
                httpUserAgent = "okhttp",
            )
    }

    enum class HybridType {
        None,
        WebView,
        JavaScript,
    }
}
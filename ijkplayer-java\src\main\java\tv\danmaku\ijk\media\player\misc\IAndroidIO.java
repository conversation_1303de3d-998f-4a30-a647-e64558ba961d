/*
 * Copyright (C) 2016 Bilibili
 * Copyright (C) 2016 <PERSON> <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package tv.danmaku.ijk.media.player.misc;

import java.io.IOException;

@SuppressWarnings("RedundantThrows")
public interface IAndroidIO {
    int  open(String url) throws IOException;
    int  read(byte[] buffer, int size) throws <PERSON>OException;
    long seek(long offset, int whence) throws IOException;
    int  close() throws IOException;
}

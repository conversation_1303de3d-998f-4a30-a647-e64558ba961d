<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-Internal\ijkplayer-java\src\main\jniLibs"/><source path="C:\Users\<USER>\StudioProjects\mytv-Internal\ijkplayer-java\jniLibs"><file name="arm64-v8a/libijkffmpeg.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\ijkplayer-java\jniLibs\arm64-v8a\libijkffmpeg.so"/><file name="arm64-v8a/libijkplayer.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\ijkplayer-java\jniLibs\arm64-v8a\libijkplayer.so"/><file name="arm64-v8a/libijksdl.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\ijkplayer-java\jniLibs\arm64-v8a\libijksdl.so"/><file name="armeabi-v7a/libijkffmpeg.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\ijkplayer-java\jniLibs\armeabi-v7a\libijkffmpeg.so"/><file name="armeabi-v7a/libijkplayer.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\ijkplayer-java\jniLibs\armeabi-v7a\libijkplayer.so"/><file name="armeabi-v7a/libijksdl.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\ijkplayer-java\jniLibs\armeabi-v7a\libijksdl.so"/><file name="x86/libijkffmpeg.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\ijkplayer-java\jniLibs\x86\libijkffmpeg.so"/><file name="x86/libijkplayer.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\ijkplayer-java\jniLibs\x86\libijkplayer.so"/><file name="x86/libijksdl.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\ijkplayer-java\jniLibs\x86\libijksdl.so"/><file name="x86_64/libijkffmpeg.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\ijkplayer-java\jniLibs\x86_64\libijkffmpeg.so"/><file name="x86_64/libijkplayer.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\ijkplayer-java\jniLibs\x86_64\libijkplayer.so"/><file name="x86_64/libijksdl.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\ijkplayer-java\jniLibs\x86_64\libijksdl.so"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-Internal\ijkplayer-java\src\debug\jniLibs"/></dataSet></merger>
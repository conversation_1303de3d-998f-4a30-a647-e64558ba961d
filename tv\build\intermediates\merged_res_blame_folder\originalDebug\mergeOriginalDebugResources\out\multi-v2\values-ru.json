{"logs": [{"outputFile": "top.yogiczy.mytv.tv-mergeOriginalDebugResources-85:/values-ru/values-ru.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\07b9e5f639314a52052890932176ce08\\transformed\\media3-ui-1.8.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,280,619,949,1032,1115,1198,1288,1388,1459,1532,1631,1732,1805,1877,1942,2020,2132,2243,2360,2437,2532,2604,2677,2765,2853,2922,2987,3040,3102,3150,3211,3278,3346,3412,3494,3552,3609,3675,3727,3788,3873,3958,4021", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "endColumns": "17,12,12,82,82,82,89,99,70,72,98,100,72,71,64,77,111,110,116,76,94,71,72,87,87,68,64,52,61,47,60,66,67,65,81,57,56,65,51,60,84,84,62,68", "endOffsets": "275,614,944,1027,1110,1193,1283,1383,1454,1527,1626,1727,1800,1872,1937,2015,2127,2238,2355,2432,2527,2599,2672,2760,2848,2917,2982,3035,3097,3145,3206,3273,3341,3407,3489,3547,3604,3670,3722,3783,3868,3953,4016,4085"}, "to": {"startLines": "2,11,17,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,375,714,6223,6306,6389,6472,6562,6662,6733,6806,6905,7006,7079,7151,7216,7294,7406,7517,7634,7711,7806,7878,7951,8039,8127,8196,8928,8981,9043,9091,9152,9219,9287,9353,9435,9493,9550,9616,9668,9729,9814,9899,9962", "endLines": "10,16,22,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128", "endColumns": "17,12,12,82,82,82,89,99,70,72,98,100,72,71,64,77,111,110,116,76,94,71,72,87,87,68,64,52,61,47,60,66,67,65,81,57,56,65,51,60,84,84,62,68", "endOffsets": "370,709,1039,6301,6384,6467,6557,6657,6728,6801,6900,7001,7074,7146,7211,7289,7401,7512,7629,7706,7801,7873,7946,8034,8122,8191,8256,8976,9038,9086,9147,9214,9282,9348,9430,9488,9545,9611,9663,9724,9809,9894,9957,10026"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\71973fdd2f8b1122e619daae98b107aa\\transformed\\appcompat-1.7.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,2899"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,255", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1325,1440,1542,1641,1727,1832,1953,2032,2108,2200,2294,2389,2482,2577,2671,2767,2862,2954,3046,3135,3241,3348,3446,3555,3662,3776,3942,21871", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "1435,1537,1636,1722,1827,1948,2027,2103,2195,2289,2384,2477,2572,2666,2762,2857,2949,3041,3130,3236,3343,3441,3550,3657,3771,3937,4037,21948"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\35f391f48f1163378f0ecf1dfb563ddd\\transformed\\material3-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,289,407,525,624,721,835,976,1093,1233,1317,1415,1508,1606,1721,1844,1947,2076,2204,2330,2510,2634,2757,2884,3004,3098,3198,3319,3452,3550,3664,3771,3903,4041,4151,4251,4336,4431,4527,4650,4744,4831,4939,5019,5103,5201,5302,5396,5491,5579,5686,5784,5883,6030,6110,6216", "endColumns": "117,115,117,117,98,96,113,140,116,139,83,97,92,97,114,122,102,128,127,125,179,123,122,126,119,93,99,120,132,97,113,106,131,137,109,99,84,94,95,122,93,86,107,79,83,97,100,93,94,87,106,97,98,146,79,105,96", "endOffsets": "168,284,402,520,619,716,830,971,1088,1228,1312,1410,1503,1601,1716,1839,1942,2071,2199,2325,2505,2629,2752,2879,2999,3093,3193,3314,3447,3545,3659,3766,3898,4036,4146,4246,4331,4426,4522,4645,4739,4826,4934,5014,5098,5196,5297,5391,5486,5574,5681,5779,5878,6025,6105,6211,6308"}, "to": {"startLines": "134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10442,10560,10676,10794,10912,11011,11108,11222,11363,11480,11620,11704,11802,11895,11993,12108,12231,12334,12463,12591,12717,12897,13021,13144,13271,13391,13485,13585,13706,13839,13937,14051,14158,14290,14428,14538,14638,14723,14818,14914,15037,15131,15218,15326,15406,15490,15588,15689,15783,15878,15966,16073,16171,16270,16417,16497,16603", "endColumns": "117,115,117,117,98,96,113,140,116,139,83,97,92,97,114,122,102,128,127,125,179,123,122,126,119,93,99,120,132,97,113,106,131,137,109,99,84,94,95,122,93,86,107,79,83,97,100,93,94,87,106,97,98,146,79,105,96", "endOffsets": "10555,10671,10789,10907,11006,11103,11217,11358,11475,11615,11699,11797,11890,11988,12103,12226,12329,12458,12586,12712,12892,13016,13139,13266,13386,13480,13580,13701,13834,13932,14046,14153,14285,14423,14533,14633,14718,14813,14909,15032,15126,15213,15321,15401,15485,15583,15684,15778,15873,15961,16068,16166,16265,16412,16492,16598,16695"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\03c59f01b4d50573fe2a148684529e0c\\transformed\\material-1.12.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,381,459,537,621,719,810,907,1044,1136,1211,1277,1376,1453,1516,1634,1695,1760,1817,1887,1948,2002,2118,2175,2237,2291,2365,2493,2581,2668,2771,2863,2949,3086,3170,3255,3389,3480,3556,3610,3661,3727,3799,3877,3948,4030,4110,4186,4263,4340,4447,4536,4609,4699,4794,4868,4949,5042,5097,5178,5244,5330,5415,5477,5541,5604,5676,5774,5873,5968,6060,6118,6173,6253,6347,6423", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,77,77,83,97,90,96,136,91,74,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,86,102,91,85,136,83,84,133,90,75,53,50,65,71,77,70,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79,93,75,78", "endOffsets": "376,454,532,616,714,805,902,1039,1131,1206,1272,1371,1448,1511,1629,1690,1755,1812,1882,1943,1997,2113,2170,2232,2286,2360,2488,2576,2663,2766,2858,2944,3081,3165,3250,3384,3475,3551,3605,3656,3722,3794,3872,3943,4025,4105,4181,4258,4335,4442,4531,4604,4694,4789,4863,4944,5037,5092,5173,5239,5325,5410,5472,5536,5599,5671,5769,5868,5963,6055,6113,6168,6248,6342,6418,6497"}, "to": {"startLines": "23,57,58,59,60,61,69,70,71,77,78,129,130,133,191,192,193,194,195,196,197,198,199,200,201,202,203,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,252,256,257,259", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1044,4132,4210,4288,4372,4470,5288,5385,5522,6082,6157,10031,10130,10379,16700,16818,16879,16944,17001,17071,17132,17186,17302,17359,17421,17475,17549,17770,17858,17945,18048,18140,18226,18363,18447,18532,18666,18757,18833,18887,18938,19004,19076,19154,19225,19307,19387,19463,19540,19617,19724,19813,19886,19976,20071,20145,20226,20319,20374,20455,20521,20607,20692,20754,20818,20881,20953,21051,21150,21245,21337,21395,21620,21953,22047,22195", "endLines": "28,57,58,59,60,61,69,70,71,77,78,129,130,133,191,192,193,194,195,196,197,198,199,200,201,202,203,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,252,256,257,259", "endColumns": "12,77,77,83,97,90,96,136,91,74,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,86,102,91,85,136,83,84,133,90,75,53,50,65,71,77,70,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79,93,75,78", "endOffsets": "1320,4205,4283,4367,4465,4556,5380,5517,5609,6152,6218,10125,10202,10437,16813,16874,16939,16996,17066,17127,17181,17297,17354,17416,17470,17544,17672,17853,17940,18043,18135,18221,18358,18442,18527,18661,18752,18828,18882,18933,18999,19071,19149,19220,19302,19382,19458,19535,19612,19719,19808,19881,19971,20066,20140,20221,20314,20369,20450,20516,20602,20687,20749,20813,20876,20948,21046,21145,21240,21332,21390,21445,21695,22042,22118,22269"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\c64c27f9b5a3bd14c8d70afc2c5cfe89\\transformed\\core-1.16.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "62,63,64,65,66,67,68,264", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4561,4659,4761,4862,4963,5068,5171,22593", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "4654,4756,4857,4958,5063,5166,5283,22689"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\e4a290dba5f52ccc9bc02699d6e15cbe\\transformed\\material-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "92", "endOffsets": "143"}, "to": {"startLines": "204", "startColumns": "4", "startOffsets": "17677", "endColumns": "92", "endOffsets": "17765"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\75b82da373a07aa04f7fa0a8c467d672\\transformed\\media3-exoplayer-1.8.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,188,253,319,397,471,559,645", "endColumns": "71,60,64,65,77,73,87,85,76", "endOffsets": "122,183,248,314,392,466,554,640,717"}, "to": {"startLines": "103,104,105,106,107,108,109,110,111", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8261,8333,8394,8459,8525,8603,8677,8765,8851", "endColumns": "71,60,64,65,77,73,87,85,76", "endOffsets": "8328,8389,8454,8520,8598,8672,8760,8846,8923"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\cdd23b539dbc00d913771dd5e581033e\\transformed\\foundation-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,145,233", "endColumns": "89,87,90", "endOffsets": "140,228,319"}, "to": {"startLines": "56,268,269", "startColumns": "4,4,4", "startOffsets": "4042,22971,23059", "endColumns": "89,87,90", "endOffsets": "4127,23054,23145"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\0c82a76523f617aef246a3faf4397c6a\\transformed\\ui-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "196,289,372,470,572,664,746,836,924,1006,1090,1177,1249,1338,1414,1492,1568,1652,1722", "endColumns": "92,82,97,101,91,81,89,87,81,83,86,71,88,75,77,75,83,69,122", "endOffsets": "284,367,465,567,659,741,831,919,1001,1085,1172,1244,1333,1409,1487,1563,1647,1717,1840"}, "to": {"startLines": "72,73,74,75,76,131,132,250,251,253,254,258,260,261,262,263,265,266,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5614,5707,5790,5888,5990,10207,10289,21450,21538,21700,21784,22123,22274,22363,22439,22517,22694,22778,22848", "endColumns": "92,82,97,101,91,81,89,87,81,83,86,71,88,75,77,75,83,69,122", "endOffsets": "5702,5785,5883,5985,6077,10284,10374,21533,21615,21779,21866,22190,22358,22434,22512,22588,22773,22843,22966"}}]}]}
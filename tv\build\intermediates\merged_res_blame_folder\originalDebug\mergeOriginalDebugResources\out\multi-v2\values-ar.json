{"logs": [{"outputFile": "top.yogiczy.mytv.tv-mergeOriginalDebugResources-85:/values-ar/values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\e4a290dba5f52ccc9bc02699d6e15cbe\\transformed\\material-release\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "261", "startColumns": "4", "startOffsets": "21693", "endColumns": "87", "endOffsets": "21776"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\c64c27f9b5a3bd14c8d70afc2c5cfe89\\transformed\\core-1.16.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "69,70,71,72,73,74,75,321", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4967,5060,5162,5257,5360,5463,5565,26587", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "5055,5157,5252,5355,5458,5560,5674,26683"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\03c59f01b4d50573fe2a148684529e0c\\transformed\\material-1.12.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,465,543,619,703,795,878,979,1098,1175,1234,1297,1388,1457,1524,1624,1687,1752,1813,1881,1943,2001,2115,2175,2236,2293,2366,2489,2570,2662,2769,2867,2947,3095,3176,3257,3385,3474,3550,3603,3657,3723,3801,3881,3952,4034,4106,4180,4253,4323,4432,4523,4594,4684,4779,4853,4936,5029,5078,5159,5228,5314,5399,5461,5525,5588,5657,5766,5876,5973,6073,6130,6188,6268,6347,6422", "endLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84", "endColumns": "12,77,75,83,91,82,100,118,76,58,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79,78,74,75", "endOffsets": "460,538,614,698,790,873,974,1093,1170,1229,1292,1383,1452,1519,1619,1682,1747,1808,1876,1938,1996,2110,2170,2231,2288,2361,2484,2565,2657,2764,2862,2942,3090,3171,3252,3380,3469,3545,3598,3652,3718,3796,3876,3947,4029,4101,4175,4248,4318,4427,4518,4589,4679,4774,4848,4931,5024,5073,5154,5223,5309,5394,5456,5520,5583,5652,5761,5871,5968,6068,6125,6183,6263,6342,6417,6493"}, "to": {"startLines": "27,64,65,66,67,68,76,77,78,128,129,180,181,190,248,249,250,251,252,253,254,255,256,257,258,259,260,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,309,313,314,316", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1409,4554,4632,4708,4792,4884,5679,5780,5899,9741,9800,13564,13655,14399,20728,20828,20891,20956,21017,21085,21147,21205,21319,21379,21440,21497,21570,21781,21862,21954,22061,22159,22239,22387,22468,22549,22677,22766,22842,22895,22949,23015,23093,23173,23244,23326,23398,23472,23545,23615,23724,23815,23886,23976,24071,24145,24228,24321,24370,24451,24520,24606,24691,24753,24817,24880,24949,25058,25168,25265,25365,25422,25645,25971,26050,26195", "endLines": "34,64,65,66,67,68,76,77,78,128,129,180,181,190,248,249,250,251,252,253,254,255,256,257,258,259,260,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,309,313,314,316", "endColumns": "12,77,75,83,91,82,100,118,76,58,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79,78,74,75", "endOffsets": "1769,4627,4703,4787,4879,4962,5775,5894,5971,9795,9858,13650,13719,14461,20823,20886,20951,21012,21080,21142,21200,21314,21374,21435,21492,21565,21688,21857,21949,22056,22154,22234,22382,22463,22544,22672,22761,22837,22890,22944,23010,23088,23168,23239,23321,23393,23467,23540,23610,23719,23810,23881,23971,24066,24140,24223,24316,24365,24446,24515,24601,24686,24748,24812,24875,24944,25053,25163,25260,25360,25417,25475,25720,26045,26120,26266"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\35f391f48f1163378f0ecf1dfb563ddd\\transformed\\material3-release\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,290,400,516,614,719,842,979,1100,1243,1330,1435,1527,1627,1745,1871,1981,2127,2271,2408,2560,2686,2806,2929,3047,3140,3238,3361,3485,3585,3688,3796,3941,4091,4198,4300,4380,4474,4567,4684,4773,4858,4958,5037,5121,5222,5325,5424,5522,5609,5715,5815,5915,6044,6123,6224", "endColumns": "117,116,109,115,97,104,122,136,120,142,86,104,91,99,117,125,109,145,143,136,151,125,119,122,117,92,97,122,123,99,102,107,144,149,106,101,79,93,92,116,88,84,99,78,83,100,102,98,97,86,105,99,99,128,78,100,92", "endOffsets": "168,285,395,511,609,714,837,974,1095,1238,1325,1430,1522,1622,1740,1866,1976,2122,2266,2403,2555,2681,2801,2924,3042,3135,3233,3356,3480,3580,3683,3791,3936,4086,4193,4295,4375,4469,4562,4679,4768,4853,4953,5032,5116,5217,5320,5419,5517,5604,5710,5810,5910,6039,6118,6219,6312"}, "to": {"startLines": "191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14466,14584,14701,14811,14927,15025,15130,15253,15390,15511,15654,15741,15846,15938,16038,16156,16282,16392,16538,16682,16819,16971,17097,17217,17340,17458,17551,17649,17772,17896,17996,18099,18207,18352,18502,18609,18711,18791,18885,18978,19095,19184,19269,19369,19448,19532,19633,19736,19835,19933,20020,20126,20226,20326,20455,20534,20635", "endColumns": "117,116,109,115,97,104,122,136,120,142,86,104,91,99,117,125,109,145,143,136,151,125,119,122,117,92,97,122,123,99,102,107,144,149,106,101,79,93,92,116,88,84,99,78,83,100,102,98,97,86,105,99,99,128,78,100,92", "endOffsets": "14579,14696,14806,14922,15020,15125,15248,15385,15506,15649,15736,15841,15933,16033,16151,16277,16387,16533,16677,16814,16966,17092,17212,17335,17453,17546,17644,17767,17891,17991,18094,18202,18347,18497,18604,18706,18786,18880,18973,19090,19179,19264,19364,19443,19527,19628,19731,19830,19928,20015,20121,20221,20321,20450,20529,20630,20723"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\71973fdd2f8b1122e619daae98b107aa\\transformed\\appcompat-1.7.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,424,506,607,721,801,880,971,1064,1156,1250,1350,1443,1538,1631,1722,1816,1895,2000,2098,2196,2304,2404,2507,2662,2759", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "208,312,419,501,602,716,796,875,966,1059,1151,1245,1345,1438,1533,1626,1717,1811,1890,1995,2093,2191,2299,2399,2502,2657,2754,2836"}, "to": {"startLines": "35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,312", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1774,1882,1986,2093,2175,2276,2390,2470,2549,2640,2733,2825,2919,3019,3112,3207,3300,3391,3485,3564,3669,3767,3865,3973,4073,4176,4331,25889", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "1877,1981,2088,2170,2271,2385,2465,2544,2635,2728,2820,2914,3014,3107,3202,3295,3386,3480,3559,3664,3762,3860,3968,4068,4171,4326,4423,25966"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\07b9e5f639314a52052890932176ce08\\transformed\\media3-ui-1.8.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,11,19,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,349,849,1314,1393,1471,1547,1641,1733,1807,1872,1964,2054,2124,2188,2251,2320,2428,2537,2652,2718,2801,2873,2945,3037,3128,3192,3255,3308,3379,3434,3495,3553,3627,3691,3755,3815,3880,3944,3996,4053,4124,4195,4251", "endLines": "10,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "17,12,12,78,77,75,93,91,73,64,91,89,69,63,62,68,107,108,114,65,82,71,71,91,90,63,62,52,70,54,60,57,73,63,63,59,64,63,51,56,70,70,55,67", "endOffsets": "344,844,1309,1388,1466,1542,1636,1728,1802,1867,1959,2049,2119,2183,2246,2315,2423,2532,2647,2713,2796,2868,2940,3032,3123,3187,3250,3303,3374,3429,3490,3548,3622,3686,3750,3810,3875,3939,3991,4048,4119,4190,4246,4314"}, "to": {"startLines": "2,11,19,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,444,944,9863,9942,10020,10096,10190,10282,10356,10421,10513,10603,10673,10737,10800,10869,10977,11086,11201,11267,11350,11422,11494,11586,11677,11741,12500,12553,12624,12679,12740,12798,12872,12936,13000,13060,13125,13189,13241,13298,13369,13440,13496", "endLines": "10,18,26,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179", "endColumns": "17,12,12,78,77,75,93,91,73,64,91,89,69,63,62,68,107,108,114,65,82,71,71,91,90,63,62,52,70,54,60,57,73,63,63,59,64,63,51,56,70,70,55,67", "endOffsets": "439,939,1404,9937,10015,10091,10185,10277,10351,10416,10508,10598,10668,10732,10795,10864,10972,11081,11196,11262,11345,11417,11489,11581,11672,11736,11799,12548,12619,12674,12735,12793,12867,12931,12995,13055,13120,13184,13236,13293,13364,13435,13491,13559"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\0c82a76523f617aef246a3faf4397c6a\\transformed\\ui-release\\res\\values-ar\\values-ar.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "187,276,359,454,552,637,718,824,908,989,1070,1153,1223,1307,1386,1465,1539,1615,1689", "endColumns": "88,82,94,97,84,80,105,83,80,80,82,69,83,78,78,73,75,73,120", "endOffsets": "271,354,449,547,632,713,819,903,984,1065,1148,1218,1302,1381,1460,1534,1610,1684,1805"}, "to": {"startLines": "79,80,125,126,127,182,183,307,308,310,311,315,317,318,319,320,322,323,324", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5976,6065,9463,9558,9656,13724,13805,25480,25564,25725,25806,26125,26271,26355,26434,26513,26688,26764,26838", "endColumns": "88,82,94,97,84,80,105,83,80,80,82,69,83,78,78,73,75,73,120", "endOffsets": "6060,6143,9553,9651,9736,13800,13906,25559,25640,25801,25884,26190,26350,26429,26508,26582,26759,26833,26954"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\75b82da373a07aa04f7fa0a8c467d672\\transformed\\media3-exoplayer-1.8.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,246,308,390,471,572,667", "endColumns": "64,58,66,61,81,80,100,94,83", "endOffsets": "115,174,241,303,385,466,567,662,746"}, "to": {"startLines": "154,155,156,157,158,159,160,161,162", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11804,11869,11928,11995,12057,12139,12220,12321,12416", "endColumns": "64,58,66,61,81,80,100,94,83", "endOffsets": "11864,11923,11990,12052,12134,12215,12316,12411,12495"}}, {"source": "C:\\Users\\<USER>\\StudioProjects\\mytv-Internal\\tv\\src\\main\\res\\values-ar\\strings.xml", "from": {"startLines": "1,124,125,126,117,118,123,130,131,128,129,133,132,119,127,113,112,114,122,116,115,121,120,136,134,135,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,210,211,362,363,350,351,267,296,306,301,300,302,303,304,305,297,298,299,307,308,260,253,29,30,28,54,175,176,55,166,167,141,142,314,77,41,164,165,163,53,48,47,49,46,45,339,340,338,44,43,348,347,42,349,50,51,52,139,140,171,172,170,137,138,86,92,214,105,107,106,89,109,101,97,103,254,215,88,81,216,218,217,91,94,90,95,104,100,102,108,83,98,84,93,110,111,82,78,255,80,213,79,87,96,99,212,85,198,199,196,197,194,195,251,252,56,143,144,269,270,275,276,277,273,274,271,272,268,261,67,68,69,70,71,72,159,38,32,33,31,35,36,39,34,37,150,151,145,146,149,147,148,203,204,152,153,354,352,353,157,158,156,154,155,74,40,76,208,209,205,257,258,315,256,316,317,345,341,343,346,344,342,60,59,58,177,178,173,174,262,57,327,330,326,323,325,331,322,324,329,328,75,62,63,61,240,242,249,250,247,239,245,246,241,248,243,244,179,180,181,182,183,184,234,235,238,230,228,229,366,367,222,223,356,357,232,233,219,220,221,237,225,224,226,227,236,231,65,64,259,364,365,73,201,202,66,359,360,361,190,191,188,189,186,187,168,169,160,162,161,185,279,280,281,278,200,319,320,321,318,266,265,264,263,312,313,309,311,310,192,193,293,292,287,288,291,284,285,286,206,207,290,282,283,289,358,294,295,21,22,23,24,25,26,27,337,336,335,334,333,355,332", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17,9452,9536,9597,8979,9039,9392,9923,9994,9777,9849,10131,10067,9137,9705,8687,8604,8747,9329,8895,8808,9269,9207,10343,10205,10269,63,153,217,282,351,430,555,686,777,878,954,1051,1122,1190,1258,1327,1395,1464,1533,17005,17077,29242,29321,28202,28254,21752,24213,24987,24546,24485,24606,24683,24814,24898,24271,24337,24420,25058,25124,21326,20705,2617,2681,2551,4354,13806,13882,4416,13007,13071,10780,10867,25554,6161,3389,12839,12910,12764,4262,3846,3770,3928,3701,3632,27517,27592,27442,3576,3512,28080,28020,3444,28137,4008,4097,4181,10581,10650,13409,13481,13338,10416,10487,6796,7217,17341,7997,8186,8074,6992,8354,7753,7522,7887,20780,17404,6924,6476,17465,17653,17542,7135,7349,7069,7406,7944,7695,7823,8275,6592,7580,6667,7290,8425,8509,6538,6232,20836,6410,17277,6316,6864,7467,7637,17204,6726,16035,16110,15825,15902,15601,15683,20611,20655,4485,11053,11137,21876,21940,22283,22358,22434,22131,22210,22006,22073,21814,21403,5427,5498,5563,5630,5723,5791,12448,3199,2800,2875,2737,3002,3071,3258,2942,3131,11719,11789,11247,11302,11645,11433,11511,16444,16515,11865,11927,28415,28307,28360,12257,12333,12191,12005,12080,5980,3337,6093,16813,16874,16579,21014,21142,25615,20905,25673,25749,27910,27659,27785,27958,27853,27718,4792,4707,4634,14000,14088,13608,13678,21455,4557,26445,26673,26362,26145,26293,26743,26066,26220,26599,26519,6047,5013,5096,4908,19898,20019,20458,20532,20332,19821,20206,20272,19957,20390,20079,20142,14209,14284,14396,14474,14589,14676,19326,19396,19751,18984,18766,18859,29674,29765,17961,18042,28604,28707,19127,19197,17753,17817,17891,19675,18341,18246,18443,18542,19600,19057,5258,5190,21271,29432,29510,5936,16282,16374,5360,29009,29065,29145,15261,15327,15050,15141,14866,14940,13157,13233,12524,12688,12611,14782,22572,22638,22721,22511,16225,25878,25933,25998,25823,21701,21648,21592,21536,25400,25469,25190,25346,25262,15394,15482,23850,23739,23355,23438,23666,23046,23151,23275,16650,16729,23593,22807,22900,23514,28946,23967,24057,1610,1674,1766,1932,2065,2238,2418,27357,27233,27145,27011,26927,28482,26820", "endColumns": "44,82,59,106,58,96,58,69,71,70,72,72,62,68,70,58,81,59,61,82,85,58,60,71,62,72,88,62,63,67,77,123,129,89,99,74,95,69,66,66,67,66,67,67,75,70,125,77,109,50,51,60,56,69,58,59,75,129,82,87,64,81,63,64,64,75,73,62,54,64,60,74,116,67,62,84,85,184,59,69,53,69,95,73,90,80,74,78,67,67,73,65,73,54,62,55,58,66,63,87,82,79,67,128,70,125,69,69,92,66,71,61,75,87,110,75,69,68,56,55,54,59,66,60,75,98,109,80,55,64,59,51,56,62,77,73,55,57,57,82,93,52,82,67,64,62,92,58,53,56,71,68,73,113,75,131,80,140,42,48,70,82,108,62,64,73,74,75,77,71,65,56,60,50,69,63,65,91,66,143,74,57,73,65,61,67,58,77,58,66,68,74,53,129,72,76,132,69,62,60,76,65,51,53,74,113,64,73,109,65,50,66,59,129,69,126,127,56,107,74,72,46,57,66,60,55,65,114,83,71,86,119,68,126,79,75,72,68,81,73,67,75,77,71,72,78,44,81,92,103,57,58,72,77,56,75,64,58,60,66,61,62,73,110,76,113,85,104,68,202,68,71,91,123,89,286,79,202,101,234,68,127,62,72,68,74,100,93,97,222,73,68,100,66,53,76,162,42,90,68,65,54,78,95,64,65,89,118,72,108,74,103,85,74,75,82,64,81,84,59,55,53,63,66,53,49,51,54,54,67,83,70,52,82,86,117,115,109,81,74,71,103,122,78,77,82,71,91,144,77,61,88,154,62,90,164,131,171,178,131,83,122,86,132,82,120,105", "endOffsets": "57,9530,9591,9699,9033,9131,9446,9988,10061,9843,9917,10199,10125,9201,9771,8741,8681,8802,9386,8973,8889,9323,9263,10410,10263,10337,147,211,276,345,424,549,680,771,872,948,1045,1116,1184,1252,1321,1389,1458,1527,1604,17071,17198,29315,29426,28248,28301,21808,24265,25052,24600,24540,24677,24808,24892,24981,24331,24414,24479,25118,25184,21397,20774,2675,2731,2611,4410,13876,13994,4479,13065,13151,10861,11047,25609,6226,3438,12904,13001,12833,4348,3922,3840,4002,3764,3695,27586,27653,27511,3626,3570,28131,28074,3506,28196,4091,4175,4256,10644,10774,13475,13602,13403,10481,10575,6858,7284,17398,8068,8269,8180,7063,8419,7817,7574,7938,20830,17459,6986,6532,17536,17747,17647,7211,7400,7129,7461,7991,7747,7881,8348,6661,7631,6720,7343,8503,8598,6586,6310,20899,6470,17335,6404,6918,7516,7689,17271,6790,16104,16219,15896,16029,15677,15819,20649,20699,4551,11131,11241,21934,22000,22352,22428,22505,22204,22277,22067,22125,21870,21449,5492,5557,5624,5717,5785,5930,12518,3252,2869,2936,2794,3065,3125,3331,2996,3193,11783,11859,11296,11427,11713,11505,11639,16509,16573,11921,11999,28476,28354,28409,12327,12442,12251,12074,12185,6041,3383,6155,16868,16999,16644,21136,21265,25667,21008,25743,25817,27952,27712,27847,28014,27904,27779,4902,4786,4701,14082,14203,13672,13800,21530,4628,26513,26737,26439,26214,26356,26814,26139,26287,26667,26593,6087,5090,5184,5007,19951,20073,20526,20605,20384,19892,20266,20326,20013,20452,20136,20200,14278,14390,14468,14583,14670,14776,19390,19594,19815,19051,18853,18978,29759,30047,18036,18240,28701,28937,19191,19320,17811,17885,17955,19745,18437,18335,18536,18760,19669,19121,5354,5252,21320,29504,29668,5974,16368,16438,5421,29059,29139,29236,15321,15388,15135,15255,14934,15044,13227,13332,12605,12758,12682,14860,22632,22715,22801,22566,16276,25927,25992,26060,25872,21746,21695,21642,21586,25463,25548,25256,25394,25340,15476,15595,23961,23844,23432,23508,23733,23145,23269,23349,16723,16807,23660,22894,23040,23587,29003,24051,24207,1668,1760,1926,2059,2232,2412,2545,27436,27351,27227,27139,27005,28598,26921"}, "to": {"startLines": "62,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,184,185,186,187,188,189,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4428,6148,6231,6291,6398,6457,6554,6613,6683,6755,6826,6899,6972,7035,7104,7175,7234,7316,7376,7438,7521,7607,7666,7727,7799,7862,7935,8024,8087,8151,8219,8297,8421,8551,8641,8741,8816,8912,8982,9049,9116,9184,9251,9319,9387,13911,13982,14108,14186,14296,14347,27129,27190,27247,27317,27376,27436,27512,27642,27725,27813,27878,27960,28024,28089,28154,28230,28304,28367,28422,28487,28548,28623,28740,28808,28871,28956,29042,29227,29287,29357,29411,29481,29577,29651,29742,29823,29898,29977,30045,30113,30187,30253,30327,30382,30445,30501,30560,30627,30691,30779,30862,30942,31010,31139,31210,31336,31406,31476,31569,31636,31708,31770,31846,31934,32045,32121,32191,32260,32317,32373,32428,32488,32555,32616,32692,32791,32901,32982,33038,33103,33163,33215,33272,33335,33413,33487,33543,33601,33659,33742,33836,33889,33972,34040,34105,34168,34261,34320,34374,34431,34503,34572,34646,34760,34836,34968,35049,35190,35233,35282,35353,35436,35545,35608,35673,35747,35822,35898,35976,36048,36114,36171,36232,36283,36353,36417,36483,36575,36642,36786,36861,36919,36993,37059,37121,37189,37248,37326,37385,37452,37521,37596,37650,37780,37853,37930,38063,38133,38196,38257,38334,38400,38452,38506,38581,38695,38760,38834,38944,39010,39061,39128,39188,39318,39388,39515,39643,39700,39808,39883,39956,40003,40061,40128,40189,40245,40311,40426,40510,40582,40669,40789,40858,40985,41065,41141,41214,41283,41365,41439,41507,41583,41661,41733,41806,41885,41930,42012,42105,42209,42267,42326,42399,42477,42534,42610,42675,42734,42795,42862,42924,42987,43061,43172,43249,43363,43449,43554,43623,43826,43895,43967,44059,44183,44273,44560,44640,44843,44945,45180,45249,45377,45440,45513,45582,45657,45758,45852,45950,46173,46247,46316,46417,46484,46538,46615,46778,46821,46912,46981,47047,47102,47181,47277,47342,47408,47498,47617,47690,47799,47874,47978,48064,48139,48215,48298,48363,48445,48530,48590,48646,48700,48764,48831,48885,48935,48987,49042,49097,49165,49249,49320,49373,49456,49543,49661,49777,49887,49969,50044,50116,50220,50343,50422,50500,50583,50655,50747,50892,50970,51032,51121,51276,51339,51430,51595,51727,51899,52078,52210,52294,52417,52504,52637,52720,52841", "endColumns": "44,82,59,106,58,96,58,69,71,70,72,72,62,68,70,58,81,59,61,82,85,58,60,71,62,72,88,62,63,67,77,123,129,89,99,74,95,69,66,66,67,66,67,67,75,70,125,77,109,50,51,60,56,69,58,59,75,129,82,87,64,81,63,64,64,75,73,62,54,64,60,74,116,67,62,84,85,184,59,69,53,69,95,73,90,80,74,78,67,67,73,65,73,54,62,55,58,66,63,87,82,79,67,128,70,125,69,69,92,66,71,61,75,87,110,75,69,68,56,55,54,59,66,60,75,98,109,80,55,64,59,51,56,62,77,73,55,57,57,82,93,52,82,67,64,62,92,58,53,56,71,68,73,113,75,131,80,140,42,48,70,82,108,62,64,73,74,75,77,71,65,56,60,50,69,63,65,91,66,143,74,57,73,65,61,67,58,77,58,66,68,74,53,129,72,76,132,69,62,60,76,65,51,53,74,113,64,73,109,65,50,66,59,129,69,126,127,56,107,74,72,46,57,66,60,55,65,114,83,71,86,119,68,126,79,75,72,68,81,73,67,75,77,71,72,78,44,81,92,103,57,58,72,77,56,75,64,58,60,66,61,62,73,110,76,113,85,104,68,202,68,71,91,123,89,286,79,202,101,234,68,127,62,72,68,74,100,93,97,222,73,68,100,66,53,76,162,42,90,68,65,54,78,95,64,65,89,118,72,108,74,103,85,74,75,82,64,81,84,59,55,53,63,66,53,49,51,54,54,67,83,70,52,82,86,117,115,109,81,74,71,103,122,78,77,82,71,91,144,77,61,88,154,62,90,164,131,171,178,131,83,122,86,132,82,120,105", "endOffsets": "4468,6226,6286,6393,6452,6549,6608,6678,6750,6821,6894,6967,7030,7099,7170,7229,7311,7371,7433,7516,7602,7661,7722,7794,7857,7930,8019,8082,8146,8214,8292,8416,8546,8636,8736,8811,8907,8977,9044,9111,9179,9246,9314,9382,9458,13977,14103,14181,14291,14342,14394,27185,27242,27312,27371,27431,27507,27637,27720,27808,27873,27955,28019,28084,28149,28225,28299,28362,28417,28482,28543,28618,28735,28803,28866,28951,29037,29222,29282,29352,29406,29476,29572,29646,29737,29818,29893,29972,30040,30108,30182,30248,30322,30377,30440,30496,30555,30622,30686,30774,30857,30937,31005,31134,31205,31331,31401,31471,31564,31631,31703,31765,31841,31929,32040,32116,32186,32255,32312,32368,32423,32483,32550,32611,32687,32786,32896,32977,33033,33098,33158,33210,33267,33330,33408,33482,33538,33596,33654,33737,33831,33884,33967,34035,34100,34163,34256,34315,34369,34426,34498,34567,34641,34755,34831,34963,35044,35185,35228,35277,35348,35431,35540,35603,35668,35742,35817,35893,35971,36043,36109,36166,36227,36278,36348,36412,36478,36570,36637,36781,36856,36914,36988,37054,37116,37184,37243,37321,37380,37447,37516,37591,37645,37775,37848,37925,38058,38128,38191,38252,38329,38395,38447,38501,38576,38690,38755,38829,38939,39005,39056,39123,39183,39313,39383,39510,39638,39695,39803,39878,39951,39998,40056,40123,40184,40240,40306,40421,40505,40577,40664,40784,40853,40980,41060,41136,41209,41278,41360,41434,41502,41578,41656,41728,41801,41880,41925,42007,42100,42204,42262,42321,42394,42472,42529,42605,42670,42729,42790,42857,42919,42982,43056,43167,43244,43358,43444,43549,43618,43821,43890,43962,44054,44178,44268,44555,44635,44838,44940,45175,45244,45372,45435,45508,45577,45652,45753,45847,45945,46168,46242,46311,46412,46479,46533,46610,46773,46816,46907,46976,47042,47097,47176,47272,47337,47403,47493,47612,47685,47794,47869,47973,48059,48134,48210,48293,48358,48440,48525,48585,48641,48695,48759,48826,48880,48930,48982,49037,49092,49160,49244,49315,49368,49451,49538,49656,49772,49882,49964,50039,50111,50215,50338,50417,50495,50578,50650,50742,50887,50965,51027,51116,51271,51334,51425,51590,51722,51894,52073,52205,52289,52412,52499,52632,52715,52836,52942"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\cdd23b539dbc00d913771dd5e581033e\\transformed\\foundation-release\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,136,220", "endColumns": "80,83,85", "endOffsets": "131,215,301"}, "to": {"startLines": "63,325,326", "startColumns": "4,4,4", "startOffsets": "4473,26959,27043", "endColumns": "80,83,85", "endOffsets": "4549,27038,27124"}}]}]}